<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button
            v-permission="'customer:stat:export'"
            plain
            @click="exportFun"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #customerType="{ row }">
          {{ customerObj[row.customerType] }}
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getCustomerStatList,
    exportCustomerStat
  } from '@/api/statistics/customers';
  import { useMobileDevice } from '@/utils/use-mobile';

  defineOptions({ name: 'Customers' });

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    analyseProjectNumber: '',
    schemeNumber: '',
    projectName: '',
    projectName: '',
    customerType: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    },
    {
      type: 'select',
      label: '客户类型',
      prop: 'customerType',
      options: [
        { label: '申办方', value: '1' },
        { label: '委托方', value: '2' }
      ]
    },
    {
      type: 'input',
      label: '客户名称',
      prop: 'customerName'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'analyseProjectNumber',
        label: '分析项目编号',
        align: 'center'
      },
      {
        label: '试验方案编号',
        prop: 'schemeNumber',
        align: 'center'
      },
      {
        label: '项目名称',
        prop: 'projectName',
        align: 'center'
      },
      {
        prop: 'customerType',
        label: '客户类型',
        align: 'center',
        slot: 'customerType'
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center'
      },
      {
        prop: 'customerLeader',
        label: '姓名',
        align: 'center'
      },
      {
        prop: 'phone',
        label: '电话',
        align: 'center'
      },
      {
        prop: 'email',
        label: '邮箱',
        align: 'center'
      },
      {
        prop: 'address',
        label: '地址',
        align: 'center'
      }
    ];
  });

  const customerObj = {
    1: '申办方',
    2: '委托方'
  };
  const selections = ref([]);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getCustomerStatList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(where);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportCustomerStat({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
