<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <ele-tabs
        v-model="active"
        :items="[
          { name: 'package', label: '耗材包' },
          { name: 'bulkpackage', label: '散装耗材' },
          { name: 'reagent', label: '试剂' }
        ]"
      >
        <!-- 通过标签页数据的 name 作为插槽名渲染每个标签的内容, 插槽还会传递 item 参数 -->
        <template #package>
          <div style="padding: 20px 0"><PackageTable /></div>
        </template>
        <template #bulkpackage>
          <div style="padding: 20px 0"><bulkpackageTable /></div>
        </template>
        <template #reagent>
          <div style="padding: 20px 0"><reagentTable /></div>
        </template>
      </ele-tabs>
    </ele-card>
  </ele-page>
</template>
<script setup>
  import { ref } from 'vue';
  import PackageTable from './components/package-table.vue';
  import bulkpackageTable from './components/bulkpackage-table.vue';
  import reagentTable from './components/reagent-table.vue';

  defineOptions({ name: 'MaterialAllocation' });

  const active = ref('package');
  /** 表格固定高度 */
  const fixedHeight = ref(true);
</script>
