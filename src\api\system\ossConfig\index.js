import request from '@/utils/request';

/**
 * 查询对象存储配置列表
 */
export async function listOssConfig(params) {
  const res = await request.get('/resource/oss/config/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询对象存储配置详细
 */
export async function getOssConfig(ossConfigId) {
  const res = await request.get('/resource/oss/config/' + ossConfigId);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增对象存储配置
 */
export async function addOssConfig(data) {
  const res = await request.post('/resource/oss/config', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改对象存储配置
 */
export async function updateOssConfig(data) {
  const res = await request.put('/resource/oss/config', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除对象存储配置
 */
export async function delOssConfig(ossConfigId) {
  const res = await request.delete('/resource/oss/config/' + ossConfigId);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 对象存储状态修改
 */
export async function changeOssConfigStatus(ossConfigId, status, configKey) {
  const data = {
    ossConfigId,
    status,
    configKey
  };
  const res = await request.put('/resource/oss/config/changeStatus', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
