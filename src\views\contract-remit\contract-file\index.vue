<template>
  <ele-page
    :body-style="isDialog ? { padding: 0 } : null"
    :flex-table="fixedHeight"
    hide-footer
  >
    <ele-card
      :flex-table="fixedHeight"
      :body-style="isDialog ? { padding: 0 } : { paddingBottom: '4px' }"
    >
      <div v-if="!isDialog" style="display: flex; justify-content: flex-end">
        <el-button
          type="primary"
          :icon="UploadOutlined"
          class="ele-btn-icon"
          @click="openUpload()"
          v-permission="'project:contractFile:add'"
        >
          上传文件
        </el-button>
      </div>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :pagination="false"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '基础列表数据', datasource: exportSource }"
        :print-config="{ datasource: exportSource }"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="normal-table"
        :border="tableBorder"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #fileName="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="handleDownload2(row)"
          >
            {{ row.fileName }}
          </el-link>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="handleDownload(row)"
            v-permission="'project:contractFile:rename'"
          >
            下载
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row)"
            v-permission="'project:contractFile:rename'"
          >
            重命名
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'project:contractFile:remove'"
          />
          <el-link
            type="danger"
            underline="never"
            @click.stop="remove(row)"
            v-permission="'project:contractFile:remove'"
          >
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- <member-edit ref="consumableEditRef" v-model="visibleModal" /> -->
    <!-- 上传文件弹窗 -->
    <el-dialog
      title="上传文件"
      v-model="uploadVisible"
      :before-close="handleClose"
      width="680px"
      append-to-body
      destroy-on-close
    >
      <div class="upload-form">
        上传文件
        <FileUpload
          style="flex: 1; margin-left: 20px"
          :ref="itemComponentRef"
          v-model="form.sysFileIds"
          :multiple="true"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit"
            >确定上传</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="编辑合同名称"
      v-model="editVisible"
      :before-close="handleClose2"
      width="680px"
      append-to-body
      destroy-on-close
    >
      <div class="upload-form">
        合同名称
        <el-input
          style="flex: 1; margin-left: 20px"
          v-model="fileName"
          clearable
          placeholder="请输入合同名称"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose2">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit2"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, nextTick, toRefs } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { UploadOutlined } from '@/components/icons';
  import {
    getContractFileList,
    addContractFile,
    renameContractFile,
    removeContractFile,
    getContractNodeDetail
  } from '@/api/contract';
  import { useMobileDevice } from '@/utils/use-mobile';
  import FileUpload from '@/components/FileUpload/index.vue';
  import download from '@/utils/download';
  import { Base64 } from 'js-base64';
  defineOptions({ name: 'ContractFile' });

  const { mobileDevice } = useMobileDevice();

  const props = defineProps({
    isDialog: {
      type: Boolean,
      default: false
    },
    nodeId: {
      type: String
    }
  });

  /** 表格实例 */
  const tableRef = ref(null);
  const uploadVisible = ref(false);
  /** 表单数据 */
  const form = reactive({
    projectId: '',
    sysFileIds: [],
    nodeId: '',
    fileName: ''
  });
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 55,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'fileName',
        label: props.isDialog ? '节点证明材料' : '合同名称',
        slot: 'fileName'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格单选选中数据 */
  const current = ref(null);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const editVisible = ref(false);
  const fileName = ref('');
  const fileId = ref('');

  /** 表格数据源 */
  const datasource = async ({ pages, where }) => {
    if (props.isDialog) {
      const { data } = await getContractNodeDetail(props.nodeId);
      return data.fileList;
    } else {
      return getContractFileList({ ...where, ...pages });
    }
  };
  const handleClose = () => {
    uploadVisible.value = false;
  };
  const openUpload = () => {
    uploadVisible.value = true;
    form.sysFileIds = [];
  };
  /** 表格数据请求完成事件 */
  const onDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
      const ids = [45, 47, 48];
      tableRef.value?.setSelectedRowKeys?.(ids);
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  const handleDownload2 = (row) => {
    console.log('row======>handleDownload2', row);
    var url = row.sysFileList[0].url;
    window.open(
      'https://kkfile.520gcp.com/onlinePreview?url=' +
        encodeURIComponent(Base64.encode(url))
    );
  };

  const handleDownload = (row) => {
    download.oss(row.sysFileIds); // 下载文件
  };
  /** 编辑 */
  const openEdit = (row) => {
    fileName.value = row.fileName;
    fileId.value = row.fileId;
    editVisible.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        removeContractFile(row.fileId).then((res) => {
          EleMessage.success('删除成功');
          reload();
        });
      })
      .catch(() => {});
  };

  const submit = () => {
    let params = {
      nodeId: '',
      addList: []
    };
    console.log('提交参数=====>form.sysFileIds', form.sysFileIds);
    if (form.sysFileIds && form.sysFileIds.length == 0) {
      return EleMessage.error('请上传合同文件');
    }
    const sysFileIdsArray = JSON.parse(form.sysFileIds || '[]');
    if (sysFileIdsArray.length > 0) {
      params.addList = sysFileIdsArray.map((item) => {
        return {
          sysFileIds: item.ossId,
          fileName: item.name
        };
      });
    } else {
      return EleMessage.error('请上传合同文件');
    }
    console.log('提交参数=====>', params);
    addContractFile(params).then((res) => {
      EleMessage.success('上传成功');
      handleClose();
      reload();
    });
  };
  const handleClose2 = () => {
    editVisible.value = false;
  };
  const submit2 = () => {
    renameContractFile({
      fileId: fileId.value,
      fileName: fileName.value
    }).then((res) => {
      EleMessage.success('操作成功');
      handleClose2();
      reload();
    });
  };
</script>
<style lang="scss" scoped>
  .upload-form {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
</style>
