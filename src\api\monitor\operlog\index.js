import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

/**
 * 分页查询操作日志
 */
export async function pageOperlogs(params) {
  const res = await request.get('/monitor/operlog/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出操作日志
 */
export async function exportOperlogs(params) {
  const res = await request({
    url: '/monitor/operlog/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `operlog_${Date.now()}.xlsx`);
}

/**
 * 批量删除操作日志
 */
export async function removeOperlogs(ids) {
  const res = await request.delete('/monitor/operlog/' + ids.join());
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 清空操作日志
 */
export async function clearOperlogs() {
  const res = await request.delete('/monitor/operlog/clean');
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 操作痕迹列表
 */
export async function getOperlogList(data) {
  const res = await request.post('/log/ops/list', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 操作痕迹添加
 */
export async function addOperlog(data) {
  const res = await request.post('/log/ops/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 操作痕迹查询
 */
export async function queryOperlog(data) {
  const res = await request.post('/log/ops/query', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 选择项目
 */
export async function selectProject(params) {
  const res = await request.get('/project/selectList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
