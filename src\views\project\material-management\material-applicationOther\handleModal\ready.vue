<template>
  <div>
    <ele-modal
      form
      :width="1200"
      title="准备申请单耗材包"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <div v-if="readyList.length !== 0" style="height: 75vh">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全部准备
        </el-checkbox>
        <ele-card
          :body-style="{ paddingBottom: '20px' }"
          v-for="(item, index) in readyList"
          :key="index"
          bordered
          style="margin-bottom: 20px"
        >
          <el-checkbox
            v-model="item.check"
            :label="item.check"
            :value="item.check"
          >
            <div>
              <span>耗材包编号：{{ item.consumablesNumber }}</span>
              <span style="margin: 0 30px"
                >耗材包名称:{{ item.consumablesName }}</span
              >
              <span>耗材包备注：{{ item.remark }}</span>
              <span style="margin: 0 30px"
                >申请数量：{{ item.preparedQuantity }}</span
              >
              <span>已准备数量：{{ item.readyQuantity || 0 }}</span>
            </div>
          </el-checkbox>
          <div>
            <el-divider />
            <p
              >本次准备数量：<el-input-number
                v-model="item.quantity"
                @input="inputQuantity(item.inventoryAddBoList)"
            /></p>
            <ele-pro-table
              row-key="userId"
              :columns="columns"
              :datasource="item.inventoryAddBoList"
              :pagination="false"
              border
            >
              <template #quantity="{ row }">
                <span>{{ row.quantity }}*{{ item.quantity }}</span>
              </template>
              <template #inventoryId="{ row }">
                <el-select
                  clearable
                  v-model="row.inventoryId"
                  placeholder="请选择批号"
                  class="ele-fluid"
                  @focus="getBatch(row)"
                >
                  <el-option
                    v-for="(item1, index) in batchNumberList"
                    :value="item1.inventoryId"
                    :label="item1.batchNumber"
                    :key="index"
                    :disabled="disableInventory(item1, row, item)"
                    @click.stop="changeBatch(item1, row)"
                    >{{ item1.batchNumber }} 库存数量：{{
                      item1.quantity
                    }}</el-option
                  >
                </el-select>
              </template>
            </ele-pro-table>
          </div>
        </ele-card>
      </div>
      <el-empty v-else description="暂无耗材包信息" />
      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button
            v-if="readyList.length !== 0"
            type="primary"
            :loading="loading"
            @click="save"
          >
            保存
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import {
    selectPrepareByApplyId,
    materialPrepareReady
  } from '@/api/project/material-management/material-applicationOther';
  import { getBatchNumberList } from '@/api/consumables-management/inventory-query';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  const checkAll = ref(false);
  const isIndeterminate = ref(true);

  const handleCheckAllChange = (val) => {
    isIndeterminate.value = false;
    readyList.value.forEach((el) => {
      el.check = val;
    });
  };

  const columns = ref([
    {
      label: '耗材ID',
      prop: 'systemId',
      align: 'center',
      width: 120
    },
    {
      label: '耗材名称',
      prop: 'consumableName',
      align: 'center'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize',
      align: 'center'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial',
      align: 'center'
    },
    {
      label: '品牌',
      prop: 'consumableBrand',
      align: 'center'
    },
    {
      label: '耗材项数量',
      prop: 'quantity',
      align: 'center',
      slot: 'quantity'
    },
    {
      label: '批号选择',
      prop: 'inventoryId',
      slot: 'inventoryId',
      align: 'center',
      width: 240
    },
    {
      label: '有效期至',
      prop: 'endDate',
      align: 'center'
    }
  ]);
  const batchNumberList = ref([]);
  const getBatch = (row) => {
    getBatchNumberListFun(row.consumableId);
  };
  //获取批号列表
  const getBatchNumberListFun = async (consumableId) => {
    batchNumberList.value = [];
    const res = await getBatchNumberList({
      consumableId: consumableId
    });
    batchNumberList.value = res?.data || [];
  };

  const changeBatch = (item, row) => {
    row.endDate = item.endDate;
  };
  const inputQuantity = (list) => {
    if (list.length == 0) return;
    list.forEach((el) => {
      el.inventoryId = '';
      el.endDate = '';
    });
  };

  const disableInventory = (item1, row, item) => {
    return item1.quantity < Number(row.quantity) * Number(item.quantity);
  };

  const readyList = ref([]);
  const getSelectPrepareByApplyId = async () => {
    const { data } = await selectPrepareByApplyId({
      applyId: props.editData.applyId
    });
    readyList.value = data ?? [];
    readyList.value.forEach((el) => {
      el.preparedQuantity = el.quantity;
      el.inventoryAddBoList = el.detailsVoList;
      el.check = false;
    });
  };

  watch(visible, (val) => {
    if (!val) return;
    getSelectPrepareByApplyId();
  });
  watch(
    () => readyList.value,
    (val) => {
      const list = val.filter((el) => el.check);
      const checkedCount = list.length;
      checkAll.value = checkedCount === val.length;
      isIndeterminate.value = checkedCount > 0 && checkedCount < val.length;
    },
    { deep: true, immediate: true }
  );
  const loading = ref(false);
  /** 保存编辑 */
  const save = () => {
    const list = readyList.value.filter((el) => el.check);
    if (!list || list.length == 0)
      return EleMessage.warning('请选择您要准备的耗材包!');
    const detailsVoList = [];
    list.forEach((el) => {
      detailsVoList.push(...el.inventoryAddBoList);
    });
    const list1 = detailsVoList.filter((el) => !el.inventoryId);
    if (list1.length != 0)
      return EleMessage.warning('请选择您勾选耗材包的批号!');
    const params = {
      projectId: userStore.projectId,
      applyId: props.editData.applyId,
      addList: list
    };
    loading.value = true;
    materialPrepareReady(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success('操作成功！');
        cancelDialog();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e);
      });
  };
</script>
