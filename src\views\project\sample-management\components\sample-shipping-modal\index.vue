<template>
  <div>
    <ele-modal
      form
      :width="1200"
      :title="modalTitle"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <div :style="{ maxHeight: '80vh', overflow: 'auto' }">
        <el-tabs
          v-if="props.mode === 'view' && handle !== 'handle'"
          v-model="activeTab"
          type="card"
        >
          <el-tab-pane
            label="发运详情"
            name="shipping"
            v-if="rowData.forwardingNumber"
          >
            <modalBody
              ref="shippingBodyRef"
              v-model:formData="form"
              :mode="mode"
              formHandle="despatch"
              :key="`shipping-${componentKey}`"
            />
          </el-tab-pane>
          <!-- 如果有接收详情数据，显示tabs -->
          <el-tab-pane
            label="接收详情"
            v-if="rowData.status === '1'"
            name="receive"
          >
            <modalBody
              ref="receiveBodyRef"
              v-model:formData="receiveFormData"
              :mode="mode"
              formHandle="receive"
              :key="`receive-${componentKey}`"
            />
          </el-tab-pane>
          <el-tab-pane label="物流进度" v-if="logisticsInfo" name="logistics">
            <LogisticsProgress :logisticsInfo="logisticsInfo" />
          </el-tab-pane>
        </el-tabs>

        <!-- 编辑的显示 -->
        <modalBody
          v-else
          ref="editShippingBodyRef"
          v-model:formData="form"
          :mode="mode"
          :formHandle="handle"
          :key="componentKey"
        />
      </div>

      <template #footer>
        <div style="text-align: right">
          <el-button @click="onClose">{{
            mode === 'view' ? '关闭' : '取消'
          }}</el-button>
          <el-button
            v-if="mode !== 'view'"
            type="primary"
            :loading="loading"
            @click="handleSave"
          >
            保存
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, computed, watch, inject } from 'vue';
  import modalBody from './components/modal-body.vue';
  import LogisticsProgress from './components/logistics-progress.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getSampleForwardingInfo,
    getSampleReceiveInfo,
    getSampleHandleInfo,
    addSampleForwarding,
    addSampleReceive,
    addSampleHandle,
    revokeSampleReceive
  } from '@/api/project/sample-management/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  /** 当前登录用户信息 */
  const loginUser = computed(() => userStore.info ?? {});

  const visible = defineModel({ type: Boolean, default: false });

  const props = defineProps({
    /** 操作类型 add/edit/view */
    mode: {
      type: String,
      default: 'add'
    },
    /** 编辑数据id: forwardingId */
    rowData: {
      type: Object,
      default: () => null
    }
  });

  const emit = defineEmits(['save', 'close']);
  const handle = inject('handle');
  const projectInfo = inject('projectInfo');

  // Modal配置
  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const loading = ref(false);

  // 组件引用
  const shippingBodyRef = ref(null);
  const receiveBodyRef = ref(null);

  // Tab 控制
  const activeTab = ref('shipping');

  // 设置默认显示的tab
  const setDefaultActiveTab = () => {
    // 如果没有发运编号，则默认显示接收
    if (props.rowData.forwardingNumber) {
      activeTab.value = 'shipping';
    } else {
      activeTab.value = 'receive';
    }
  };

  // 强制更新key
  const componentKey = ref(0);

  // 获取当前日期（YYYY-MM-DD格式）
  const getCurrentDate = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 发运表单数据
  const { companyProjectNumber, schemeNumber, projectName } = projectInfo;
  const [form, resetFields, assignFields] = useFormData({
    basicInfo: {},
    listBoList: [{ id: 1, sampleType: null, sampleNum: 1, remark: '' }],
    detailsBoList: []
  });

  // 接收表单数据
  const receiveFormData = ref({
    basicInfo: {},
    listBoList: [{ id: 1, sampleType: null, sampleNum: null, remark: '' }],
    detailsBoList: []
  });

  // 物流进度
  let logisticsInfo = ref();

  // 计算属性
  const modalTitle = computed(() => {
    const name =
      handle === 'despatch' ? '发运' : handle === 'receive' ? '接收' : '处理';
    const titles = {
      add: `新增样本${name}`,
      edit: `确认样本${name}`,
      view: `样本${name}详情`,
      receive: `接收样本`
    };
    return titles[props.mode] || '样本发运';
  });

  const getSampleInfo = async (type, id) => {
    // 这里可以根据实际API数据结构来映射
    const ajax =
      type === 'despatch'
        ? getSampleForwardingInfo
        : type === 'receive'
          ? getSampleReceiveInfo
          : getSampleHandleInfo;
    // 根据Id获取值
    const res = await ajax(id);
    if (res.code === 200) {
      const {
        forwardingList,
        forwardingDetails,
        receiveList,
        receiveDetails,
        logisticsInfo,
        handleDetails,
        sysOssVoList,
        receiveRemark
      } = res.data;
      const listBoList = type === 'despatch' ? forwardingList : receiveList;
      const detailsBoList =
        type === 'despatch'
          ? forwardingDetails
          : type === 'receive'
            ? receiveDetails
            : handleDetails;
      // 列表接收样本
      projectInfo.sysFileIds = sysOssVoList;
      if (handle === 'receive') {
        projectInfo.receiveName = loginUser.value.nickName;
        projectInfo.receiveDate = getCurrentDate();
        projectInfo.receiveRemark = receiveRemark;
      }
      return {
        basicInfo: { ...res.data, ...projectInfo },
        listBoList: listBoList,
        detailsBoList: detailsBoList?.map((item) => {
          return {
            ...item,
            ...JSON.parse(item.content)
          };
        }),
        logisticsInfo
      };
    }
  };

  // 监听编辑数据变化
  watch(
    () => visible.value,
    async (newData) => {
      if (newData) {
        if (props.mode === 'add') {
          resetFields();
          // 确保嵌套对象也被正确重置
          const currentDate = getCurrentDate();
          const basicInfo = {
            companyProjectNumber,
            schemeNumber,
            projectName,
            forwardingName: loginUser.value.nickName,
            receiveName: loginUser.value.nickName,
            handleName: loginUser.value.nickName
          };

          // 根据不同的handle设置默认日期
          if (handle === 'despatch') {
            basicInfo['forwardingDate'] = currentDate;
          } else if (handle === 'receive') {
            basicInfo['receiveDate'] = currentDate;
          } else if (handle === 'handle') {
            basicInfo['handleDate'] = currentDate;
          }

          Object.assign(form, {
            basicInfo,
            listBoList: [{ id: 1, sampleType: null, sampleNum: 1, remark: '' }],
            detailsBoList: []
          });
          // 强制更新组件以确保数据重置生效
          componentKey.value += 1;

          // 为新增和接收模式设置默认tab
          if (handle === 'receive') {
            activeTab.value = 'receive';
          } else {
            activeTab.value = 'shipping';
          }
        } else {
          // 如果有发运编号，就调用发运详情，并显示
          if (props.rowData.forwardingId) {
            const data = await getSampleInfo(
              'despatch',
              props.rowData.forwardingId
            );
            logisticsInfo.value = data.logisticsInfo;
            assignFields(data);
          }
          // 如果是样本状态为已接收，就调用样本接收详情接口
          if (
            props.mode === 'view' &&
            props.rowData.status === '1' &&
            handle !== 'handle'
          ) {
            const data = await getSampleInfo(
              'receive',
              props.rowData.forwardingId
            );
            if (data) {
              receiveFormData.value = { ...data };
            }
          }

          // 如果是样本处理
          if (handle === 'handle') {
            const data = await getSampleInfo(handle, props.rowData.handleId);
            assignFields(data);
          }

          // 设置默认显示的tab
          setDefaultActiveTab();
        }
      }
    },
    { immediate: true, deep: true }
  );

  // 保存
  const editShippingBodyRef = ref(null);
  const handleSave = async () => {
    try {
      loading.value = true;

      // 验证当前激活的tab或单个表单
      let isValid = false;

      isValid = await editShippingBodyRef.value?.validate();

      if (!isValid) {
        return;
      }

      // 组装保存数据
      const saveData = {
        projectId: projectInfo.projectId,
        ...form.basicInfo,
        detailsBoList: form.detailsBoList
      };

      // 接收需要传递发运编号
      const forwardingId = props.rowData?.forwardingId;
      if (forwardingId) {
        saveData.forwardingId = forwardingId;
      }

      // 接收单处理
      if (handle === 'receive') {
        if (typeof saveData.sysFileIds === 'string') {
          saveData.sysFileIds = JSON.parse(saveData.sysFileIds);
        }
        saveData.sysFileIds = saveData.sysFileIds
          ?.map((item) => item.ossId)
          ?.join(',');
      }

      // 样本处理需要处理detailsBoList，只需要传inventoryId
      if (handle === 'handle') {
        saveData.detailsBoList = saveData.detailsBoList.map((item) => {
          return {
            inventoryId: item.inventoryId
          };
        });
      } else {
        saveData.listBoList = form.listBoList;
      }
      const ajax =
        handle === 'receive' && forwardingId // 确认接收
          ? revokeSampleReceive
          : handle === 'receive' // 独立接收
            ? addSampleReceive
            : handle === 'despatch' // crc发运
              ? addSampleForwarding
              : addSampleHandle; // 新增处理
      const res = await ajax(saveData);
      if (res.code === 200) {
        EleMessage.success('保存成功');
        emit('save');
        visible.value = false;
      }
    } catch (error) {
      EleMessage.error('保存失败: ' + error.message);
    } finally {
      loading.value = false;
    }
  };

  // 关闭
  const onClose = () => {
    visible.value = false;
    emit('close');
  };

  const cancelDialog = () => {
    onClose();
  };
</script>

<style scoped>
  /* 样本发运Modal样式 */
</style>
