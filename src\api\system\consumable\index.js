import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

/**
 * 查询研究耗材配置列表
 */
export async function listConsumable(params) {
  const res = await request.get('/system/consumable/list', { params });
  console.log(res.data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增研究耗材配置
 */
export async function addConsumable(data) {
  const res = await request.post('/system/consumable/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改研究耗材配置
 */
export async function editConsumable(data) {
  const res = await request.post('/system/consumable/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除研究耗材配置
 */
export async function removeConsumable(id) {
  const res = await request.delete('/system/consumable/' + id);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出研究耗材配置列表
 */
export async function exportConsumable(params) {
  const res = await request({
    url: '/system/consumable/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `consumable${Date.now()}.xlsx`);
}
