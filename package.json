{"name": "zxzy-template-plus", "version": "1.3.1", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "~1.0.1", "@ant-design/colors": "7.2.1", "@bytemd/plugin-gfm": "~1.21.0", "@bytemd/plugin-highlight": "~1.21.0", "@element-plus/icons-vue": "~2.3.1", "@hnjing/zxzy-admin-plus": "2.0.2", "@vueuse/core": "~11.0.3", "axios": "~1.7.5", "bytemd": "~1.21.0", "countup.js": "~2.8.0", "cropperjs": "~1.6.2", "crypto-js": "^4.2.0", "dayjs": "~1.11.13", "echarts": "~5.5.1", "echarts-wordcloud": "~2.1.0", "element-plus": "2.9.11", "exceljs": "~4.4.0", "file-saver": "^2.0.5", "github-markdown-css": "~5.6.1", "highlight.js": "~11.10.0", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "jsbarcode": "~3.11.6", "jsencrypt": "^3.3.2", "lodash-es": "~4.17.21", "monaco-editor": "~0.51.0", "nprogress": "~0.2.0", "pinia": "~2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "pinyin-pro": "^3.26.0", "sortablejs": "~1.15.2", "tinymce": "~5.10.9", "vite-plugin-vue-devtools": "^7.7.2", "vue": "~3.4.38", "vue-echarts": "~7.0.3", "vue-i18n": "~9.14.0", "vue-router": "~4.4.3", "vuedraggable": "~4.1.0", "xgplayer": "~3.0.20", "xgplayer-hls": "~3.0.20", "xgplayer-music": "~3.0.20"}, "devDependencies": {"@vitejs/plugin-vue": "~5.1.2", "@vue/compiler-sfc": "~3.4.38", "eslint": "~8.57.0", "eslint-config-prettier": "~9.1.0", "eslint-plugin-prettier": "~5.2.1", "eslint-plugin-vue": "~9.27.0", "postcss": "~8.4.41", "prettier": "~3.3.3", "rimraf": "~5.0.10", "sass": "~1.80.6", "unplugin-vue-components": "~0.27.4", "vite": "~5.4.2", "vite-plugin-compression": "~0.5.1", "vue-eslint-parser": "~9.4.3"}}