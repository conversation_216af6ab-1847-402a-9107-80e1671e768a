<!-- 添加和修改的表单 -->
<template>
  <pro-form
    ref="formRef"
    :model="form"
    :rules="rules"
    :items="items"
    :footer="true"
    :label-width="100"
    :grid="{ span: 24 }"
    @updateValue="setFieldValue"
    :footerStyle="{ justifyContent: 'flex-end' }"
  >
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </pro-form>
</template>

<script setup>
  import ProForm from '@/components/ProForm/index.vue';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import {
    hospitalAdd,
    hospitalEdit
  } from '@/api/project/project-configuration/institutional-information';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });

  const emit = defineEmits(['close', 'seccess']);

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    hospitalId: void 0,
    hospitalCode: '',
    hospitalName: '',
    researcher: '',
    phone: '',
    email: '',
    address: '',
    remark: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '机构代号',
      prop: 'hospitalCode',
      type: 'input',
      required: true
    },
    {
      label: '机构名称',
      prop: 'hospitalName',
      type: 'input',
      required: true
    },
    {
      label: '研究者',
      prop: 'researcher',
      type: 'input',
      required: true
    },
    {
      label: '电话',
      prop: 'phone',
      type: 'input',
      required: true
    },
    {
      label: '邮箱',
      prop: 'email',
      type: 'input',
      required: true
    },
    {
      label: '地址',
      prop: 'address',
      type: 'input',
      required: true
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'input'
    }
  ]);

  /** 表单验证规则 */
  const rules = reactive({});
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? hospitalEdit : hospitalAdd;
      const params = {
        ...form,
        projectId: userStore.projectId
      };
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('seccess');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    emit('close');
    resetFields();
  };

  watch(
    () => props.data,
    () => {
      if (props.data) {
        assignFields({
          ...props.data
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
