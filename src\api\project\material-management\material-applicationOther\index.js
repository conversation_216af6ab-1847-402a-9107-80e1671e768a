import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getMaterialApplyAdminList(params) {
  const res = await request.get('/materialApply/adminList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//详情单-物资管理-准备清单
export async function selectPrepareApplyList(params) {
  const res = await request.get('/materialPrepare/selectPrepareApplyList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//准备申请单耗材包查询
export async function selectPrepareByApplyId(params) {
  const res = await request.get('/materialPrepare/selectPrepareByApplyId', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 废弃
export async function materialPrepareDiscard(data) {
  const res = await request.post('/materialPrepare/discard', data, {
    headers: {
      isEncrypt: 'true' // 暂时注释掉
    }
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportMaterialApplyAdmin(params) {
  const res = await request({
    url: '/materialApply/exportAdminList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资准备_${Date.now()}.xlsx`);
}
// 准备保存
export async function materialPrepareReady(data) {
  const res = await request.post('/materialPrepare/prepare', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}
