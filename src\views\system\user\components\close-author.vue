<template>
  <ele-modal
    :width="1200"
    title="已授权项目"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    v-model="visible"
    align-center
  >
    <el-tabs v-model="activeTab" @tab-click="handleClickTab">
      <el-tab-pane label="内部授权" name="first" />
      <el-tab-pane label="外部授权" name="second" />
    </el-tabs>
    <EleTableSearch
      :model="form"
      :items="items"
      filterRowToMore="1"
      :show-label="true"
      :label-width="100"
      @updateValue="updateFormValue"
      @submit="onSearch"
      @reset="onSearch"
      style="margin-bottom: 10px"
    >
      <!--  <template #toolbar>
       <ele-tooltip
          content="单个新增"
          type="warning"
          placement="top"
          bg="linear-gradient( 135deg, #43CBFF 10%, #9708CC 100%)"
          arrow-bg="#7556E0"
          :offset="8"
        >
          <el-button
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button>
        </ele-tooltip>
        <el-button plain @click="exportData()"> 导出 </el-button>
      </template> -->
    </EleTableSearch>
    <ele-pro-table
      ref="tableRef"
      row-key="grantId"
      :columns="activeTab == 'first' ? columns : columns2"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    >
      <template #hospitalList="{ row }">
        {{ row.hospitalList.map((item) => item.hospitalName).join('，') }}
      </template>
      <template #projectStatus="{ row }">
        <!-- 项目状态（1代表在研中 2代表暂停 3代表结束 4代表废弃 -->
        <el-tag v-if="row.projectStatus == 1" type="success">在研中</el-tag>
        <el-tag v-else-if="row.projectStatus == 2" type="info">暂停</el-tag>
        <el-tag v-else-if="row.projectStatus == 3" type="danger">结束</el-tag>
        <el-tag v-else-if="row.projectStatus == 4" type="warning">废弃</el-tag>
      </template>
    </ele-pro-table>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
  <passwordAuth
    v-model="visiblePasswordModal"
    title="取消授权"
    @success="saveCancelAuth"
  />
</template>

<script setup>
  import { ref, nextTick, watch, reactive } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { getProjectList, removeSystemCancel } from '@/api/system/user';
  import passwordAuth from '@/components/PasswordAuth/index.vue';
  const emit = defineEmits(['done']);
  const props = defineProps({
    /** 用户 */
    data: Object
  });
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const visiblePasswordModal = ref(false);
  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    }
  ]);
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      ishow: true
    },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 60,
      align: 'center',
      ishow: true
    },

    {
      prop: 'analyseProjectNumber',
      label: '分析项目编号',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'schemeNumber',
      label: '试验方案编号',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'projectName',
      label: '项目名称',
      align: 'left',
      ishow: true
    },
    {
      prop: 'projectStatus',
      label: '项目状态',
      align: 'center',
      width: 120,
      slot: 'projectStatus',
      ishow: true
    },
    {
      prop: 'roleName',
      label: '授权项目角色',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'duties',
      label: '项目职责',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'impowerTime',
      label: '授权时间',
      align: 'left',
      width: 180,
      ishow: true
    }
  ]);
  const columns2 = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      ishow: true
    },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 60,
      align: 'center',
      ishow: true
    },

    {
      prop: 'analyseProjectNumber',
      label: '分析项目编号',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'schemeNumber',
      label: '试验方案编号',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'projectName',
      label: '项目名称',
      align: 'left',
      ishow: true
    },
    {
      prop: 'projectStatus',
      label: '项目状态',
      align: 'center',
      width: 120,
      slot: 'projectStatus',
      ishow: true
    },
    {
      prop: 'roleName',
      label: '授权项目角色',
      align: 'center',
      width: 130,
      ishow: true
    },
    {
      prop: 'hospitalList',
      label: '授权医院',
      align: 'center',
      width: 130,
      showOverflowTooltip: true,
      slot: 'hospitalList',
      ishow: true
    },
    {
      prop: 'impowerTime',
      label: '授权时间',
      align: 'left',
      width: 180,
      ishow: true
    }
  ]);
  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);
  /** 项目角色列表 */
  const roleList = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 表单数据 */
  const form = reactive({
    consumableName: '',
    consumableBrand: '',
    consumableType: ''
  });
  const activeTab = ref('first');

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };
  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 搜索事件 */
  const onSearch = (where) => {
    Object.assign(lastWhere, where);
    query(where);
  };
  const handleClickTab = (e) => {
    activeTab.value = e.props.name;
    query();
  };
  /** 保存编辑 */
  const grantIds = ref([]);
  const save = () => {
    console.log(selections.value, props.data);
    if (selections.value.length == 0) {
      EleMessage.error('请先选择项目');
      return;
    }
    grantIds.value = selections.value.map((val) => val.grantId);
    visiblePasswordModal.value = true;
    // loading.value = true;
    // removeSystemCancel(grantIds)
    //   .then(() => {
    //     loading.value = false;
    //     EleMessage.success({ message: '操作成功', plain: true });
    //     handleCancel();
    //     emit('done');
    //   })
    //   .catch((e) => {
    //     loading.value = false;
    //     EleMessage.error({ message: e.message, plain: true });
    //   });
  };

  const saveCancelAuth = (params) => {
    removeSystemCancel({ ...params, grantIds: grantIds.value }).then(() => {
      EleMessage.success('操作成功');
      visiblePasswordModal.value = false;
      handleCancel();
      emit('done');
    });
  };

  /** 查询 */
  const query = (where) => {
    getProjectList({
      userId: props.data.userId,
      isImpower: 1,
      ...where,
      userType: activeTab.value == 'first' ? 0 : 1
    })
      .then((result) => {
        datasource.value = result.data;
      })
      .catch((e) => {
        EleMessage.error({ message: e.message, plain: true });
      });
  };

  /** 监听弹窗打开 */
  watch(visible, () => {
    if (visible.value) {
      selections.value = [];
      activeTab.value = 'first';
      if (props.data) {
        query();
      }
    }
  });
</script>
