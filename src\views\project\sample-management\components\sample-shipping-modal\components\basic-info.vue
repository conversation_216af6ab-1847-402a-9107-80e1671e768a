<template>
  <ele-card
    :body-style="{ paddingBottom: '4px' }"
    header="基本信息"
    style="margin-bottom: 16px"
  >
    <template v-if="handle !== 'handle' && mode === 'view'" #extra>
      <el-tag v-show="form.status === '0'" type="warning"> 待接收 </el-tag>
      <el-tag v-show="form.status === '1'" type="success"> 已接收 </el-tag>
      <el-tag v-show="form.status === '2'" type="danger"> 已撤销 </el-tag>
    </template>
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :footer="false"
      :label-width="120"
      :grid="{ span: 8 }"
      :disabled="mode === 'view'"
      @updateValue="setFieldValue"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive, computed, watch, inject, onMounted } from 'vue';
  import ProForm from '@/components/ProForm/index.vue';
  import { useDictData } from '@/utils/use-dict-data';
  import { selectHospitalList } from '@/api/project/material-management/material-application';

  const modelValue = defineModel({
    type: Object,
    default: () => ({})
  });

  const props = defineProps({
    mode: {
      type: String,
      default: 'add' // add/edit/view/receive
    },
    // 表单处理类型
    formHandle: {
      type: String,
      default: 'despatch'
    }
  });

  const handle = inject('handle');
  const projectInfo = inject('projectInfo');

  const [logisticsCompany] = useDictData(['logistics_company']);
  const myLogisticsCompany = computed(() =>
    logisticsCompany.value
      ?.filter((item) => item.remark === '冷链')
      ?.map((item) => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
  );

  const formRef = ref(null);

  // 获取当前日期（YYYY-MM-DD格式）
  const getCurrentDate = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 表单数据
  const form = reactive({});

  // 表单配置
  const items = computed(() => {
    const formItems = [
      {
        prop: 'companyProjectNumber',
        label: '公司项目编号',
        type: 'text',
        props: {
          disabled: true
        }
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        type: 'text',
        props: {
          disabled: true
        }
      },
      {
        prop: 'projectName',
        label: '项目名称',
        type: 'ellipsis',
        props: {
          disabled: true
        }
      }
    ];
    // 样本发运
    if (props.formHandle === 'despatch') {
      formItems.push(
        {
          prop: 'hospitalId',
          label: '发运机构',
          type: 'select',
          props: {
            disabled: hospitalList.value.length === 1
          },
          options: hospitalList.value,
          required: true
        },
        {
          prop: 'hospitalCode',
          label: '机构代号',
          type: 'input',
          props: {
            disabled: true
          }
        },
        {
          prop: 'forwardingName',
          label: '发运人',
          type: 'input',
          props: {
            disabled: handle === 'receive',
            maxlength: 10,
            showWordLimit: true
          },
          required: true
        },
        {
          prop: 'forwardingDate',
          label: '发运日期',
          type: 'date',
          props: {
            disabled: handle === 'receive'
          },
          required: true
        },
        {
          type: 'select',
          label: '运输单位',
          prop: 'logisticsCompany',
          options: myLogisticsCompany.value,
          props: {
            disabled: handle === 'receive'
          },
          required: true
        },
        {
          prop: 'bookingNote',
          label: '运输单号',
          type: 'input',
          props: {
            disabled: handle === 'receive',
            maxlength: 20,
            showWordLimit: true
          },
          required: true
        }
      );
    }
    if (props.formHandle === 'receive') {
      formItems.push(
        {
          prop: 'hospitalId',
          label: '接收机构',
          type: 'select',
          options: hospitalList.value,
          props: {
            disabled: hospitalList.value.length === 1
          },
          required: true
        },
        {
          prop: 'receiveName',
          label: '接收人',
          type: 'input',
          required: true,
          props: {
            maxlength: 10,
            showWordLimit: true
          }
        },
        {
          prop: 'receiveDate',
          label: '接收日期',
          type: 'date',
          required: true
        },
        {
          label: '样本接收单',
          prop: 'sysFileIds',
          type: 'fileUpload',
          props: {
            disabled: props.mode === 'view'
          },
          required: true
        },
        {
          prop: 'receiveRemark',
          label: '接收备注',
          type: 'textarea',
          props: {
            placeholder: '请输入接收备注',
            rows: 2,
            maxlength: 50,
            showWordLimit: true
          }
        }
      );
    }

    // 样本发运和接收
    if (handle === 'despatch') {
      formItems.push({
        prop: 'remark',
        label: '备注',
        type: 'textarea',
        props: {
          placeholder: '请输入备注',
          rows: 2,
          maxlength: 50,
          showWordLimit: true
        }
      });
    }

    // 样本处理
    if (handle === 'handle') {
      formItems.push(
        {
          type: 'select',
          label: '处理类型',
          prop: 'handleType',
          options: [
            {
              label: '转移',
              value: '1'
            },
            {
              label: '销毁',
              value: '2'
            }
          ],
          required: true
        },
        {
          prop: 'transferPosition',
          label: '样本位置',
          type: 'input',
          required: true,
          vIf: form.handleType === '1'
        },
        {
          prop: 'handleName',
          label: '处理人',
          type: 'input',
          required: true
        },
        {
          prop: 'handleDate',
          label: '处理日期',
          type: 'date',
          required: true
        },
        {
          prop: 'handleReason',
          label: '处理原因',
          type: 'textarea',
          props: {
            rows: 2
          },
          required: true
        }
      );
    }
    return formItems;
  });

  // 监听外部数据变化
  watch(
    () => modelValue.value,
    (newValue) => {
      console.log('basic-info 接收到外部数据变化:', newValue);
      if (newValue) {
        Object.assign(form, newValue);

        // 为新增模式设置默认日期
        if (props.mode === 'add') {
          const currentDate = getCurrentDate();

          // 如果是发运模式且发运日期为空，设置默认值
          if (handle === 'despatch' && !form.forwardingDate) {
            form.forwardingDate = currentDate;
          }

          // 如果是接收模式且接收日期为空，设置默认值
          if (
            handle === 'receive' &&
            props.formHandle === 'receive' &&
            !form.receiveDate
          ) {
            form.receiveDate = currentDate;
          }
        }
      }
    },
    { immediate: true, deep: true }
  );

  // 监听表单数据变化
  watch(
    form,
    (newValue) => {
      console.log('basic-info 表单数据变化，更新modelValue:', newValue);
      modelValue.value = { ...newValue };
    },
    { deep: true }
  );

  // 设置字段值
  const setFieldValue = (prop, value) => {
    form[prop] = value;
    if (prop === 'hospitalId') {
      const current = hospitalList.value.filter((item) => {
        return item.hospitalId === value;
      });
      form.hospitalCode = current[0].hospitalCode;
    }
  };

  // 验证表单
  const validate = async () => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('基本信息验证失败:', error);
      return false;
    }
  };

  const hospitalList = ref([]);
  // 获取机构下拉列表
  const getSelectHospitalList = async () => {
    const res = await selectHospitalList({
      projectId: projectInfo.projectId
    });
    if (res.data.length !== 0) {
      hospitalList.value = res.data.map((el) => {
        return {
          label: el.hospitalName,
          value: el.hospitalId,
          ...el
        };
      });

      // 如果只有一个机构且是新增模式，自动选中
      if (
        hospitalList.value.length === 1 &&
        props.mode === 'add' &&
        !form.hospitalId
      ) {
        form.hospitalId = hospitalList.value[0].value;
        form.hospitalCode = hospitalList.value[0].hospitalCode;
        console.log(
          '自动选中唯一机构:',
          hospitalList.value[0].label,
          hospitalList.value[0].value
        );
      }
    }
  };
  onMounted(() => {
    getSelectHospitalList();
  });

  // 暴露方法给父组件
  defineExpose({
    validate
  });
</script>

<style scoped>
  /* 自定义样式 */
</style>
