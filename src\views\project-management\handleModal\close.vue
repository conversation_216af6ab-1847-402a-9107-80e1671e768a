<template>
  <div>
    <ele-modal
      form
      :width="550"
      title="关闭项目"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { projectClose } from '@/api/project-management';
  import ProForm from '@/components/ProForm/index.vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    companyProjectNumber: '',
    schemeNumber: '',
    projectName: '',
    projectAttribute: '',
    approvalDate: '',
    endDate: '',
    projectId: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '公司项目编号',
      prop: 'companyProjectNumber',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '试验方案编号',
      prop: 'schemeNumber',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '项目名称',
      prop: 'projectName',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      type: 'dictSelect',
      label: '项目属性',
      prop: 'projectAttribute',
      props: { code: 'project_attribute', disabled: true }
    },
    {
      label: '立项日期',
      prop: 'approvalDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD', disabled: true }
    },
    {
      label: '项目结束日期',
      prop: 'endDate',
      type: 'date',
      props: {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date) => {
          return date < new Date(props.editData.approvalDate);
        }
      },
      required: true
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      projectClose(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  watch(visible, (val) => {
    if (val) {
      assignFields({
        ...props.editData
      });
    }
  });
</script>
