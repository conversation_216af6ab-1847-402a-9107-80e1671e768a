<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '100px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
      header="基础信息"
    >
      <el-row>
        <el-col :span="8"
          ><p>耗材ID：{{ info.systemId }}</p></el-col
        >
        <el-col :span="8"
          ><p>耗材名称：{{ info.consumableName }}</p></el-col
        >
        <el-col :span="8"
          ><p>规格尺寸：{{ info.consumableSize }}</p></el-col
        >
        <el-col :span="8"
          ><p>颜色材质：{{ info.consumableMaterial }}</p></el-col
        >
        <el-col :span="8"
          ><p>品牌：{{ info.consumableBrand }}</p></el-col
        >
        <el-col :span="8"
          ><p>包装单位：{{ info.consumablePackagingUnit }}</p></el-col
        >
        <el-col :span="8"
          ><p>包装规格：{{ info.consumablePackagingSpecification }}</p></el-col
        >
        <el-col :span="8"
          ><p>库存提醒阈值：{{ info.consumableThreshold }}</p></el-col
        >
        <el-col :span="8"
          ><p>采购货期/天数：{{ info.consumableDeliveryDay }}</p></el-col
        >
        <el-col :span="24"
          ><p>备注：{{ info.remark }}</p></el-col
        >
      </el-row>
    </ele-card>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '480px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <template #header>
        <div class="card-header">
          <span>批号明细</span>
          <span style="margin-left: 200px"
            >总库存：{{ info.inventoryNumber }}</span
          >
        </div>
      </template>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :toolbar="{ theme: 'default' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'inventory:inventory:add'"
            type="primary"
            underline="never"
            @click.stop="inventoryClick(row)"
          >
            库存盘点
          </el-link>
        </template>
      </ele-pro-table>
      <handleModal
        v-model="visibleModal"
        :editData="editData"
        @success="getDetails"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, computed, watch, reactive, onMounted } from 'vue';
  import handleModal from './handleModal/index.vue';
  import { getInventoryDetails } from '@/api/consumables-management/inventory-query';
  import { useRoute } from 'vue-router';

  const { query } = useRoute();
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 80,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'batchNumber',
        label: '批号',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '有效期至',
        align: 'center'
      },
      {
        prop: 'quantity',
        label: '库存数量',
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ref([]);
  const info = reactive({
    systemId: '',
    consumableName: '',
    consumableSize: '',
    consumableMaterial: '',
    consumableBrand: '',
    consumablePackagingUnit: '',
    consumablePackagingSpecification: '',
    consumableThreshold: '',
    consumableDeliveryDay: '',
    remark: '',
    inventoryNumber: '',
    consumableType: '',
    consumableId: ''
  });
  const getDetails = async () => {
    const { data } = await getInventoryDetails(id.value);
    for (let key in info) {
      info[key] = data[key];
    }
    datasource.value = data.batchList ?? [];
  };
  const visibleModal = ref(false);
  const editData = ref({});
  const inventoryClick = (row) => {
    const detailsInfo = {
      ...info,
      ...row
    };
    editData.value = detailsInfo || null;
    visibleModal.value = true;
  };
  const id = ref('');
  onMounted(() => {
    id.value = query.id;
    getDetails();
  });
</script>
