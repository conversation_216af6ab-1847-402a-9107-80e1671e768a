<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1100"
    v-model="visible"
    title="编辑冷链信息"
    @open="handleOpen"
  >
    <div class="title">基础信息</div>
    <el-row>
      <el-col :span="8"
        ><p>分析项目编号：{{ editData.analyseProjectNumber }}</p></el-col
      >
      <el-col :span="8"
        ><p>公司项目编号：{{ editData.companyProjectNumber }}</p></el-col
      >
      <el-col :span="8"
        ><p>试验方案编号：{{ editData.schemeNumber }}</p></el-col
      >
      <el-col :span="8"
        ><p>运输单号：{{ editData.bookingNote }}</p></el-col
      >
      <el-col :span="8"
        ><p>运输单位：{{ editData.logisticsCompany }}</p></el-col
      >
      <el-col :span="8"
        ><p>下单人：{{ editData.forwardingName }}</p></el-col
      >
      <el-col :span="8"
        ><p>发件日期：{{ editData.forwardingDate }}</p></el-col
      >
      <el-col :span="8"
        ><p>发件单位：{{ editData.hospitalName }}</p></el-col
      >
      <el-col :span="8"
        ><p>订单备注：{{ editData.remark }}</p></el-col
      >
    </el-row>
    <div class="title" style="margin-bottom: 20px">其他信息</div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="88px"
      @submit.prevent=""
    >
      <el-row>
        <el-col :span="8"
          ><el-form-item label="箱数" prop="cases">
            <el-input
              v-model="form.cases"
              placeholder="请输入箱数"
              type="number"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="规格尺寸" prop="specSize">
            <el-input
              v-model="form.specSize"
              placeholder="请输入规格尺寸"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="运输条件" prop="transportationConditions">
            <el-input
              v-model="form.transportationConditions"
              placeholder="请输入运输条件"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="费用" prop="expense">
            <el-input
              v-model="form.expense"
              placeholder="请输入费用"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="付款日期" prop="paymentDate">
            <el-date-picker
              v-model="form.paymentDate"
              type="date"
              placeholder="请选择付款日期"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="开票日期" prop="invoicingDate">
            <el-date-picker
              v-model="form.invoicingDate"
              type="date"
              placeholder="请选择开票日期"
              style="width: 220px"
            /> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="费用备注" prop="expenseRemarks">
            <el-input
              :rows="4"
              type="textarea"
              v-model="form.expenseRemarks"
              placeholder="请输入费用备注"
            /> </el-form-item
        ></el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { addPost, updatePost } from '@/api/system/post';
  import { editLenglianStat } from '@/api/statistics/coldchain';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  const editData = ref(props.data);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    cases: '',
    specSize: '',
    transportationConditions: '',
    expense: '',
    paymentDate: '',
    invoicingDate: '',
    expenseRemarks: '',
    forwardingType: '',
    forwardingId: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    cases: [
      {
        required: true,
        message: '请输入箱数',
        type: 'string',
        trigger: 'blur'
      }
    ],
    specSize: [
      {
        required: true,
        message: '请输入规格尺寸',
        type: 'string',
        trigger: 'blur'
      }
    ],
    transportationConditions: [
      {
        required: true,
        message: '请输入运输条件',
        type: 'string',
        trigger: 'blur'
      }
    ],
    expense: [
      {
        required: true,
        message: '请输入费用',
        type: 'string',
        trigger: 'blur'
      }
    ],
    paymentDate: [
      {
        required: true,
        message: '请选择付款日期',
        type: 'date',
        trigger: 'blur'
      }
    ],
    invoicingDate: [
      {
        required: true,
        message: '请选择开票日期',
        type: 'date',
        trigger: 'blur'
      }
    ],
    expenseRemarks: [
      {
        required: true,
        message: '请输入费用备注',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      editLenglianStat(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          handleCancel();
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    console.log('props.data====>', props.data);
    editData.value = props.data;
    if (props.data) {
      assignFields(props.data);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
<style lang="scss" scoped>
  .title {
    font-weight: 900;
    font-size: 16px;
  }
</style>
