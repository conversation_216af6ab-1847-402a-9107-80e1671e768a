// import NProgress from 'nprogress';
// import 'nprogress/nprogress.css';
const Load = {
  index: 0
};

// 按钮loading管理器
const ButtonLoadingManager = {
  // 存储按钮的loading状态 Map<Element, {count: number, originalContent: string, requests: Set}>
  buttonMap: new Map(),
  // 存储请求URL到按钮的映射 Map<requestKey, Element>
  requestButtonMap: new Map(),

  // 生成按钮唯一标识
  getButtonKey(element) {
    if (!element._loadingId) {
      element._loadingId =
        'btn_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }
    return element._loadingId;
  },

  // 创建loading图标
  createLoadingIcon() {
    const loadingIcon = document.createElement('span');
    loadingIcon.className = 'loading-icon';
    loadingIcon.style.marginRight = '4px';
    loadingIcon.style.display = 'inline-block';
    loadingIcon.style.width = '8px';
    loadingIcon.style.height = '8px';
    loadingIcon.style.border = '1px solid currentColor';
    loadingIcon.style.borderRadius = '50%';
    loadingIcon.style.borderTopColor = 'transparent';
    loadingIcon.style.animation = 'rotating 1s linear infinite';
    return loadingIcon;
  },

  // 生成请求唯一标识
  generateRequestKey(config) {
    const method = config.method || 'get';
    const url = config.url || '';
    return `${method}:${url}`;
  },

  // 添加loading状态
  addLoading(element, requestKey) {
    this.getButtonKey(element); // 确保元素有唯一标识
    let buttonInfo = this.buttonMap.get(element);

    if (!buttonInfo) {
      // 首次添加loading
      buttonInfo = {
        count: 1,
        originalContent: element.innerHTML,
        originalDisabled: element.disabled,
        requests: new Set()
      };
      this.buttonMap.set(element, buttonInfo);

      // 添加loading图标和禁用状态
      const loadingIcon = this.createLoadingIcon();
      element.insertBefore(loadingIcon, element.firstChild);
      element.classList.add('is-disabled');
      element.disabled = true;
    } else {
      // 增加计数
      buttonInfo.count++;
    }

    // 记录请求和按钮的关联
    if (requestKey) {
      buttonInfo.requests.add(requestKey);
      this.requestButtonMap.set(requestKey, element);
    }
  },

  // 移除loading状态
  removeLoading(element, requestKey) {
    const buttonInfo = this.buttonMap.get(element);
    if (!buttonInfo) return;

    // 如果指定了requestKey，从请求集合中移除
    if (requestKey) {
      buttonInfo.requests.delete(requestKey);
      this.requestButtonMap.delete(requestKey);
    }

    buttonInfo.count--;

    if (buttonInfo.count <= 0) {
      // 移除loading图标和禁用状态
      const loadingIcon = element.querySelector('.loading-icon');
      if (loadingIcon) {
        loadingIcon.remove();
      }
      element.classList.remove('is-disabled');
      element.disabled = buttonInfo.originalDisabled;

      // 清理所有相关的请求映射
      buttonInfo.requests.forEach((reqKey) => {
        this.requestButtonMap.delete(reqKey);
      });

      // 清理映射
      this.buttonMap.delete(element);
    }
  },

  // 根据请求key移除loading状态
  removeLoadingByRequest(requestKey) {
    const element = this.requestButtonMap.get(requestKey);
    if (element) {
      this.removeLoading(element, requestKey);
    }
  },

  // 清理所有按钮loading状态
  clearAll() {
    this.buttonMap.forEach((buttonInfo, element) => {
      const loadingIcon = element.querySelector('.loading-icon');
      if (loadingIcon) {
        loadingIcon.remove();
      }
      element.classList.remove('is-disabled');
      element.disabled = buttonInfo.originalDisabled;
    });
    this.buttonMap.clear();
    this.requestButtonMap.clear();
  }
};

export default {
  loading: {
    open(config) {
      Load.index++;
      // NProgress.start();
      // 按钮置灰和loading图标
      if (
        window.event &&
        window.event.type === 'click' &&
        window.event.currentTarget &&
        window.event.currentTarget.classList.contains('el-button')
      ) {
        const button = window.event.currentTarget;
        const requestKey = config
          ? ButtonLoadingManager.generateRequestKey(config)
          : null;

        ButtonLoadingManager.addLoading(button, requestKey);
      }
    },
    close(config) {
      Load.index--;
      // if (Load.index <= 0) {
      //   NProgress.done();
      // }
      // 移除按钮loading状态
      if (config) {
        // 如果有config，根据请求key移除对应的按钮loading
        const requestKey = ButtonLoadingManager.generateRequestKey(config);
        ButtonLoadingManager.removeLoadingByRequest(requestKey);
      } else {
        // 兼容旧的调用方式，尝试从DOM中查找最后一个loading按钮
        const allButtons = document.querySelectorAll('.el-button.is-disabled');
        if (allButtons.length > 0) {
          const lastButton = Array.from(allButtons).pop();
          ButtonLoadingManager.removeLoading(lastButton);
        }
      }
    },
    clear() {
      Load.index = 0;
      // NProgress.stop();
      // 清理所有按钮loading状态
      ButtonLoadingManager.clearAll();
    }
  }
};
