<template>
  <div>
    <ele-modal
      form
      :width="500"
      title="打印耗材包条码"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <div class="print-bar-code">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        <span>已选择{{ modelData.length }}个耗材包条码</span>
      </div>
      <el-divider style="margin: 0; opacity: 0.6" />
      <div v-if="data.length != 0" class="box">
        <el-checkbox-group v-model="modelData" @change="handleCheckedCitiesChange">
          <div v-for="item in data" :key="item.packageCode">
            <el-checkbox :value="item" :label="item.packageCode" />
          </div>
        </el-checkbox-group>
      </div>
      <el-empty v-else description="暂无数据" />

      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button v-if="data.length != 0" type="primary" @click="save">
            确定打印
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { materialPrepareApplyPrint } from '@/api/project/material-management/materialsdetailed-list';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import type { CheckboxValueType } from 'element-plus'

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    idList: {
      type: Array,
      default: () => []
    }
  });
  const checkAll = ref(false);
  const isIndeterminate = ref(false);
  const emit = defineEmits(['surePrint']);
  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    data.value = [];
    modelData.value = [];
  };

  const handleCheckAllChange = (val: CheckboxValueType) => {
    modelData.value = val ? data.value : []
    isIndeterminate.value = false
  }

  const handleCheckedCitiesChange = (value: CheckboxValueType[]) => {
    const checkedCount = value.length
    checkAll.value = checkedCount === data.value.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < data.value.length
  }

  const modelData = ref([]);
  const data = ref([]);
  const getPrintData = async () => {
    try {
      const res = await materialPrepareApplyPrint({
        prepareDetailsIdList: props.idList
      });
      data.value = res.data ?? [];
      modelData.value = res.data ?? [];
      checkAll.value = true;
      isIndeterminate.value = false;
    } catch (err) {
      EleMessage.error(err);
    }
  };
  const save = () => {
    emit('surePrint', modelData.value);
  };
  watch(visible, (val) => {
    if (!val) return;
    getPrintData();
  });
</script>
<style lang="scss" scoped>
  .box {
    max-height: 400px;
    overflow: auto;
  }
  .print-bar-code {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
