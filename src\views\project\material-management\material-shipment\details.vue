<template>
  <ele-page :flex-table="false">
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <ele-tabs
        v-model="active"
        :items="[
          { name: 'shipment', label: '发运详情' },
          { name: 'logistics', label: '物流进度' }
        ]"
      >
        <template #shipment>
          <ele-card
            :flex-table="true"
            :body-style="{ padding: '20px 0 4px 0' }"
            :style="{
              minHeight: '100px',
              marginBottom: '10px'
            }"
          >
            <shipmentBasicInfo :data="basicInfo" />
            <receiveInformation
              v-if="basicInfo.receiveTime"
              :data="basicInfo"
            />
          </ele-card>
          <ele-card
            :flex-table="true"
            :body-style="{ padding: '20px 0 4px 0' }"
            :style="{
              minHeight: '380px',
              marginBottom: '10px'
            }"
          >
            <h3>发运明细</h3>
            <ele-segmented
              size="large"
              v-model="active1"
              :items="applyItem"
              style="margin-bottom: 20px"
            />
            <shipmentPackage :data="forwardingConsumablesDetailsVoList" />
            <shipmentBulkpackage
              :data="forwardingBulkConsumablesDetailsVoList"
            />
            <shipmentReagent :data="forwardingBulkReagentDetailsVoList" />
          </ele-card>
        </template>
        <template #logistics>
          <el-timeline
            style="max-width: 600px; height: 78vh; padding-top: 30px"
          >
            <el-timeline-item
              v-for="(activity, index) in logisticsInfoList"
              :key="index"
              :type="index === '0' ? 'primary' : ''"
              :timestamp="activity.time"
            >
              {{ activity.msg }}
            </el-timeline-item>
          </el-timeline>
        </template>
      </ele-tabs>
      <template #footer>
        <div style="text-align: right">
          <el-button v-if="buttonType" type="primary" @click="actionFun">{{
            urlObj[buttonType].name
          }}</el-button>
        </div>
      </template>
    </ele-card>
    <handleModal
      v-model="visibleModal"
      :id="id"
      :data="basicInfo"
      @success="success"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, watch, unref } from 'vue';
  import shipmentBasicInfo from '../components/shipmentBasicInfo.vue';
  import receiveInformation from '../components/receiveInformation.vue';
  import shipmentPackage from '../components/shipmentPackage.vue';
  import shipmentBulkpackage from '../components/shipmentBulkpackage.vue';
  import shipmentReagent from '../components/shipmentReagent.vue';
  import handleModal from '../material-receiving/handleModal/index.vue';
  import { getMaterialForwardingDetails } from '@/api/project/material-management/material-shipment';

  import { useRouter } from 'vue-router';
  const { currentRoute, push } = useRouter();

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const active = ref('shipment');
  const active1 = ref(1);

  const basicInfo = reactive({});
  const applyItem = ref([]);
  let logisticsInfoList = reactive([]);
  const getDetails = async (id) => {
    const { data } = await getMaterialForwardingDetails(id);
    Object.assign(basicInfo, data);

    if (basicInfo.logisticsInfo) {
      logisticsInfoList = Object.assign(
        {},
        JSON.parse(basicInfo.logisticsInfo)
      );
    }
    applyItem.value = data?.forwardingApplyDetailsVoList || [];
    applyItem.value = data?.forwardingApplyDetailsVoList.map((el) => {
      return { ...el, label: el.applyNumber, value: el.applyNumber };
    });
    active1.value = applyItem.value[0].value;
    getList(applyItem.value[0]);
  };

  const buttonType = ref('');
  const id = ref('');
  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      buttonType.value = query?.type || '';
      id.value = query?.id;
      getDetails(query.id);
    },
    { immediate: true }
  );

  watch(active1, (val) => {
    const list = applyItem.value.filter((el) => el.applyNumber == val);
    getList(list[0]);
  });
  const forwardingConsumablesDetailsVoList = ref([]);
  const forwardingBulkConsumablesDetailsVoList = ref([]);
  const forwardingBulkReagentDetailsVoList = ref([]);
  const getList = (info) => {
    forwardingConsumablesDetailsVoList.value =
      info.forwardingConsumablesDetailsVoList ?? [];
    forwardingBulkConsumablesDetailsVoList.value =
      info.forwardingBulkConsumablesDetailsVoList ?? [];
    forwardingBulkReagentDetailsVoList.value =
      info.forwardingBulkReagentDetailsVoList ?? [];
  };

  const visibleModal = ref(false);
  const actionFun = () => {
    if (buttonType.value == 'receive') {
      visibleModal.value = true;
    }
  };

  const urlObj = {
    receive: {
      path: '/project/material-management/material-receiving',
      name: '接收'
    }
  };

  const success = () => {
    console.log(urlObj[buttonType.value].path);
    push({
      path: urlObj[buttonType.value].path
    });
  };
</script>
