<template>
  <div class="top-menu">
    <el-radio-group
      @change="changeSampleGroup"
      v-model="sampleGroup"
      style="margin: 0 0 30px 0"
    >
      <el-radio-button
        v-for="item in sampleGroupList"
        :key="item.groupId"
        :value="item.groupId"
        :label="item.groupName"
      />
    </el-radio-group>
    <div :class="{ 'group-nbsp': sampleGroupList?.length }">
      <el-button
        type="primary"
        plain
        :icon="PlusOutlined"
        @click="addGroup()"
        v-permission="'sample:trialGroup:add'"
      >
        新增组别
      </el-button>
      <el-button
        type="warning"
        plain
        :icon="EditOutlined"
        v-show="sampleGroup && currentSampleGroup?.status !== '1'"
        @click="updateGroup()"
        v-permission="'sample:trialGroup:edit'"
      >
        编辑组别
      </el-button>
      <el-button
        type="danger"
        plain
        v-show="sampleGroup && currentSampleGroup?.status !== '1'"
        :icon="DeleteOutlined"
        @click="deleteGroup()"
        v-permission="'sample:trialGroup:remove'"
      >
        删除组别
      </el-button>
      <el-button
        type="info"
        plain
        v-show="addSubjectNumberVisible"
        :icon="PlusOutlined"
        @click="addSubjectNum()"
        v-permission="'sample:trialGroup:addNum'"
      >
        新增受试者数量
      </el-button>
    </div>
  </div>
  <GroupDialog v-model="visibleModal" :editData="editData" @refresh="init()" />
</template>

<script setup>
  import { ref, inject, onMounted, computed, watch } from 'vue';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import GroupDialog from '../handleModal/sample-group-dialog.vue';
  import {
    getSampleGroupList,
    deleteSampleGroup
  } from '@/api/project/project-configuration/experiment-design';
  import {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined
  } from '@/components/icons';

  const projectInfo = inject('projectInfo');

  // 使用computed来响应projectId的变化，提供默认值
  const projectId = computed(() => projectInfo?.projectId);

  const emit = defineEmits(['changeSampleGroup']);
  const sampleGroup = ref(null);
  const sampleGroupList = ref([]);
  const visibleModal = ref(false);
  const editData = ref({});

  // 获取组别列表 /getSampleGroupList/{projectId}
  const getGroupList = async () => {
    if (!projectId.value) return;
    const res = await getSampleGroupList(projectId.value);
    if (res.code === 200) {
      sampleGroupList.value = res.data;
    }
  };

  // 选择组别
  const currentSampleGroup = ref(null);
  const changeSampleGroup = (val) => {
    currentSampleGroup.value = sampleGroupList.value.find(
      (i) => i.groupId === val
    );
    console.log(111, currentSampleGroup.value);
    emit('changeSampleGroup', currentSampleGroup.value);
  };

  // 新增受试者数量按钮控制
  const addSubjectNumberVisible = computed(() => {
    return (
      sampleGroup.value &&
      currentSampleGroup.value?.status === '1' &&
      projectInfo?.designType !== '3'
    );
  });

  // 新增组别
  const addGroup = async () => {
    console.log('新增组别');
    editData.value = null;
    visibleModal.value = true;
  };

  // 编辑组别;
  const updateGroup = () => {
    // 通过组别id获取组别信息 /getTrailGroup/{id}
    editData.value = currentSampleGroup.value;
    visibleModal.value = true;
  };

  // 新增受试者数量
  const addSubjectNum = () => {
    editData.value = { handleType: 3, groupId: sampleGroup.value };
    visibleModal.value = true;
  };

  // 删除组别
  const deleteGroup = () => {
    console.log('删除组别');
    const group = sampleGroupList.value.find(
      (i) => i.groupId === sampleGroup.value
    );
    ElMessageBox.confirm(`确定要删除【${group.groupName}】吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        // 删除组别 /removeSamplePoint/{ids}
        deleteSampleGroup(group.groupId).then((res) => {
          if (res.code === 200) {
            EleMessage.success({ message: '删除成功', plain: true });
            init();
          }
        });
      })
      .catch(() => {});
  };

  const init = async () => {
    await getGroupList();
    // 默认选择第一个组别
    sampleGroup.value = sampleGroupList.value?.[0]?.groupId;
    changeSampleGroup(sampleGroup.value);
  };

  // 监听projectId变化，当数据加载完成后初始化
  watch(
    () => projectId.value,
    (newProjectId) => {
      if (newProjectId) {
        init();
      }
    },
    { immediate: true }
  );

  onMounted(async () => {
    // 如果projectId已经存在且不在加载中，直接初始化
    if (projectId.value) {
      init();
    }
  });
</script>
<style scoped lang="less">
  .top-menu {
    height: 40px;
    display: flex;
    justify-content: flex-start;
  }
  .group-nbsp {
    margin-left: 20px;
  }
</style>
