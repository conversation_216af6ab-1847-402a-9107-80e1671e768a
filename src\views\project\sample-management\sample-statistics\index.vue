<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            @click="handleExportSampleLabel()"
            v-permission="'sample:sampleInventory:export'"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="systemId"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="sampleLabelList"
        :sticky="true"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, h } from 'vue';
  import { useDictData } from '@/utils/use-dict-data';
  import {
    getSampleInventoryList,
    exportSampleInventory
  } from '@/api/project/sample-management/index.js';
  import { useUserStore } from '@/store/modules/user';
  import { ElTag } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  defineOptions({ name: 'SampleStatistics' });
  /**
   * 获取项目详情
   * 项目ID    projectId
   * 设计类型  designType
   * 1=研究周期+采样时间
   * 2=周期+年/月/周/日+采样时间
   * 3=访视+采样点
   */
  const userStore = useUserStore();
  const projectInfo = userStore.projectInfo;
  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');

  /** 性别字典数据 */
  const [sampleTypeDicts] = useDictData(['sample_type']);
  const options = reactive({
    // 样本状态
    sampleStatus: [
      { label: '在库', value: '0' },
      { label: '转移', value: '1' },
      { label: '销毁', value: '2' }
    ]
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({});

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = computed(() => {
    let myItems = [
      {
        type: 'input',
        label: '样本ID',
        prop: 'systemId',
        props: {
          labelWidth: '120px'
        }
      },
      {
        type: 'select',
        label: '样本状态',
        prop: 'status',
        options: options.sampleStatus
      },
      {
        type: 'input',
        label: '机构名称',
        prop: 'hospitalName'
      },
      {
        type: 'input',
        label: '样本位置',
        prop: 'position'
      },
      {
        type: 'date',
        label: '接收日期',
        prop: 'createDate'
      }
    ];
    return myItems;
  });

  /** 表格列配置 */
  const columns = computed(() => {
    let baseColumns = [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'systemId',
        label: '样本ID',
        minWidth: 120
      },
      {
        prop: 'status',
        label: '样本状态',
        width: 100,
        formatter: ({ status }) => {
          return h(
            ElTag,
            {
              type:
                status === '0'
                  ? 'success'
                  : status === '1'
                    ? 'warning'
                    : 'danger'
            },
            // 状态（0代表在库 1代表转移 2代表销毁）
            () => (status === '0' ? '在库' : status === '1' ? '转移' : '销毁')
          );
        }
      },
      {
        prop: 'groupName',
        label: '试验组别',
        width: 100
      }
    ];
    if (designType.value === '1' || designType.value === '2') {
      baseColumns.push({
        label: '周期',
        prop: 'cycleName',
        minWidth: 80
      });
    }
    /** 类型为2 的列*/
    if (designType.value === '2') {
      baseColumns.push(
        {
          prop: 'samplePointYear',
          label: '年',
          minWidth: 80
        },
        {
          prop: 'samplePointMonth',
          label: '月',
          minWidth: 80
        },
        {
          prop: 'samplePointWeek',
          label: '周',
          minWidth: 80
        },
        {
          prop: 'samplePointDay',
          label: '日',
          minWidth: 80
        }
      );
    }

    /** 类型为3 的列*/
    if (designType.value === '3') {
      baseColumns.push(
        {
          label: '访视号',
          prop: 'visitNumber',
          minWidth: 110,
          hideInTable: designType.value !== '3'
        },
        {
          label: '研究时间',
          prop: 'studyTime',
          minWidth: 110,
          hideInTable: designType.value !== '3'
        }
      );
    }

    baseColumns.push(
      /** 类型为1、2、3 的列*/
      {
        label: '采样时间点',
        prop: 'samplePointTime',
        minWidth: 110
      },
      // 公用字段
      {
        label: '样本序号',
        prop: 'sampleNumber',
        minWidth: 90
      },
      {
        label: '检测指标',
        prop: 'testingIndex',
        minWidth: 110
      },
      {
        label: '样本类型',
        prop: 'sampleType',
        formatter: (row) => {
          return sampleTypeDicts.value.find(
            (d) => d.dictValue === row.sampleType
          )?.dictLabel;
        },
        minWidth: 180
      },
      {
        label: '分装号',
        prop: 'packagingNumber',
        minWidth: 80
      },
      {
        label: '受试者编号',
        prop: 'subjectNumber',
        minWidth: 150,
        hideInTable: designType.value === '3'
      },
      {
        label: '机构名称',
        prop: 'hospitalName',
        minWidth: 110
      },
      {
        label: '接收日期',
        prop: 'createDate',
        minWidth: 110
      },
      {
        label: '样本位置',
        prop: 'position',
        minWidth: 110,
        fixed: 'right'
      }
    );
    return baseColumns;
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    const res = await getSampleInventoryList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
    res.records = res.records.map((row) => {
      return {
        ...row,
        ...JSON.parse(row.content)
      };
    });
    return res;
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  /** 导出数据 */
  const handleExportSampleLabel = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportSampleInventory({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
