<template>
  <div>
    <ele-modal
      form
      :width="1100"
      :title="editData.consumablesName"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <ele-pro-table
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :pagination="false"
      >
      </ele-pro-table>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { getConsumablesDetailsVoList } from '@/api/statistics/materials';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  // 耗材包列表
  const columns = ref([
    {
      label: '耗材ID',
      prop: 'systemId'
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '数量',
      prop: 'quantity'
    },
    {
      label: '样本序号',
      prop: 'sampleNumber'
    },
    {
      label: '样本类型',
      prop: 'substrate',
      slot: 'substrate'
    },
    {
      label: '分装号',
      prop: 'packageCode'
    },
    {
      label: '检测内容',
      prop: 'detectionContent'
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ]);

  /** 表格数据源 */
  const datasource = async ({}) => {
    const { data } = await getConsumablesDetailsVoList({
      consumablesId: props.editData.consumablesId
    });
    return {
      records: data
    };
  };
</script>
