<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <div class="content-wrapper">
        <div class="content-body">
          <div class="title">待办任务</div>
          <div class="top-menu">
            <el-radio-group
              v-model="tabPosition"
              style="margin-bottom: 10px"
              @change="changeTab"
            >
              <el-radio-button v-if="fileTypeDict.length" value=""
                >全部{{
                  totalCount ? '(' + totalCount + ')' : ''
                }}</el-radio-button
              >
              <el-radio-button
                :value="c.value"
                v-for="c in fileTypeDict"
                :key="c.value"
                >{{ c.label
                }}{{ c.number ? '(' + c.number + ')' : '' }}</el-radio-button
              >
            </el-radio-group>
          </div>
          <!-- 表格 -->
          <ele-pro-table
            ref="tableRef"
            row-key="userId"
            :columns="columns"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            cache-key="message-pending"
            :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
            :default-sort="{ prop: 'createTime', order: 'ascending' }"
            :footer-style="{ paddingBottom: '12px' }"
            style="padding-bottom: 0; margin-top: -5px"
            :pagination="false"
          >
            <!-- 操作列 -->
            <template #action="{ row }">
              <el-link
                type="primary"
                underline="never"
                @click.stop="openDetail(row)"
              >
                去处理
              </el-link>
            </template>
          </ele-pro-table>
        </div>
        <div class="content-right">
          <div class="title">
            <span>消息通知</span>
            <el-link type="primary" underline="never" @click="toMore"
              >查看更多</el-link
            ></div
          >
          <div class="msg-wrapper">
            <div class="msg-item" v-for="(item, index) in msgList" :key="index">
              <el-tooltip
                :content="item.messContent"
                placement="top"
                class="item"
              >
                <p class="msg-title">{{ item.messContent }}</p>
              </el-tooltip>
              <p class="sub-title">
                <span style="color: #909399">{{ item.createTime }}</span>
                <el-link
                  type="primary"
                  underline="never"
                  @click.stop="openSee(item)"
                  >去查看></el-link
                >
              </p>
            </div>
            <el-empty :image-size="100" v-if="msgList.length == 0" />
          </div>
        </div>
      </div>
    </ele-card>
    <msgDialog ref="msgDialogRef" v-model="visibleModal" />
  </ele-page>
</template>

<script setup>
  import { ref, computed, nextTick } from 'vue';
  import { getProblemList, getTodoList } from '@/api/todo';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import msgDialog from './msgDialog.vue';

  defineOptions({ name: 'MessagePending' });

  const userStore = useUserStore();

  const { push } = useRouter();

  const tabPosition = ref('');

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 60,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'title',
        label: '项目名称'
      },
      {
        prop: 'todoNum',
        label: '待处理数量',
        align: 'center',
        width: 120
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 120,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);
  const visibleModal = ref(false);
  const msgList = ref([]);
  const fileTypeDict = ref([
    // 代办类型（1物资待审核, 2物资待发运, 3物资待接收, 4样本待接收, 5问题待关闭, 6问题超期提醒, 7合同待上传, 8节点待配置, 9开票&汇款进度待跟进, 10耗材包有效期提醒, 11库存不足提醒）
    {
      label: '物资待审核',
      value: '1',
      num: 0
    },
    {
      label: '物资待发运',
      value: '2',
      num: 0
    },
    {
      label: '物资待接收',
      value: '3',
      num: 0
    },
    {
      label: '样本待接收',
      value: '4',
      num: 0
    },
    {
      label: '问题待关闭',
      value: '5',
      num: 0
    },
    {
      label: '问题超期提醒',
      value: '6',
      num: 0
    },
    {
      label: '合同待上传',
      value: '7',
      num: 0
    },
    {
      label: '节点待配置',
      value: '8',
      num: 0
    },
    {
      label: '开票&汇款进度待跟进',
      value: '9',
      num: 0
    },
    {
      label: '耗材包有效期提醒',
      value: '10',
      num: 0
    },
    {
      label: '库存不足提醒',
      value: '11',
      num: 0
    }
  ]);
  const totalCount = ref(0);
  const dataList = ref([]);

  /** 表格数据源 */
  const datasource = async ({ pages, where }) => {
    const response = await getTodoList({
      type: where.type ? where.type : 0
    });
    if (where && !where.type) {
      totalCount.value = 0;
      dataList.value = response;
      console.log(dataList.value, fileTypeDict.value);
      if (
        dataList.value &&
        dataList.value.length &&
        fileTypeDict.value &&
        fileTypeDict.value.length
      ) {
        // 统计各类型文件数量
        fileTypeDict.value = fileTypeDict.value.filter((type) => {
          const count = dataList.value.filter(
            (file) => file.todoType === type.value
          ).length;
          if (count) {
            type.number = count;
            return type;
          }
        });
        // 统计总数量
        totalCount.value = dataList.value.length;
      } else {
        fileTypeDict.value = [];
      }
    }
    return response;
  };

  const changeTab = (val) => {
    tabPosition.value = val;
    reload({
      type: val
    });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const openDetail = async (row) => {
    if (row.projectId) {
      await userStore.setUserInfoByProject(row.projectId, '/message-pending');
    }
    push(row.url);
  };
  const openSee = (row) => {
    userStore.setUserInfoByProject(row.projectId, '/message-pending');
    push({
      path: row.messUrl,
      query: {
        id: row?.projectId
      }
    });
  };
  const toMore = () => {
    visibleModal.value = true;
  };
  nextTick(() => {
    let params = {
      page: 1,
      limit: 10
    };
    getProblemList(params).then((res) => {
      if (res.code == 200) {
        msgList.value = res.records;
      }
    });
  });
</script>
<style lang="scss" scoped>
  .top-menu {
    display: flex;
    justify-content: space-between;
  }
  .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    margin-right: 10px;
  }
  .content-wrapper {
    display: flex;
  }
  .content-body {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 10px 20px;
    flex: 1;
    height: calc(100vh - 143px);
    overflow-y: auto;
  }
  .content-right {
    width: 350px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 10px 20px;
    margin-left: 15px;
    height: calc(100vh - 143px);
    overflow-y: auto;
  }
  .sub-title {
    display: flex;
    justify-content: space-between;
    padding-right: 5px;
  }
  .msg-item {
    border-bottom: 1px solid #e4e7ed;
  }
  .msg-wrapper {
    height: calc(100vh - 220px);
    overflow-y: auto;
    padding-bottom: 20px;
  }
  .msg-title {
    font-size: 14px;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
