<template>
  <ele-page flex-table>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'materialForwarding:forwarding:add'"
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button>
          <el-button
            v-permission="'materialForwarding:forwarding:export'"
            @click="exportFun"
            plain
            >导出</el-button
          >
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="forwardingId"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="material-shipment"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <template #status="{ row }">
          <el-tag :type="statusMap[row.status]"
            >{{ statusObj[row.status] }}
          </el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'materialForwarding:forwarding:query'"
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
          <el-divider
            v-permission="'materialForwarding:forwarding:query'"
            direction="vertical"
          />
          <el-link
            type="primary"
            underline="never"
            v-permission="'project:problem:add'"
            @click.stop="questionsClick(row)"
          >
            提问
          </el-link>
          <el-divider
            v-permission="'materialForwarding:forwarding:revoke'"
            v-if="row.status == '0'"
            direction="vertical"
          />
          <el-link
            v-permission="'materialForwarding:forwarding:revoke'"
            v-if="row.status == '0'"
            type="danger"
            underline="never"
            @click.stop="revoke(row)"
          >
            撤销
          </el-link>
        </template>
      </ele-pro-table>

      <handleModal
        v-model="visibleModal"
        :handle="handle"
        :editData="editData"
        @success="reload"
      />
      <handleModalQuestions v-model="questionsVisibleModal" :data="editData" />
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getMaterialForwardingList,
    exportMaterialForwarding,
    materialForwardingRevoke
  } from '@/api/project/material-management/material-shipment';
  import handleModal from './handleModal/index.vue';
  import handleModalQuestions from './handleModal/questions.vue';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'MaterialShipment' });

  const { push } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    hospitalName: '',
    hospitalCode: '',
    forwardingNumber: '',
    bookingNote: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      type: 'input',
      label: '发运编号',
      prop: 'forwardingNumber'
    },
    {
      type: 'input',
      label: '运输单号',
      prop: 'bookingNote'
    }
  ]);

  const statusObj = {
    0: '待接收',
    1: '已接收',
    2: '已撤销'
  };

  const statusMap = {
    0: 'perimary',
    1: 'success',
    2: 'danger'
  };

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'status',
        label: '发运状态',
        width: 100,
        align: 'center',
        slot: 'status'
      },
      {
        prop: 'applyNumbers',
        label: '申请编号',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '研究机构',
        align: 'center'
      },
      {
        prop: 'forwardingNumber',
        label: '发运编号',
        align: 'center'
      },
      {
        prop: 'recipientName',
        label: '收件人姓名',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'phone',
        label: '电话',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'address',
        label: '地址',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'shipperName',
        label: '发运人',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '发运日期',
        align: 'center'
      },
      {
        prop: 'receiveName',
        label: '接收人',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'receiveTime',
        label: '接收时间',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'logisticsCompany',
        label: '运输单位',
        align: 'center'
      },
      {
        prop: 'bookingNote',
        label: '运输单号',
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialForwardingList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  //详情
  const openDetail = (row) => {
    push({
      path: '/project/material-management/material-shipment/details',
      query: {
        id: row?.forwardingId
      }
    });
  };

  /** 编辑 */
  const openEdit = (row) => {
    handle.value = row ? 'edit' : 'add';
    editData.value = row || null;
    visibleModal.value = true;
  };

  // 提问
  const questionsVisibleModal = ref(false);
  const questionsClick = (row) => {
    editData.value = row || null;
    questionsVisibleModal.value = true;
  };

  /** 撤销 */
  const revoke = (row) => {
    ElMessageBox.confirm(`确定要撤销该申请数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        materialForwardingRevoke({ forwardingId: row.forwardingId })
          .then(() => {
            loading.close();
            EleMessage.success('操作成功');
            reload(form);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportMaterialForwarding({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
