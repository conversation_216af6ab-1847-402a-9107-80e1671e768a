<template>
  <div>
    <ele-modal
      form
      :width="550"
      title="废弃项目"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <h4 class="flex"
        >你确认废弃该项目么？废弃后，所有流程都会被冻结且不允许操作</h4
      >
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { projectDiscard } from '@/api/project-management';
  import ProForm from '@/components/ProForm/index.vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    id: {
      type: String,
      default: ''
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    password: '',
    remark: '',
    projectId: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '密码验证',
      prop: 'password',
      type: 'input',
      props: {
        showPassword: true
      },
      required: true
    },
    {
      label: '操作原因',
      prop: 'remark',
      type: 'input',

      required: true
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      projectDiscard(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  watch(visible, (val) => {
    if (val) {
      assignFields({ projectId: props.id });
    }
  });
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
</style>
