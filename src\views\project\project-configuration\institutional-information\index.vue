<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'project:hospital:add'"
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增机构
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="normal-table"
        :border="tableBorder"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'project:hospital:edit'"
            type="primary"
            underline="never"
            @click.stop="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['project:hospital:edit', 'project:hospital:remove']"
            direction="vertical"
          />
          <el-link
            v-permission="'project:hospital:remove'"
            type="danger"
            underline="never"
            @click.stop="remove(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>

      <handleModal
        v-model="visibleModal"
        :handle="handle"
        :editData="editData"
        @seccess="reload"
      />
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getHospitalList,
    hospitalRemove
  } from '@/api/project/project-configuration/institutional-information';
  import handleModal from './handleModal/index.vue';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'InstitutionalInformation' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    hospitalCode: '',
    hospitalName: '',
    phone: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '电话',
      prop: 'phone'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        width: 100,
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '机构名称',
        align: 'center'
      },
      {
        prop: 'researcher',
        label: '研究者',
        align: 'center'
      },
      {
        prop: 'phone',
        label: '电话',
        align: 'center'
      },
      {
        prop: 'email',
        label: '邮箱',
        align: 'center'
      },
      {
        prop: 'address',
        label: '地址',
        align: 'center'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 150,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getHospitalList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  const changeTableBorder = () => {
    tableBorder.value = !tableBorder.value;
  };

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');
  /** 编辑 */
  const openEdit = (row) => {
    handle.value = row ? 'edit' : 'add';
    editData.value = row || null;
    visibleModal.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该机构吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        hospitalRemove(row.hospitalId)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
