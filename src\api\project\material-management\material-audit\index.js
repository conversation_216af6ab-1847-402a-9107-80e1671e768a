import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
// CRC-物资申请列表
export async function getMaterialApplyAuditList(params) {
  const res = await request.get('/materialApply/auditList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//审核
export async function materialApplyAudit(data) {
  const res = await request.post('/materialApply/audit', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出
export async function exportMaterialApplyAudit(params) {
  const res = await request({
    url: '/materialApply/exportAuditList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资审核_${Date.now()}.xlsx`);
}
