import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getCustomerStatList(params) {
  const res = await request.get('/customerStat/selectCustomerStatList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出
export async function exportCustomerStat(params) {
  const res = await request({
    url: '/customerStat/exportCustomerStatList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `客户统计_${Date.now()}.xlsx`);
}
