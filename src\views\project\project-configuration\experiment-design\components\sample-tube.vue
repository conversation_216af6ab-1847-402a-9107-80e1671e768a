<template>
  <div class="sample-tube-container">
    <div class="header" v-show="!props.isBatch">
      <div class="title">样本管配置</div>
      <div>
        <el-button
          type="success"
          v-loading="loading"
          :icon="CheckCircleOutlined"
          @click="handleSave"
          v-show="!isView"
          v-permission="'sample:sampleTube:edit'"
          >保存配置</el-button
        >
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      label-width="0px"
      v-show="pointId"
      @submit.prevent=""
    >
      <ele-data-table
        row-key="id"
        :columns="columns"
        :data="form.tube"
        cell-class-name="editable-table-cell"
        class="editable-table"
      >
        <!-- 检测指标 -->
        <template #testingIndex="{ row, $index }">
          <el-form-item
            :prop="'tube.' + $index + '.testingIndex'"
            :rules="{
              required: true,
              message: '请选择检测指标',
              trigger: 'blur'
            }"
            style="margin-bottom: 0"
          >
            <el-input
              v-model="row.testingIndex"
              placeholder="请输入检测指标"
              :disabled="isView"
              maxlength="10"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>
        </template>

        <!-- 样本类型 -->
        <template #sampleType="{ row, $index }">
          <el-form-item
            :prop="'tube.' + $index + '.sampleType'"
            :rules="{
              required: true,
              message: '请选择样本类型',
              trigger: 'blur'
            }"
            style="margin-bottom: 0"
          >
            <dict-data
              v-model="row.sampleType"
              placeholder="请选择样本类型"
              :disabled="isView"
              style="width: 100%"
              code="sample_type"
            />
          </el-form-item>
        </template>

        <!-- 分装份数 -->
        <template #splitChargingNumber="{ row, $index }">
          <el-form-item
            :prop="'tube.' + $index + '.splitChargingNumber'"
            :rules="{
              required: true,
              message: '请输入分装份数',
              trigger: 'blur'
            }"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="row.splitChargingNumber"
              :disabled="isView"
              :min="0"
              :max="10"
              placeholder="请输入分装数"
              style="width: 100%"
            />
          </el-form-item>
        </template>

        <!-- 操作 -->
        <template #action="{ $index }">
          <el-link
            type="danger"
            underline="never"
            @click="handleDelete($index)"
            v-permission="'sample:sampleTube:remove'"
          >
            删除
          </el-link>
        </template>
      </ele-data-table>
      <el-button
        plain
        type="primary"
        :icon="PlusOutlined"
        @click="handleAdd"
        style="width: 100%; margin-top: 5px"
        v-show="!isView"
        v-permission="'sample:sampleTube:add'"
        >新增样本管</el-button
      >
    </el-form>
    <el-empty v-show="!pointId" description="请选择采样点进行配置" />
  </div>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { PlusOutlined, CheckCircleOutlined } from '@/components/icons';
  import {
    getSampleTubeList,
    addSampleTubeBatch,
    editSampleTube,
    deleteSampleTube
  } from '@/api/project/project-configuration/experiment-design';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  const props = defineProps({
    pointId: [String, Array],
    // 是否批量配置
    isBatch: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['saveTube']);
  // 保存的时候需要调用删除接口；
  const deleteIds = ref([]);

  // 表格数据
  const form = reactive({
    tube: [
      {
        id: 1,
        testingIndex: 'Plasma',
        sampleType: '血浆',
        splitChargingNumber: 0,
        remark: ''
      },
      {
        id: 2,
        testingIndex: '血清',
        sampleType: '血清',
        splitChargingNumber: 0,
        remark: ''
      }
    ]
  });

  // 表格列配置
  const columns = computed(() => {
    const columns = [
      {
        label: '检测指标',
        prop: 'testingIndex',
        minWidth: 120,
        slot: 'testingIndex'
      },
      {
        label: '样本类型',
        prop: 'sampleType',
        minWidth: 120,
        slot: 'sampleType'
      },
      {
        label: '分装份数',
        prop: 'splitChargingNumber',
        minWidth: 120,
        slot: 'splitChargingNumber'
      }
    ];
    if (!props.isView) {
      columns.push({
        label: '操作',
        columnKey: 'action',
        width: 80,
        align: 'center',
        slot: 'action'
      });
    }
    return columns;
  });

  // 新增行
  const handleAdd = () => {
    const newId = Math.max(...form.tube.map((item) => item.id)) + 1;
    form.tube.push({
      id: newId,
      testingIndex: '',
      sampleType: '',
      splitChargingNumber: null,
      remark: ''
    });
  };

  // 删除行
  const handleDelete = (index) => {
    form.tube[index].tubeId && deleteIds.value.push(form.tube[index].tubeId);
    form.tube.splice(index, 1);
  };

  const formRef = ref();
  const loading = ref(false);
  // 保存
  const handleSave = () => {
    if (!form.tube.length) {
      EleMessage.error('请添加样本管');
      return;
    }
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let params;
      if (props.isBatch) {
        params = {
          pointIds: props.pointId,
          tubeList: form.tube
        };
      } else {
        params = form.tube?.map((item) => {
          return { ...item, pointId: props.pointId };
        });
      }

      // 先调用删除接口
      if (deleteIds.value.length) {
        await deleteSampleTube(deleteIds.value.join(','));
      }
      // 保存，都调用编辑，后端根据有无id进行判断
      const saveOrUpdate = props.isBatch ? addSampleTubeBatch : editSampleTube;
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  // 关闭
  const onClose = () => {
    emit('saveTube', props.pointId);
  };

  // 获取样本管
  const getSampleTube = async (pointId) => {
    const res = await getSampleTubeList(pointId);
    if (res.data.length) {
      form.tube = res.data;
    } else {
      form.tube = [];
      handleAdd();
    }
  };

  watch(
    () => props.pointId,
    async (val) => {
      deleteIds.value = [];
      // 单个配置才要去获取数据
      if (val && !props.isBatch) {
        await getSampleTube(val);
      } else {
        form.tube = [];
        handleAdd();
      }
    },
    {
      immediate: true,
      deep: true
    }
  );

  defineExpose({
    handleSave
  });
</script>

<style scoped lang="less">
  .sample-tube-container {
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      margin-bottom: 10px;
      padding: 0 10px 10px 10px;
      height: 25px;
      border-bottom: 1px solid #e9e9e9;

      .title {
        font-weight: bold;
        font-size: 16px;
      }
    }

    .footer {
      margin-top: 16px;
      text-align: right;

      .el-button {
        margin-left: 8px;
      }
    }
  }

  .editable-table {
    :deep(.editable-table-cell) {
      padding: 4px 8px;
    }
  }
</style>
