<template>
  <div>
    <ele-modal
      form
      :width="1300"
      title="项目立项"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
      modal-class="custom"
    >
      <edit-form @close="visible = false" :id="id" @success="success" />
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import EditForm from '../components/edit-form.vue';
  import { ref } from 'vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 操作类型 add/edit */
    handle: {
      type: String,
      default: 'add'
    },
    /** 编辑数据 */
    id: {
      type: String,
      default: ''
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  const success = () => {
    emit('success');
  };
</script>
<style lang="scss" scoped>
  .custom {
    height: 300px;
  }
</style>
