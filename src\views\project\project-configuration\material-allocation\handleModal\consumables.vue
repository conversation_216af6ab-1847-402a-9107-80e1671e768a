<template>
  <div>
    <ele-modal
      form
      :width="600"
      :title="handle == 'edit' ? '编辑耗材项' : '新增耗材项'"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <el-alert
        title="样本序号大于0，表示当前耗材项需要生成样本标签"
        type="warning"
        show-icon
        style="margin-bottom: 15px"
      />
      <pro-form
        ref="formRef"
        :model="form"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { selectInventoryInfoList } from '@/api/project/project-configuration/material-allocation';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    handle: {
      type: String,
      default: 'add'
    },
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    consumableType: '',
    consumableId: '',
    sampleNumber: '',
    quantity: '',
    substrate: '',
    packageCode: '',
    detectionContent: '',
    remark: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '耗材类型',
      type: 'dictSelect',
      prop: 'consumableType',
      props: { code: 'consumable_type' },
      required: true
    },
    {
      label: '耗材名称',
      prop: 'consumableId',
      type: 'select',
      options: [],
      required: true
    },
    {
      label: '样本序号',
      prop: 'sampleNumber',
      type: 'inputNumber'
    },
    {
      label: '数量',
      prop: 'quantity',
      type: 'inputNumber',
      required: true,
      props: {
        disabled: false,
        min: 1
      }
    },
    {
      label: '样本类型',
      prop: 'substrate',
      type: 'dictSelect',
      props: { code: 'sample_type', disabled: false },
      required: false
    },
    {
      label: '分装号',
      prop: 'packageCode',
      type: 'inputNumber',
      required: false,
      props: {
        disabled: false
      }
    },
    {
      label: '检测内容',
      prop: 'detectionContent',
      type: 'input',
      required: false,
      props: {
        disabled: false
      }
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea'
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const info = consumableList.value.filter(
        (el) => el.consumableId == form.consumableId
      )[0];
      emit('success', { ...info, ...form });
      cancelDialog();
    });
  };

  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };
  //获取耗材项
  const consumableList = ref([]);
  const getConsumableListFun = async () => {
    const res = await selectInventoryInfoList({
      consumableType: form.consumableType
    });
    if (res.data.length !== 0) {
      consumableList.value = res.data.map((el) => {
        return {
          label: `${el.consumableName}/${el.consumableSize}/${el.consumableMaterial}/${el.consumableBrand}/库存总量${el.inventoryNumber ? el.inventoryNumber : 0}`,
          value: el.consumableId,
          ...el
        };
      });
    }
    setPropItem('consumableId', consumableList.value);
  };
  selectInventoryInfoList({});
  const setRequiredAndDisabled = (proplist, flag) => {
    if (flag) {
      form.quantity = 1;
    } else {
      form.quantity = null;
    }
    items.value.forEach((el) => {
      const list = proplist.filter((item) => item == el.prop);
      if (list.length == 0) return;
      el.required = flag || el.prop == 'quantity';
      el.props.disabled = flag && el.prop == 'quantity';
    });
  };

  watch(
    () => form.consumableType,
    (val) => {
      if (!val) return;
      getConsumableListFun();
    }
  );
  watch(
    () => form.sampleNumber,
    (val) => {
      if (val) {
        setRequiredAndDisabled(
          ['quantity', 'substrate', 'packageCode', 'detectionContent'],
          true
        );
      } else {
        setRequiredAndDisabled(
          ['quantity', 'substrate', 'packageCode', 'detectionContent'],
          false
        );
      }
    }
  );
  watch(
    visible,
    (val) => {
      if (!val) return;
      if (props.editData) {
        assignFields({
          ...props.editData
        });
      } else {
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
