import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//查询
export async function getHospitalMaterialInventoryList(params) {
  const res = await request.get('/hospitalMaterialInventory/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增耗材包标记异常-查询列表
export async function hospitalMaterialSelectWaitList(params) {
  const res = await request.get('/hospitalMaterialInventory/selectWaitList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 标记异常
export async function hospitalMaterialMarkAbnormal(data) {
  const res = await request.post(
    '/hospitalMaterialInventory/markAbnormal',
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出 机构物资库存
export async function exportHospitalMaterialInventory(params) {
  const res = await request({
    url: '/hospitalMaterialInventory/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `机构物资库存_${Date.now()}.xlsx`);
}
