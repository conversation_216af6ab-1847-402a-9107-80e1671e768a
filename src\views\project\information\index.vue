<template>
  <ele-page :flex-table="false" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '20px' }"
      :style="{
        minHeight: fixedHeight ? '180px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
      header="项目&研究信息"
    >
      <template #extra>
        <el-button
          v-permission="'project:project:edit'"
          type="primary"
          plain
          @click="editClick"
          >编辑</el-button
        >
      </template>
      <el-descriptions :column="3">
        <el-descriptions-item label="公司项目编号：">{{
          info.companyProjectNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="试验方案编号：">{{
          info.schemeNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="项目属性：">
          <dict-data
            code="project_attribute"
            type="tag"
            :model-value="info.projectAttribute"
          />
        </el-descriptions-item>
        <el-descriptions-item label="项目名称：" :span="3">{{
          info.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="委托方：">{{
          info.projectClient
        }}</el-descriptions-item>
        <el-descriptions-item label="申办方：">{{
          info.sponsor
        }}</el-descriptions-item>

        <el-descriptions-item label="项目负责人：">{{
          info.projectLeader
        }}</el-descriptions-item>

        <el-descriptions-item label="立项日期：">{{
          info.approvalDate
        }}</el-descriptions-item>
        <el-descriptions-item
          :label="info.endDate ? '结束日期：' : ''"
          :span="2"
          >{{ info.endDate || '' }}</el-descriptions-item
        >
        <el-descriptions-item label="备注：" :span="3">{{
          info.remark
        }}</el-descriptions-item>
      </el-descriptions>
      <ele-data-table row-key="userId" :columns="columns" :data="dataList" />
    </ele-card>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '20px' }"
      :style="{
        minHeight: fixedHeight ? '180px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
      header="客户联系信息"
    >
      <ele-data-table row-key="userId" :columns="columns3" :data="dataList3">
        <template #customerType="{ row }">
          {{ customerTypeObj[row.customerType] }}
        </template>
      </ele-data-table>
    </ele-card>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '20px' }"
      :style="{
        minHeight: fixedHeight ? '180px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
      header="项目成员"
    >
      <template #extra>
        <el-button
          type="primary"
          plain
          v-permission="'project:grant:internalList'"
          @click="goConfig('/project/project-configuration/project-member')"
          >去配置</el-button
        >
      </template>
      <ele-data-table row-key="userId" :columns="columns1" :data="dataList1" />
    </ele-card>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '20px' }"
      :style="{
        minHeight: fixedHeight ? '180px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
      header="研究机构"
    >
      <template #extra>
        <el-button
          type="primary"
          plain
          @click="
            goConfig('/project/project-configuration/institutional-information')
          "
          v-permission="'project:hospital:add'"
          >去配置</el-button
        >
      </template>
      <ele-data-table row-key="userId" :columns="columns2" :data="dataList2" />
    </ele-card>
    <handleModal
      v-model="visibleModal"
      handle="edit"
      :id="userStore.projectId"
      @success="getDetails"
    />
  </ele-page>
</template>

<script setup>
  import { ref, computed, watch, reactive, unref, onMounted } from 'vue';
  import { getProjectDetails } from '@/api/project-management';
  import handleModal from '../../project-management/handleModal/index.vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';

  defineOptions({ name: 'Information' });

  const { currentRoute, push } = useRouter();
  const userStore = useUserStore();
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const info = reactive({});
  // 研究信息列表配置
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '研究名称',
      prop: 'studyName'
    },
    {
      label: '研究编号',
      prop: 'studyNumber'
    },
    {
      label: '检测指标',
      prop: 'checkIndex'
    },
    {
      label: '研究负责人',
      prop: 'studyLeader'
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ]);
  // 研究信息数据
  const dataList = ref([]);

  // 项目成员列表配置
  const columns1 = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '成员姓名',
      prop: 'nickName'
    },
    {
      label: '项目角色',
      prop: 'roleName'
    },
    {
      label: '项目职责',
      prop: 'duties'
    },
    {
      label: '授权日期',
      prop: 'impowerTime'
    }
  ]);
  // 项目成员数据
  const dataList1 = ref([]);

  // 研究机构列表配置
  const columns2 = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      label: '研究者',
      prop: 'researcher'
    },
    {
      label: '电话',
      prop: 'phone'
    },
    {
      label: '邮箱',
      prop: 'email'
    },
    {
      label: '地址',
      prop: 'address'
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ]);
  // 研究机构数据
  const dataList2 = ref([]);

  // 研究机构列表配置
  const columns3 = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '客户类型',
      prop: 'customerType',
      slot: 'customerType'
    },
    {
      label: '项目经理',
      prop: 'customerLeader'
    },
    {
      label: '电话',
      prop: 'phone'
    },
    {
      label: '邮箱',
      prop: 'email'
    },
    {
      label: '地址',
      prop: 'address',
      width: '400px'
    }
  ]);
  // 研究机构数据
  const dataList3 = ref([]);

  const customerTypeObj = {
    1: '申办方',
    2: '委托方'
  };

  // 编辑项目信息
  const visibleModal = ref(false);
  const editClick = () => {
    visibleModal.value = true;
  };

  const getDetails = async () => {
    const { data } = await getProjectDetails(userStore.projectId);
    Object.assign(info, data);
    dataList.value = data?.projectStudyList || [];
    dataList1.value = data?.projectGrantList || [];
    dataList2.value = data?.projectHospitalList || [];
    dataList3.value = data?.projectCustomerList || [];
  };

  const goConfig = (url) => {
    push({ path: url });
  };
  onMounted(() => {
    getDetails();
  });
</script>
<style scoped lang="scss">
  :deep .el-descriptions__label {
    margin-right: 5px !important;
  }
</style>
