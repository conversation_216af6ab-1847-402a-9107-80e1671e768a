<template>
  <div class="demo-container">
    <div class="demo-header">
      <h2>弧度切换按钮 Demo</h2>
      <p>点击按钮查看平滑的弧度切换效果</p>
    </div>

    <div class="demo-content">
      <!-- 基础版本 -->
      <div class="demo-section">
        <h3>基础版本</h3>
        <CurvedToggleButton @change="handleChange" />
        <p class="result">当前选择: {{ currentValue?.label || '未选择' }}</p>
      </div>

      <!-- 自定义颜色版本 -->
      <div class="demo-section">
        <h3>自定义颜色版本</h3>
        <CurvedToggleButton 
          class="custom-color"
          @change="handleCustomChange" 
        />
        <p class="result">当前选择: {{ customValue?.label || '未选择' }}</p>
      </div>

      <!-- 大尺寸版本 -->
      <div class="demo-section">
        <h3>大尺寸版本</h3>
        <CurvedToggleButton 
          class="large-size"
          @change="handleLargeChange" 
        />
        <p class="result">当前选择: {{ largeValue?.label || '未选择' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CurvedToggleButton from '@/components/demo/CurvedToggleButton.vue'

// 响应式数据
const currentValue = ref(null)
const customValue = ref(null)
const largeValue = ref(null)

// 事件处理
const handleChange = (value) => {
  currentValue.value = value
  console.log('基础版本选择:', value)
}

const handleCustomChange = (value) => {
  customValue.value = value
  console.log('自定义颜色版本选择:', value)
}

const handleLargeChange = (value) => {
  largeValue.value = value
  console.log('大尺寸版本选择:', value)
}
</script>

<style scoped>
.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  background: #fafafa;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h2 {
  color: #333;
  font-size: 28px;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.demo-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.demo-section h3 {
  color: #333;
  font-size: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.result {
  text-align: center;
  margin-top: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  color: #495057;
  font-weight: 500;
}

/* 自定义颜色样式 */
.custom-color :deep(.toggle-slider) {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.custom-color :deep(.toggle-wrapper) {
  background: #ffe8e8;
}

/* 大尺寸样式 */
.large-size :deep(.toggle-button) {
  padding: 16px 32px;
  font-size: 16px;
  min-width: 140px;
}

.large-size :deep(.toggle-wrapper) {
  border-radius: 30px;
}

.large-size :deep(.toggle-slider) {
  border-radius: 26px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 20px 15px;
  }
  
  .demo-section {
    padding: 20px;
  }
  
  .demo-header h2 {
    font-size: 24px;
  }
}
</style>
