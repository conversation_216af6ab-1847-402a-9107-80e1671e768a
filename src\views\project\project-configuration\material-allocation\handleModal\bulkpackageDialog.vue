<template>
  <div>
    <ele-modal
      form
      :width="1000"
      title="选择耗材项"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
      </EleTableSearch>
      <ele-pro-table
        ref="tableRef"
        row-key="consumableId"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
      >
      </ele-pro-table>
      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    bulkConsumablesAdd,
    selectPageInventoryInfoList
  } from '@/api/project/project-configuration/material-allocation';

  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const visible = defineModel({ type: Boolean, default: false });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    selections.value = [];
  };

  /** 提交状态 */
  const loading = ref(false);
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    consumableName: ''
  });
  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };
  /** 搜索事件 */
  const onSearch = (where) => {
    reload(form);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName'
    }
  ]);
  //
  // 耗材包列表
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize',
      width: 90
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ]);
  const selections = ref([]);
  const isUpdate = ref(false);

  /** 保存编辑 */
  const save = () => {
    if (selections.value.length == 0) return EleMessage.warning('请选择');
    const consumableIdList = selections.value.map((el) => el.consumableId);
    loading.value = true;
    const params = {
      projectId: userStore.projectId,
      consumableIdList
    };
    bulkConsumablesAdd(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        emit('success');
        cancelDialog();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    const projectId = userStore.projectId;
    return selectPageInventoryInfoList({
      ...where,
      ...orders,
      ...filters,
      ...pages,
      projectId
    });
  };
</script>
