<template>
  <div class="curved-toggle-container">
    <div class="toggle-wrapper">
      <!-- 背景滑块 -->
      <div 
        class="toggle-slider" 
        :class="{ 'active': activeIndex === 1 }"
      ></div>
      
      <!-- 按钮组 -->
      <button 
        v-for="(item, index) in options" 
        :key="item.value"
        class="toggle-button"
        :class="{ 'active': activeIndex === index }"
        @click="handleToggle(index)"
      >
        {{ item.label }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义选项
const options = ref([
  { label: '*********', value: '*********' },
  { label: '*********', value: '*********' }
])

// 当前激活的索引
const activeIndex = ref(0)

// 定义事件
const emit = defineEmits(['change'])

// 切换处理
const handleToggle = (index) => {
  activeIndex.value = index
  emit('change', options.value[index])
}

// 暴露当前值
defineExpose({
  activeValue: () => options.value[activeIndex.value],
  setActive: (index) => {
    activeIndex.value = index
  }
})
</script>

<style scoped>
.curved-toggle-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.toggle-wrapper {
  position: relative;
  display: flex;
  background: #f5f5f5;
  border-radius: 25px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.toggle-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(50% - 4px);
  height: calc(100% - 8px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 21px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

.toggle-slider.active {
  transform: translateX(100%);
}

.toggle-button {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 21px;
  min-width: 120px;
  white-space: nowrap;
}

.toggle-button:hover {
  color: #333;
}

.toggle-button.active {
  color: white;
  font-weight: 600;
}

.toggle-button:focus {
  outline: none;
}

/* 添加弧度连接效果 */
.toggle-wrapper::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 60%;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 1px;
  z-index: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .toggle-button {
    min-width: 100px;
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 动画增强 */
.toggle-wrapper:hover .toggle-slider {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 可选：添加渐变背景变化 */
.toggle-slider {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.toggle-button.active {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
</style>
