<template>
  <div class="curved-toggle-container">
    <div class="toggle-wrapper">
      <!-- 左侧按钮 -->
      <button
        class="toggle-button left-button"
        :class="{ active: activeIndex === 0 }"
        @click="handleToggle(0)"
      >
        {{ options[0].label }}
      </button>

      <!-- 右侧按钮 -->
      <button
        class="toggle-button right-button"
        :class="{ active: activeIndex === 1 }"
        @click="handleToggle(1)"
      >
        {{ options[1].label }}
      </button>

      <!-- 圆弧分隔线 -->
      <div class="arc-divider"></div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  // 定义选项
  const options = ref([
    { label: '*********', value: '*********' },
    { label: '*********', value: '*********' }
  ]);

  // 当前激活的索引
  const activeIndex = ref(0);

  // 定义事件
  const emit = defineEmits(['change']);

  // 切换处理
  const handleToggle = (index) => {
    activeIndex.value = index;
    emit('change', options.value[index]);
  };

  // 暴露当前值
  defineExpose({
    activeValue: () => options.value[activeIndex.value],
    setActive: (index) => {
      activeIndex.value = index;
    }
  });
</script>

<style scoped>
  .curved-toggle-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .toggle-wrapper {
    position: relative;
    display: flex;
    background: #f0f0f0;
    height: 50px;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .toggle-button {
    position: relative;
    flex: 1;
    padding: 12px 24px;
    border: none;
    background: #f0f0f0;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    white-space: nowrap;
    z-index: 2;
  }

  .left-button {
    clip-path: polygon(
      0 0,
      calc(100% - 20px) 0,
      100% 50%,
      calc(100% - 20px) 100%,
      0 100%
    );
    padding-right: 35px;
  }

  .right-button {
    clip-path: polygon(20px 0, 100% 0, 100% 100%, 20px 100%, 0 50%);
    padding-left: 35px;
    margin-left: -20px;
  }

  .toggle-button:hover {
    color: #333;
  }

  .toggle-button.active {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .toggle-button:focus {
    outline: none;
  }

  .arc-divider {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 100%;
    background: transparent;
    z-index: 3;
    pointer-events: none;
  }

  .arc-divider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .toggle-button {
      min-width: 100px;
      padding: 10px 16px;
      font-size: 13px;
    }

    .left-button {
      padding-right: 25px;
    }

    .right-button {
      padding-left: 25px;
    }
  }

  /* 动画增强 */
  .toggle-wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
</style>
