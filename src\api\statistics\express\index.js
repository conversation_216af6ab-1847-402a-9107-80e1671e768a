import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getExpressDeliveryStat(params) {
  const res = await request.get('/expressDeliveryStat/selectMaterialStatList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出
export async function exportExpressDeliveryStat(params) {
  const res = await request({
    url: '/expressDeliveryStat/exportConsumablesPackageStat',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `快递统计_${Date.now()}.xlsx`);
}
