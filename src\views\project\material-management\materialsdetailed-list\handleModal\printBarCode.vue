<template>
  <div>
    <ele-modal
      form
      :width="750"
      title="打印耗材包条码"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <div class="flex">
        <div
          v-for="(item, index) in data"
          :key="index"
          style="
            margin: 0 10px 20px 10px;
            text-align: center;
            font-size: 12px;
            line-height: 14px;
          "
        >
          <div>{{ item.schemeNumber }}</div>
          <div
            ><span style="display: inline-block; width: 20px; "></span>/{{ item.visitNumber }}/{{ item.studyTime
            }}{{ item.samplePointTime ? '-' : ''
            }}{{ item.samplePointTime }}</div
          >
          <ele-bar-code
            :value="item.packageCode"
            :tag="tag"
            :options="options"
          />
        </div>
      </div>
      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" @click="save"> 确定打印 </el-button>
        </div>
      </template>
    </ele-modal>
    <ele-printer
      v-model="printing"
      margin="0mm 12mm 10mm 12mm"
      :header-style="{
        padding: '26px 0 2px 0',
        fontSize: '13px',
        borderBottom: '1px solid #666',
        marginBottom: '26px'
      }"
      :body-style="{ fontSize: '14px', lineHeight: 2.5 }"
      target="_iframe"
      :static="false"
      @done="handlePrintDone"
    >
      <div v-for="(item, index) in data" :key="index">
        <div
          style="
            text-align: center;
            font-size: 12px;
            line-height: 12px;
            height: 90px;
            padding: 10px
          "
        >
          <div>{{ item.schemeNumber }}</div>
          <div>
            <span style="display: inline-block; width: 20px; "></span>/{{
              item.visitNumber
            }}/{{ item.studyTime }}{{ item.samplePointTime ? '-' : ''
            }}{{ item.samplePointTime }}
          </div>
          <ele-bar-code
            :value="item.packageCode"
            :tag="tag"
            :options="options"
          />
        </div>
      </div>
    </ele-printer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, reactive, nextTick } from 'vue';
  import type { MessageHandler as ElMessageHandler } from 'element-plus/es/components/message';
  import { EleMessage } from 'ele-admin-plus';
  let loading: ElMessageHandler;

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    data: {
      type: Array,
      default: () => []
    }
  });

  /** 渲染方式 */
  const tag = ref('img');

  /** 参数配置 */
  const options = reactive({
    height: 20,
    width: 1,
    marginTop: 2,
    marginLeft: 5,
    marginRight: 5,
    marginBottom: 0,
    displayValue: true,
    textPosition: 'bottom',
    fontSize: 12,
    format: 'CODE128'
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };
  const printing = ref(false);
  const save = () => {
    loading = EleMessage.loading({
      message: '正在打印中..',
      plain: true,
      centered: true,
      mask: true
    });
    nextTick(() => {
      printing.value = true;
    });
  };
  /** 打印结束事件 */
  const handlePrintDone = () => {
    loading && loading.close();
  };
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    max-height: 500px;
    overflow: auto;
  }
</style>
