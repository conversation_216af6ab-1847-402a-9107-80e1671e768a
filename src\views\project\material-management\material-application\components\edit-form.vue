<!-- 添加和修改的表单 -->
<template>
  <div>
    <div :style="{ maxHeight: '80vh', overflow: 'auto', marginBottom: '60px' }">
      <ele-card
        :flex-table="fixedHeight"
        :body-style="{ paddingBottom: '4px' }"
        :style="{
          minHeight: fixedHeight ? '180px' : void 0,
          marginBottom: fixedHeight ? '10px' : void 0
        }"
        header="基本信息"
      >
        <pro-form
          ref="formRef"
          :model="form"
          :items="items"
          :rules="rules"
          :footer="false"
          :label-width="120"
          :grid="{ span: 8 }"
          @updateValue="setFieldValue"
        />
      </ele-card>
      <ele-card
        :flex-table="fixedHeight"
        :body-style="{ paddingBottom: '20px' }"
        :style="{
          minHeight: fixedHeight ? '180px' : void 0,
          marginBottom: fixedHeight ? '10px' : void 0
        }"
        header="耗材包信息"
        bordered
      >
        <template #extra>
          <el-button type="primary" plain @click="addPackage('packageType')"
            >添加耗材包</el-button
          >
        </template>
        <ele-data-table row-key="userId" :columns="columns" :data="datasource">
          <template #quantity="{ row }">
            <el-input-number :min="1" v-model="row.quantity" />
          </template>
          <template #action="{ $index }">
            <el-link
              type="primary"
              underline="never"
              @click="delRow(datasource, $index)"
              >删除</el-link
            >
          </template>
        </ele-data-table>
      </ele-card>
      <ele-card
        :flex-table="fixedHeight"
        :body-style="{ paddingBottom: '20px' }"
        :style="{
          minHeight: fixedHeight ? '180px' : void 0,
          marginBottom: fixedHeight ? '10px' : void 0
        }"
        header="散装耗材信息"
        bordered
      >
        <template #extra>
          <el-button type="primary" plain @click="addPackage('bulkpackageType')"
            >添加散装耗材</el-button
          >
        </template>
        <ele-data-table
          row-key="userId"
          :columns="columns1"
          :data="datasource1"
        >
          <template #quantity="{ row }">
            <el-input-number
              :min="1"
              v-model="row.quantity"
              style="width: 100%"
            />
          </template>
          <template #spareQuantity="{ row }">
            <el-input-number
              :min="1"
              v-model="row.spareQuantity"
              style="width: 100%"
            />
          </template>
          <template #action>
            <el-link
              type="primary"
              underline="never"
              @click="delRow(datasource1, $index)"
              >删除</el-link
            >
          </template>
        </ele-data-table>
      </ele-card>
      <ele-card
        :flex-table="fixedHeight"
        :body-style="{ paddingBottom: '20px' }"
        :style="{
          minHeight: fixedHeight ? '180px' : void 0,
          marginBottom: '10px'
        }"
        header="试剂信息"
        bordered
      >
        <template #extra>
          <el-button type="primary" plain @click="addPackage('reagentType')"
            >添加试剂</el-button
          >
        </template>
        <ele-data-table
          row-key="userId"
          :columns="columns2"
          :data="datasource2"
        >
          <template #quantity="{ row }">
            <el-input-number :min="1" v-model="row.quantity" />
          </template>
          <template #action>
            <el-link
              type="primary"
              underline="never"
              @click="delRow(datasource2, $index)"
              >删除</el-link
            >
          </template>
        </ele-data-table>
      </ele-card>
    </div>

    <div class="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </div>
    <packageTableDialog
      v-model="packageVisibleModal"
      :type="type"
      @changeSelection="changeSelection"
    />
  </div>
</template>

<script setup>
  import ProForm from '@/components/ProForm/index.vue';
  import packageTableDialog from '../handleModal/packageTableDialog.vue';
  import { ref, reactive, watch } from 'vue';
  import { phoneReg } from 'ele-admin-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import {
    selectHospitalList,
    materialApplyAdd
  } from '@/api/project/material-management/material-application';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const { analyseProjectNumber, schemeNumber, projectName } =
    userStore.projectInfo;

  const emit = defineEmits(['close', 'success']);

  const fixedHeight = ref(true);
  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    analyseProjectNumber: analyseProjectNumber,
    schemeNumber: schemeNumber,
    projectName: projectName,
    hospitalId: '',
    hospitalCode: '',
    recipientName: '',
    phone: '',
    deliverDate: '',
    address: '',
    remark: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '分析项目编号',
      prop: 'analyseProjectNumber',
      type: 'text'
    },
    {
      label: '试验方案编号',
      prop: 'schemeNumber',
      type: 'text'
    },
    {
      label: '项目名称',
      prop: 'projectName',
      type: 'ellipsis'
    },
    {
      label: '研究机构',
      prop: 'hospitalId',
      type: 'select',
      options: [],
      required: true
    },
    {
      label: '机构代号',
      prop: 'hospitalCode',
      type: 'input',
      props: { disabled: true }
    },
    {
      label: '收件人姓名',
      prop: 'recipientName',
      type: 'input',
      required: true
    },
    {
      label: '电话',
      prop: 'phone',
      type: 'input',
      required: true
    },
    {
      label: '期望送达日期',
      prop: 'deliverDate',
      type: 'date',
      required: true
    },
    {
      label: '地址',
      prop: 'address',
      type: 'input',
      colProps: {
        span: 24
      }
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'input',
      colProps: {
        span: 24
      }
    }
  ]);

  const rules = reactive({
    phone: [
      {
        pattern: phoneReg,
        message: '手机号格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });
  // 耗材包信息列表配置
  const columns = ref([
    {
      label: '耗材包编号',
      prop: 'consumablesNumber',
      align: 'center'
    },
    {
      label: '耗材包名称',
      prop: 'consumablesName',
      align: 'center'
    },
    {
      label: '耗材包备注',
      prop: 'remark',
      align: 'center'
    },
    {
      label: '申请数量',
      prop: 'quantity',
      slot: 'quantity',
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 100,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  // 耗材包信息数据
  const datasource = ref([]);

  // 散装耗材信息列表配置
  const columns1 = ref([
    {
      type: 'index',
      columnKey: 'index',
      label: '耗材编号',
      width: 100,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材名称',
      prop: 'consumableName',
      align: 'center'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize',
      align: 'center'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial',
      align: 'center'
    },
    {
      label: '品牌',
      prop: 'consumableBrand',
      align: 'center'
    },
    {
      label: '理论申请数量',
      prop: 'quantity',
      slot: 'quantity',
      align: 'center'
    },
    {
      label: '备用申请数量',
      prop: 'spareQuantity',
      slot: 'spareQuantity',
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 100,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  // 散装耗材信息数据
  const datasource1 = ref([]);

  // 试剂信息列表配置
  const columns2 = ref([
    {
      label: '试剂编号',
      prop: 'bulkReagentNumber',
      align: 'center'
    },
    {
      label: '试剂名称',
      prop: 'reagentName',
      align: 'center'
    },
    {
      label: '保存条件',
      prop: 'storageCondition',
      align: 'center'
    },
    {
      label: '备注',
      prop: 'remark',
      align: 'center'
    },
    {
      label: '申请数量',
      prop: 'quantity',
      slot: 'quantity',
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 100,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  //试剂信息数据
  const datasource2 = ref([]);

  //添加耗材包,散装耗材,散装试剂
  const packageVisibleModal = ref(false);
  const type = ref('');
  const addPackage = (typeName) => {
    packageVisibleModal.value = true;
    type.value = typeName;
  };
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const list = datasource.value.filter((el) => !el.quantity);
      console.log(list, 'ooooo');
      if (list.length !== 0)
        return EleMessage.warning('耗材包申请数量不能为空！');
      const list1 = datasource1.value.filter(
        (el) => !el.quantity || !el.spareQuantity
      );
      if (list1.length !== 0)
        return EleMessage.warning(
          '散装耗材理论申请数量和备用申请数量不能为空！'
        );
      const list2 = datasource2.value.filter((el) => !el.quantity);
      if (list2.length !== 0)
        return EleMessage.warning('试剂申请数量不能为空！');
      loading.value = true;
      const params = {
        projectId: userStore.projectId,
        ...form,
        consumablesAddBoList: datasource.value,
        bulkConsumablesAddBoList: datasource1.value,
        bulkReagentAddBoList: datasource2.value
      };
      materialApplyAdd(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success('操作成功！');
          emit('success');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    resetFields();
    setTimeout(() => {
      formRef.value?.clearValidate?.();
    }, 0);
    emit('close');
    datasource.value = [];
    datasource1.value = [];
    datasource2.value = [];
  };

  // const getProjectInfo = () => {
  //   assignFields({
  //     ...userStore.projectInfo
  //   });
  // };
  const hospitalList = ref([]);
  const getSelectHospitalList = async () => {
    const res = await selectHospitalList({ projectId: userStore.projectId });
    if (res.data.length !== 0) {
      hospitalList.value = res.data.map((el) => {
        return {
          label: el.hospitalName,
          value: el.hospitalId,
          ...el
        };
      });
    }
    setPropItem('hospitalId', hospitalList.value);
  };
  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };
  // getProjectInfo();
  getSelectHospitalList();
  watch(
    () => form.hospitalId,
    () => {
      if (form.hospitalId) {
        const hospitalCode = hospitalList.value.filter(
          (el) => el.hospitalId == form.hospitalId
        )[0].hospitalCode;
        assignFields({ ...form, hospitalCode: hospitalCode });
      }
    }
  );

  const changeSelection = (info) => {
    if (info.type == 'packageType') {
      datasource.value = info.data;
    } else if (info.type == 'bulkpackageType') {
      datasource1.value = info.data;
    } else {
      datasource2.value = info.data;
    }
  };

  const delRow = (list, index) => {
    list.splice(index, 1);
  };
</script>
<style lang="scss" scoped>
  .footer {
    position: absolute;
    bottom: 20px;
    right: 30px;
    z-index: 1000;
    background: #fff;
  }
</style>
