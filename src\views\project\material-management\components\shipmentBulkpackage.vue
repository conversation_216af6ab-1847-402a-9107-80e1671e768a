<template>
  <div style="margin-bottom: 30px">
    <div style="font-weight: bold">散装耗材发运内容</div>
    <ele-pro-table
      row-key="userId"
      :columns="columns"
      :datasource="data"
      :pagination="false"
      border
    >
    </ele-pro-table>
  </div>
</template>
<script setup>
  import { ref } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '理论+备用申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '批号',
      prop: 'batchNumber'
    },
    {
      label: '本次发运数量',
      prop: 'quantity'
    },
    {
      label: '有效期至',
      prop: 'endDate'
    }
  ]);
</script>
