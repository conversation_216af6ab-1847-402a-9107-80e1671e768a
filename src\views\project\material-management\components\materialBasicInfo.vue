<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '180px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="基础信息"
    bordered
  >
    <template #extra>
      <el-tag type="primary" plain>{{ statusName }}</el-tag>
    </template>
    <el-descriptions>
      <el-descriptions-item label="项目名称：" :span="3">{{
        data.projectName
      }}</el-descriptions-item>
      <el-descriptions-item label="分析项目编号：">{{
        data.analyseProjectNumber
      }}</el-descriptions-item>
      <el-descriptions-item label="试验方案编号：">{{
        data.schemeNumber
      }}</el-descriptions-item>

      <el-descriptions-item label="研究机构：">{{
        data.hospitalName
      }}</el-descriptions-item>
      <el-descriptions-item label="机构代号：">{{
        data.hospitalCode
      }}</el-descriptions-item>
      <el-descriptions-item label="收件人姓名：">{{
        data.recipientName
      }}</el-descriptions-item>
      <el-descriptions-item label="电话：">{{
        data.phone
      }}</el-descriptions-item>
      <el-descriptions-item label="期望送达日期：">{{
        data.deliverDate
      }}</el-descriptions-item>
      <el-descriptions-item label="地址：">{{
        data.address
      }}</el-descriptions-item>
      <el-descriptions-item label="备注：">{{
        data.remark
      }}</el-descriptions-item>
    </el-descriptions>
  </ele-card>
</template>
<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const statusObj = {
    0: '待审核',
    1: '已通过',
    2: '已撤销'
  };
  const statusName = computed(() => {
    return props.data?.auditStatus ? statusObj[props.data.auditStatus] : '';
  });
</script>
<style scoped lang="scss">
  :deep .el-descriptions__label {
    margin-right: 5px !important;
  }
</style>
