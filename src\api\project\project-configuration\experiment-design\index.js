import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

// ==================== 实验设计通用 API ====================

// 查询设计类型
export async function getSampleDesignType(projectId) {
  const res = await request.get(`/sample/designType/${projectId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增设计类型
export async function addSampleDesignType(data) {
  const res = await request.post('/sample/designType/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 编辑设计类型
export async function updateSampleDesignType(data) {
  const res = await request.post('/sample/designType/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 确认设计类型
export async function confirmSampleDesignType(params) {
  const res = await request.get(
    `/sample/designType/confirm/${params.projectId}`
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 样本组相关 API ====================

// 获取样本组列表
export async function getSampleGroupList(projectId) {
  const res = await request.get(
    `/sample/designType/getTrailGroup/${projectId}`
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增样本组
export async function addSampleGroup(data) {
  const res = await request.post('/sample/designType/addTrialGroup', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 编辑样本组
export async function editSampleGroup(data) {
  const res = await request.post('/sample/designType/editTrailGroup', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 删除样本组
export async function deleteSampleGroup(id) {
  const res = await request.delete(`/sample/designType/removeTrailGroup/${id}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 采样点相关 API ====================

// 获取采样点列表 (获取当前组别的采样点)
export async function getSamplePointList(id) {
  const res = await request.get(`/sample/designType/getSamplePoint/${id}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增采样点
export async function addSamplePoint(data) {
  const res = await request.post('/sample/designType/addSamplePoint', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增受试者数量
export async function addSubjectNum(data) {
  const res = await request.post('/sample/designType/addSubjectNum', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 编辑采样点
export async function editSamplePoint(data) {
  const res = await request.post('/sample/designType/editSamplePoint', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 删除采样点
export async function deleteSamplePoint(id) {
  const res = await request.delete(
    `/sample/designType/removeSamplePoint/${id}`
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 样本管相关 API ====================

// 获取样本管列表
export async function getSampleTubeList(id) {
  const res = await request.get(`/sample/designType/getSampleTube/${id}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增样本管
export async function addSampleTube(data) {
  const res = await request.post('/sample/designType/addSampleTube', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 批量批量新增样本管 /sample/designType/batchAddSampleTube
export async function addSampleTubeBatch(data) {
  const res = await request.post('/sample/designType/batchAddSampleTube', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 编辑样本管
export async function editSampleTube(data) {
  const res = await request.post('/sample/designType/editSampleTube', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 删除样本管
export async function deleteSampleTube(ids) {
  const res = await request.delete(
    `/sample/designType/removeSampleTube/${ids}`
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 样本详情相关 API ====================

// 生成样本标签
export async function genSampleLabel(id) {
  const res = await request.get(`/sample/designType/genSampleLabel/${id}`);
  return res.data;
}

// 获取样本标签列表
export async function getSampleLabelList(params) {
  const res = await request.get('/sample/designType/sampleLabelList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 获取样本标签详情
export async function getSampleLabelInfo(sysId) {
  const res = await request.get(`/sample/designType/getSampleLabel/${sysId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出样本标签
export async function exportSampleLabel(params) {
  const res = await request({
    url: '/sample/designType/exportSampleLabel',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `sample_label_${Date.now()}.xlsx`);
}
