<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '180px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <div style="margin-bottom: 15px">
        <el-link type="primary" @click="goBack" underline="false"
          >问题沟通</el-link
        >
        > 详情</div
      >
      <div class="detail-body">
        <div class="title"> 问题来源</div>
        <el-row>
          <el-col :span="8"
            ><p>分析项目编号：{{ detailInfo.analyseProjectNumber }}</p></el-col
          >
          <el-col :span="8"
            ><p>试验方案编号：{{ detailInfo.schemeNumber }}</p></el-col
          >
          <el-col :span="24"
            ><p>项目名称：{{ detailInfo.projectName }}</p></el-col
          >
        </el-row>
        <!-- 来源物资 -->
        <el-row v-if="detailInfo.problemSourceMaterials">
          <el-col :span="8"
            ><p
              >运输单位：{{
                detailInfo.problemSourceMaterials.logisticsCompany
              }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >运输单号：{{ detailInfo.problemSourceMaterials.bookingNote }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发件单位：{{ detailInfo.problemSourceMaterials.hospitalName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >收件单位：{{
                detailInfo.problemSourceMaterials.receivingUnit
              }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发运人：{{ detailInfo.problemSourceMaterials.shipperName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发运日期：{{
                detailInfo.problemSourceMaterials.forwardingDate
              }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收人：{{ detailInfo.problemSourceMaterials.receiveName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收日期：{{ detailInfo.problemSourceMaterials.receiveTime }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收备注：{{
                detailInfo.problemSourceMaterials.receiveRemark
              }}</p
            ></el-col
          >
        </el-row>
        <!-- 来源样本 -->
        <el-row v-if="detailInfo.problemSourceSample">
          <el-col :span="8"
            ><p
              >运输单位：{{
                detailInfo.problemSourceSample.logisticsCompany
              }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >运输单号：{{ detailInfo.problemSourceSample.bookingNote }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发件单位：{{ detailInfo.problemSourceSample.hospitalName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >收件单位：{{ detailInfo.problemSourceSample.receivingUnit }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >样本总数：{{ detailInfo.problemSourceSample.sampleNumber }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发运人：{{ detailInfo.problemSourceSample.shipperName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >发运日期：{{ detailInfo.problemSourceSample.forwardingDate }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收人：{{ detailInfo.problemSourceSample.receiveName }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收日期：{{ detailInfo.problemSourceSample.receiveTime }}</p
            ></el-col
          >
          <el-col :span="8"
            ><p
              >接收备注：{{ detailInfo.problemSourceSample.receiveRemark }}</p
            ></el-col
          >
        </el-row>
        <hr class="separator" />
        <div>
          <el-row>
            <el-col :span="24"
              ><p>问题描述：{{ detailInfo.problemDescribe }}</p></el-col
            >
            <el-col
              :span="24"
              v-if="detailInfo.sysFileList && detailInfo.sysFileList.length > 0"
              ><p v-for="(c, cIndex) in detailInfo.sysFileList" :key="cIndex"
                ><span>{{ cIndex == 0 ? '附件：' : '' }}</span>
                <el-link
                  type="primary"
                  :underline="false"
                  :class="{ 'file-content': cIndex > 0 }"
                  @click="handleDownload(c)"
                  >{{ c.originalName }}</el-link
                >
              </p></el-col
            >
            <el-col :span="8"
              ><p>创建人：{{ detailInfo.createBy }}</p></el-col
            >

            <el-col :span="8"
              ><p>创建时间：{{ detailInfo.createTime }}</p></el-col
            >

            <el-col :span="8"
              ><p>问题超期日：{{ detailInfo.problemOverdueDate }}</p></el-col
            >
            <el-col :span="8" v-if="detailInfo.closeBy"
              ><p>关闭人：{{ detailInfo.closeBy }}</p></el-col
            >

            <el-col :span="8" v-if="detailInfo.closeBy"
              ><p>关闭时间：{{ detailInfo.closeTime }}</p></el-col
            >

            <el-col :span="8" v-if="detailInfo.closeBy"
              ><p>关闭备注：{{ detailInfo.closeRemark }}</p></el-col
            >
          </el-row>
          <p
            class="title"
            style="padding-left: 20px"
            v-if="detailInfo.replyList && detailInfo.replyList.length > 0"
          >
            评论记录</p
          >
          <el-timeline
            class="msg-record"
            v-if="detailInfo.replyList && detailInfo.replyList.length > 0"
          >
            <el-timeline-item
              v-for="(item, index) in detailInfo.replyList"
              :key="index"
            >
              <p style="padding-left: 40px">
                {{ item.replyUserName
                }}{{ item.replyUserRole ? '(' + item.replyUserRole + ')：' : ''
                }}{{ item.replyDescribe }}
                <span style="color: #999; margin-left: 20px">{{
                  item.createTime
                }}</span>
              </p>
              <p
                style="padding-left: 40px"
                v-for="(c, cIndex) in item.sysFileList"
                :key="cIndex"
              >
                <span>{{ cIndex == 0 ? '附件：' : '' }}</span
                ><el-link
                  :class="{ 'file-content': cIndex > 0 }"
                  type="primary"
                  :underline="false"
                  @click="handleDownload(c)"
                  >{{ c.originalName }}</el-link
                >
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </ele-card>
    <problem-open
      ref="problemOpenRef"
      v-model="visibleModal"
      :type="typeQuestion"
      :data="detailInfo"
      @done="getInitData"
    />
    <ele-bottom-bar teleported>
      <template #extra>
        <el-button
          type="primary"
          @click="exportData"
          v-permission="'project:problem:exportDetail'"
          >导出</el-button
        >
        <el-button
          type="primary"
          plain
          v-if="detailInfo.problemStatus == 0"
          @click="closeProblem(1)"
          v-permission="'project:problem:close'"
          >关闭问题</el-button
        >
        <el-button
          type="primary"
          plain
          v-if="detailInfo.problemStatus == 0"
          v-permission="'project:problem:reply'"
          @click="closeProblem(2)"
          >回复问题</el-button
        >
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
  import { ref, nextTick, watch, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import problemOpen from './problem-open.vue';
  import {
    getProblemDetail,
    exportProblemList,
    exportProblem
  } from '@/api/problem';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import download from '@/utils/download';
  import { Base64 } from 'js-base64';
  const fixedHeight = ref(true);
  const router = useRouter();
  const route = useRoute();
  const visibleModal = ref(false);
  const typeQuestion = ref('');
  const detailInfo = ref({});
  // 定义返回上一页的方法
  const goBack = () => {
    router.go(-1);
  };
  const closeProblem = (type) => {
    typeQuestion.value = type;
    visibleModal.value = true;
  };
  // watch(
  //   () => detailInfo.value,
  //   (newVal) => {
  //     if (newVal) {
  //       getInitData();
  //     }
  //   }
  // );

  const getInitData = () => {
    if (route.query.problemId) {
      getProblemDetail(route.query.problemId).then((res) => {
        console.log(res);
        if (res.code == 200) {
          detailInfo.value = res.data;
        }
      });
    }
  };
  nextTick(() => {
    console.log(route.query.problemId);
    getInitData();
  });
  const handleDownload = (row) => {
    // download.oss(row.ossId); // 下载文件
    var url = row.url;
    window.open(
      'https://kkfile.520gcp.com/onlinePreview?url=' +
        encodeURIComponent(Base64.encode(url))
    );
  };
  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    exportProblem({ problemId: route.query.problemId, page: 1, limit: 10 })
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.message);
      });
  };
</script>

<style lang="scss" scoped>
  .detail-body {
    border: 1px solid #eee;
    padding: 10px;
    .title {
      font-weight: 900;
      font-size: 16px;
    }
  }
  .separator {
    border: none;
    height: 1px;
    background-color: #eee;
    margin: 20px 0;
  }
  // .footer-btn {
  //   padding: 20px;
  //   border: 1px solid #eee;
  //   border-top: none;
  //   text-align: right;
  // }
  .file-content {
    margin-left: 40px;
  }
  .msg-record {
    max-height: 300px;
    overflow-y: auto;
  }
</style>
