<template>
  <EleTableSearch
    :model="form"
    :items="items"
    filterRowToMore="1"
    :show-label="false"
    @updateValue="updateFormValue"
    @submit="reload($event, 1)"
    @reset="reload($event, 1)"
    style="margin-top: 20px"
  >
    <template #toolbar>
      <el-button
        class="ele-btn-icon"
        type="primary"
        :disabled="!selections.length"
        @click="batchPrint()"
      >
        批量打印
      </el-button>
      <el-button
        class="ele-btn-icon"
        @click="handleExportSampleLabel()"
        v-permission="'sample:sampleTubeLabel:export'"
      >
        导出
      </el-button>
    </template>
  </EleTableSearch>
  <!-- 表格 -->
  <ele-pro-table
    ref="tableRef"
    row-key="systemId"
    :columns="columns"
    :datasource="datasource"
    v-model:current="current"
    v-model:selections="selections"
    :show-overflow-tooltip="true"
    cache-key="sampleLabelList"
    :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
    :default-sort="{ prop: 'createTime', order: 'ascending' }"
    :footer-style="{ paddingBottom: '12px' }"
    style="padding-bottom: 0; margin-top: -5px"
  >
    <!-- 状态列 -->
    <template #status="{ row }">
      <el-tag v-if="row.status === '0'" type="success">已生成</el-tag>
      <el-tag type="warning" v-else-if="row.status === '1'">已作废</el-tag>
    </template>
    <!-- 操作列 -->
    <template #action="{ row }">
      <el-link
        type="primary"
        underline="never"
        @click.stop="openSinglePrint(row)"
      >
        打印
      </el-link>
    </template>
  </ele-pro-table>
  <SamplePrint v-model="visibleModal" :data="printData" />
</template>

<script setup>
  import { ref, reactive, computed, inject } from 'vue';
  import {
    getSampleLabelList,
    exportSampleLabel
  } from '@/api/project/project-configuration/experiment-design';
  import { useDictData } from '@/utils/use-dict-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import SamplePrint from '../handleModal/sample-print-dialog.vue';

  const projectInfo = inject('projectInfo');

  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');
  const projectId = computed(() => projectInfo?.projectId || '');

  /** 表格实例 */
  const tableRef = ref(null);
  // 状态（0代表已生成 1代表已作废）
  const statusOptions = [
    { label: '已生成', value: '0' },
    { label: '已作废', value: '1' }
  ];

  const [sampleTypeDicts] = useDictData(['sample_type']);

  /** 表单数据 */
  const form = reactive({
    projectId: projectId.value,
    designType: designType.value,
    status: '',
    systemId: '',
    groupName: '',
    visitNumber: '',
    studyTime: '',
    samplePointTime: '',
    sampleNumber: '',
    testingIndex: '',
    sampleType: '',
    packagingNumber: '',
    subjectNumber: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'select',
      label: '状态',
      prop: 'status',
      options: statusOptions
    },
    {
      type: 'input',
      label: '样本ID',
      prop: 'systemId',
      props: {
        labelWidth: '120px'
      }
    },
    {
      type: 'input',
      label: '试验组别',
      prop: 'groupName'
    },
    {
      type: 'input',
      label: '访视号',
      prop: 'visitNumber',
      vIf: designType.value === '3'
    },
    {
      type: 'input',
      label: '研究时间',
      prop: 'studyTime',
      vIf: designType.value === '3'
    },
    {
      type: 'input',
      label: '采样时间',
      prop: 'samplePointTime'
    },
    {
      type: 'input',
      label: '样本序号',
      prop: 'sampleNumber'
    },
    {
      type: 'input',
      label: '检测指标',
      prop: 'testingIndex'
    },
    {
      type: 'dictSelect',
      label: '样本类型',
      prop: 'sampleType',
      props: {
        code: 'sample_type'
      }
    },
    {
      type: 'input',
      label: '分装号',
      prop: 'packagingNumber'
    },
    {
      type: 'input',
      label: '受试者编号',
      prop: 'subjectNumber',
      vIf: designType.value !== '3'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        reserveSelection: true,
        fixed: 'left'
      },
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        columnKey: 'status',
        prop: 'status',
        label: '状态',
        width: 100,
        align: 'center',
        slot: 'status'
      },
      {
        prop: 'systemId',
        label: '样本ID',
        minWidth: 150
      },
      {
        label: '试验组别',
        prop: 'groupName',
        minWidth: 110
      },
      /** 类型为2 的列*/
      {
        label: '周期',
        prop: 'cycleName',
        minWidth: 80,
        hideInTable: designType.value === '3'
      },
      {
        prop: 'samplePointYear',
        label: '年',
        minWidth: 80,
        hideInTable: designType.value !== '2'
      },
      {
        prop: 'samplePointMonth',
        label: '月',
        minWidth: 80,
        hideInTable: designType.value !== '2'
      },
      {
        prop: 'samplePointWeek',
        label: '周',
        minWidth: 80,
        hideInTable: designType.value !== '2'
      },
      {
        prop: 'samplePointDay',
        label: '日',
        minWidth: 80,
        hideInTable: designType.value !== '2'
      },
      /** 类型为3 的列*/
      {
        label: '访视号',
        prop: 'visitNumber',
        minWidth: 110,
        hideInTable: designType.value !== '3'
      },
      {
        label: '研究时间',
        prop: 'studyTime',
        minWidth: 110,
        hideInTable: designType.value !== '3'
      },
      /** 类型为1、2、3 的列*/
      {
        label: '采样时间点',
        prop: 'samplePointTime',
        minWidth: 110
      },
      // 公用字段
      {
        label: '样本序号',
        prop: 'sampleNumber',
        minWidth: 90
      },
      {
        label: '检测指标',
        prop: 'testingIndex',
        minWidth: 110
      },
      {
        label: '样本类型',
        prop: 'sampleType',
        formatter: (row) => {
          return sampleTypeDicts.value.find(
            (d) => d.dictValue === row.sampleType
          )?.dictLabel;
        },
        minWidth: 150
      },
      {
        label: '分装号',
        prop: 'packagingNumber',
        minWidth: 80
      },
      {
        label: '受试者编号',
        prop: 'subjectNumber',
        minWidth: 110,
        hideInTable: designType.value === '3'
      },
      {
        label: '生成日期',
        prop: 'createDate',
        minWidth: 110,
        sortable: 'custom'
      },
      {
        columnKey: 'action',
        label: '操作',
        minWidth: 100,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    const res = await getSampleLabelList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
    res.records = res.records.map((item) => {
      return {
        ...item,
        ...JSON.parse(item.content)
      };
    });
    return res;
  };
  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };
  const batchPrint = () => {
    printData.value = selections.value;
    visibleModal.value = true;
  };

  // 标签打印
  const visibleModal = ref(false);
  const printData = ref([]);
  // 单个打印
  const openSinglePrint = (row) => {
    console.log(row);
    printData.value = [row];
    visibleModal.value = true;
  };

  /** 导出数据 */
  const handleExportSampleLabel = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportSampleLabel({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  defineExpose({
    reload
  });
</script>
