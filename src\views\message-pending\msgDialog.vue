<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1100"
    v-model="visible"
    title="消息通知列表"
    @open="handleOpen"
    append-to-body
    align-center
  >
    <div>
      <el-form label-width="88px" @keyup.enter="search" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="消息内容">
              <el-input
                clearable
                v-model.trim="messContent"
                placeholder="请输入消息内容"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="时间">
              <el-date-picker
                v-model="dateTimeList"
                type="daterange"
                range-separator="-"
                value-format="YYYY-MM-DD"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="msg-wrapper">
        <!-- 将v-for数据源修改为分页后的列表 -->
        <div class="msg-item" v-for="(item, index) in msgList" :key="index">
          <!-- <el-tooltip :content="item.title" placement="top" class="item"> -->
          <p class="msg-title">{{ item.messContent }}</p>
          <!-- </el-tooltip> -->
          <p class="sub-title">
            <span style="color: #909399">{{ item.createTime }}</span>
            <el-link
              type="primary"
              underline="never"
              @click.stop="openSee(item)"
              >去查看></el-link
            >
          </p>
        </div>
      </div>
      <!-- 添加分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, onMounted, nextTick, computed } from 'vue';
  import { getProblemList } from '@/api/todo';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();

  const { push } = useRouter();
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const emit = defineEmits(['done']);
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const msgList = ref([]);
  const dateTimeList = ref([]);
  const messContent = ref('');

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };
  const search = () => {
    initData();
  };
  const reset = () => {
    messContent.value = '';
    dateTimeList.value = [];
    initData();
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    initData();
  };
  const initData = () => {
    let params = {
      page: currentPage.value,
      limit: pageSize.value,
      messContent: messContent.value,
      startTime: dateTimeList.value[0] || '',
      endTime: dateTimeList.value[1] || ''
    };
    getProblemList(params).then((res) => {
      if (res.code == 200) {
        msgList.value = res.records;
        total.value = res.total;
      }
    });
  };
  onMounted(() => {
    initData();
  });
  // 添加分页相关数据
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);

  // 分页事件处理
  const handleSizeChange = (val) => {
    pageSize.value = val;
    currentPage.value = 1; // 重置页码
    initData();
  };

  const handleCurrentChange = (val) => {
    currentPage.value = val;
    initData();
  };
  const openSee = (row) => {
    console.log(row);
    userStore.setUserInfoByProject(row.projectId, '/project-management');
    push({
      path: '/project/information',
      query: {
        id: row?.projectId
      }
    });
    handleCancel();
  };
</script>
<style lang="scss" scoped>
  .title {
    font-weight: 900;
    font-size: 16px;
  }
  .msg-wrapper {
    height: calc(100vh - 440px);
    overflow-y: auto;
    padding-bottom: 20px;
  }
  .msg-title {
    font-size: 14px;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .sub-title {
    display: flex;
    justify-content: space-between;
    padding-right: 5px;
  }
  .msg-item {
    border-bottom: 1px solid #e4e7ed;
  }
  .pagination-container {
    display: flex;
    margin-top: 20px;
    justify-content: center;
  }
</style>
