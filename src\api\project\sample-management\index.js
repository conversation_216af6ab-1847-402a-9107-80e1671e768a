import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

// ==================== 样本发运 API ====================

// 查询样本发运列表
export async function getSampleForwardingList(params) {
  const res = await request.get(`/sample/forwarding/list`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增样本发运
export async function addSampleForwarding(data) {
  const res = await request.post('/sample/forwarding/add', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出样本发运列表
export async function exportSampleForwarding(params) {
  const res = await request({
    url: '/sample/forwarding/export',
    method: 'POST',
    data: params ? toFormData(params) : null,
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `样本发运_${Date.now()}.xlsx`);
}

// 获取样本发运信息
export async function getSampleForwardingInfo(forwardingId) {
  const res = await request.get(`/sample/forwarding/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 撤销样本发运信息
export async function revokeSampleForwarding(forwardingId) {
  const res = await request.get(`/sample/forwarding/revoke/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 样本接收 API ====================

// 查询样本接收列表
export async function getSampleReceiveList(params) {
  const res = await request.get(`/sample/receive/list`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增样本接收
export async function addSampleReceive(data) {
  const res = await request.post('/sample/receive/add', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出样本接收列表
export async function exportSampleReceive(params) {
  const res = await request({
    url: '/sample/receive/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `样本接收列表_${Date.now()}.xlsx`);
}

// 获取样本接收信息
export async function getSampleReceiveInfo(forwardingId) {
  const res = await request.get(`/sample/receive/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 确认样本接收
export async function revokeSampleReceive(data) {
  const res = await request.post(`/sample/receive/confirm`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// ==================== 样本库存 API ====================

// 查询样本库存列表
export async function getSampleInventoryList(params) {
  const res = await request.get(`/sample/inventory/list`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 获取样本库存信息
export async function getSampleInventoryInfo(systemId) {
  const res = await request.get(`/sample/inventory/${systemId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出样本库存列表
export async function exportSampleInventory(params) {
  const res = await request({
    url: '/sample/inventory/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `样本明细统计_${Date.now()}.xlsx`);
}

// ==================== 样本处理 API ====================

// 查询样本处理列表
export async function getSampleHandleList(params) {
  const res = await request.get(`/sample/handle/list`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新增样本处理
export async function addSampleHandle(data) {
  const res = await request.post('/sample/handle/add', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 导出样本处理列表
export async function exportSampleHandle(params) {
  const res = await request({
    url: '/sample/handle/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `样本处理_${Date.now()}.xlsx`);
}

// 获取样本处理详细信息
export async function getSampleHandleInfo(forwardingId) {
  const res = await request.get(`/sample/handle/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 撤销样本处理信息
export async function revokeSampleHandle(forwardingId) {
  const res = await request.get(`/sample/handle/revoke/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
