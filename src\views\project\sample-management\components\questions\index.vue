<template>
  <div>
    <ele-modal
      form
      :width="700"
      title="新建问题"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <el-descriptions border label-width="100px">
        <el-descriptions-item label="运输单号：" :span="2">{{
          data.bookingNote
        }}</el-descriptions-item>
        <el-descriptions-item label="运输单位：" :span="2">{{
          data.logisticsCompany
        }}</el-descriptions-item>
        <el-descriptions-item label="发件单位：" :span="2">{{
          data.hospitalName
        }}</el-descriptions-item>
        <el-descriptions-item label="收件单位：" :span="2">
          阳光德美</el-descriptions-item
        >
      </el-descriptions>
      <el-divider />
      <pro-form
        ref="formRef"
        :model="form"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, inject, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { addProblem } from '@/api/problem';
  import { pageRoles } from '@/api/system/role';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    data: {
      type: Object,
      default: () => ({})
    }
  });

  const projectInfo = inject('projectInfo');

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    problemType: '2',
    answerRole: '',
    problemDescribe: '',
    sysFileIds: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '问题类型',
      prop: 'problemType',
      type: 'dictSelect',
      props: {
        code: 'problem_type',
        disabled: true
      }
    },
    {
      label: '回答角色',
      prop: 'answerRole',
      type: 'select',
      options: async () => {
        const res = await pageRoles({
          pageNum: 1,
          pageSize: 1000,
          isProblem: 1
        });
        return res.records.map((i) => {
          return {
            label: i.roleName,
            value: i.roleId
          };
        });
      },
      required: true
    },
    {
      label: '问题描述',
      prop: 'problemDescribe',
      type: 'textarea',
      props: {
        maxlength: 200,
        showWordLimit: true
      },
      required: true
    },
    {
      label: '附件上传',
      prop: 'sysFileIds',
      type: 'fileUpload',
      required: true
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const sysFileIds = JSON.parse(form.sysFileIds).map((item) => item.ossId);
      addProblem({
        ...form,
        problemSourceId: props.data.forwardingId,
        sysFileIds: sysFileIds.join(','),
        projectId: projectInfo.projectId
      })
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  watch(
    () => visible.value,
    (val) => {
      if (val) {
        formRef.value?.resetFields?.();
      }
    }
  );
</script>
