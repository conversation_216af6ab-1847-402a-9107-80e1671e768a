<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '100px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="审核信息"
    bordered
  >
    <el-empty
      v-if="data.auditStatus != '1'"
      description="暂无审核信息"
      height="100"
    />
    <el-descriptions v-else>
      <el-descriptions-item label="审核人：">{{
        data.auditName
      }}</el-descriptions-item>
      <el-descriptions-item label="审核通过时间：">{{
        data.auditTime
      }}</el-descriptions-item>
      <el-descriptions-item label="审核备注：">{{
        data.auditRemark
      }}</el-descriptions-item>
    </el-descriptions>
  </ele-card>
</template>
<script setup>
  import { ref } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
</script>
