<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="88px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="操作类型：">
            <el-select
              v-model="form.operationName"
              placeholder="请选择操作类型"
              clearable
            >
              <el-option label="新增" value="add" />
              <el-option label="修改" value="edit" />
              <el-option label="删除" value="delete" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="系统模块">
            <!-- <el-input
              clearable
              v-model.trim="form.title"
              placeholder="请输入"
            /> -->
            <!-- <dict-data
              code="object_name"
              v-model="form.objectName"
              placeholder="请选择"
            /> -->
            <!-- objectName -->
            <el-select
              v-model="form.objectName"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="item in getDicts()"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="操作人员">
            <el-input
              clearable
              v-model.trim="form.operName"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col> -->
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="操作时间">
            <el-date-picker
              unlink-panels
              type="daterange"
              v-model="dateRange"
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <!-- 放一个插槽 -->
            <slot name="exportData"></slot>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref,watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useDictData } from '@/utils/use-dict-data';
  //获取父组件参数
  const props = defineProps({
    /** 默认查询参数 */
    currentStatus: String
  });
  //监听currentStatus
  watch(
    () => props.currentStatus,
    (val) => {
      form.objectName = null;
    }
  );
  const emit = defineEmits(['search']);
  /** 字典数据 */
  const [objectName] = useDictData(['object_name']);
  /** 表单数据 */
  const [form, resetFields] = useFormData({
    title: '',
    operName: ''
  });
  const getDicts = () => {
    try {
      if (props.currentStatus == 'project') {
        return objectName.value.filter((val) => val.remark != '非项目');
      }
      return objectName.value.filter((val) => val.remark == '非项目');
    } catch (error) {
      return [];
    }
  };
  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      params: {
        beginTime: d1 ? `${d1} 00:00:00` : '',
        endTime: d2 ? `${d2} 23:59:59` : ''
      }
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
