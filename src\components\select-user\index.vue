<template>
  <el-select
    class="monitor-select-user"
    v-model="user"
    v-bind="$attrs"
    filterable
    clearable
    placeholder="请选择人员"
    :filter-method="filterUser"
  >
    <el-option
      v-for="item in userList"
      :key="item.memberId"
      :label="item.memberName"
      :value="item.memberId"
    />
  </el-select>
</template>

<script setup>
  import { ref } from 'vue';
  import { getMemberList } from '@/api/statistics';
  import { pinyin } from 'pinyin-pro';

  const user = defineModel('modelValue', { type: Number });
  const userList = ref([]);

  // 获取人员列表
  getMemberList().then((res) => {
    list.value = res;
    userList.value = res;
  });

  // 自定义过滤方法
  const list = ref([]);
  const filterUser = (query) => {
    if (!query) {
      userList.value = list.value;
      return;
    }
    userList.value = list.value.filter((item) => {
      const memberName = item.memberName;
      const pinyinName = pinyin(memberName, {
        pattern: 'first',
        type: 'array'
      })?.join(''); // 使用 pinyin-pro 转换拼音
      return (
        memberName.includes(query) || // 匹配中文
        pinyinName.includes(query.toLowerCase()) // 匹配拼音
      );
    });
  };
</script>

<style scoped></style>
