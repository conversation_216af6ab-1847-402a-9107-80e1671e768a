<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button v-permission="'material:stat:export'" plain @click="exportFun"> 导出 </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #consumablesName="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click="showMaterialItem(row)"
            >{{ row.consumablesName }}</el-link
          >
        </template>
      </ele-pro-table>
    </ele-card>
    <materialItemList
      v-if="materialItemVisible"
      v-model="materialItemVisible"
      :editData="editData"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getMaterialStatList,
    exportMaterialStat
  } from '@/api/statistics/materials';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import materialItemList from '../../project/handleModal/materialItemList.vue';

  defineOptions({ name: 'Materials' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    companyProjectNumber: '',
    consumablesName: '',
    hospitalName: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '公司项目编号',
      prop: 'companyProjectNumber'
    },
    {
      type: 'input',
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        align: 'center'
      },
      {
        prop: 'companyProjectNumber',
        label: '公司项目编号',
        align: 'center'
      },
      {
        prop: 'consumablesName',
        label: '耗材包名称',
        align: 'center',
        slot: 'consumablesName'
      },
      {
        prop: 'hospitalName',
        label: '机构名称',
        align: 'center'
      },
      {
        prop: 'prepareDate',
        label: '准备日期',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '寄出日期',
        align: 'center'
      },
      {
        prop: 'receiveDate',
        label: '接收日期',
        align: 'center'
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialStatList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(form);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportMaterialStat({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  const editData = ref(null);
  const materialItemVisible = ref(false);
  const showMaterialItem = (row) => {
    editData.value = row ?? null;
    materialItemVisible.value = true;
  };
</script>
