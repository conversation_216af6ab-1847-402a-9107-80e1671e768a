<template>
  <ele-page :flex-table="false">
    <ele-card
      :flex-table="true"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: '380px',
        marginBottom: '10px'
      }"
    >
      <ele-tabs
        v-model="active"
        :items="[
          { name: 'application', label: '申请单' },
          { name: 'ready', label: '准备清单' }
        ]"
      >
        <template #application>
          <div style="margin-top: 15px">
            <materialBasicInfo :data="basicInfo" />
            <auditInformation :data="basicInfo" />
            <materialPackageAudit :data="basicInfo" />
            <bulkpackageAudit :data="basicInfo" />
            <reagentAudit :data="basicInfo" />
          </div>
        </template>
        <template #ready>
          <div style="margin-top: 15px">
            <readyList :id="applyId" />
          </div>
        </template>
      </ele-tabs>
      <!-- <template #footer>
        <div style="text-align: right">
          <el-button type="primary" :loading="loading" @click="save">
            准备
          </el-button>
        </div>
      </template> -->
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, watch, reactive, unref } from 'vue';
  import materialBasicInfo from '../components/materialBasicInfo.vue';
  import auditInformation from '../components/auditInformation.vue';
  import materialPackageAudit from '../components/materialPackageAudit.vue';
  import bulkpackageAudit from '../components/bulkpackageAudit.vue';
  import reagentAudit from '../components/reagentAudit.vue';
  import readyList from '../components/readyList.vue';

  import { materialApplyDetails } from '@/api/project/material-management/material-application';

  import { useRouter } from 'vue-router';
  const { currentRoute, push } = useRouter();

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const active = ref('application');

  const basicInfo = reactive({});
  const getDetails = async (id) => {
    const { data } = await materialApplyDetails(id);
    Object.assign(basicInfo, data);
  };
  const applyId = ref('');
  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      applyId.value = query?.id || '';
      getDetails(query.id);
    },
    { immediate: true }
  );
</script>
