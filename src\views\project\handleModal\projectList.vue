<template>
  <div>
    <ele-modal
      form
      :width="1100"
      :title="title"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
      </EleTableSearch>
      <ele-pro-table
        ref="tableRef"
        row-key="projectId"
        :columns="columns"
        :datasource="datasource"
        :pagination="false"
      >
        <template #projectStatus="{ row }">
          <!-- 项目状态（1代表在研中 2代表暂停 3代表结束 4代表废弃 -->
          <el-tag v-if="row.projectStatus == 1" type="success">在研中</el-tag>
          <el-tag v-else-if="row.projectStatus == 2" type="info">暂停</el-tag>
          <el-tag v-else-if="row.projectStatus == 3" type="danger">结束</el-tag>
          <el-tag v-else-if="row.projectStatus == 4" type="warning"
            >废弃</el-tag
          >
        </template>
      </ele-pro-table>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { getOrganizationProjectList } from '@/api/statistics/institutions';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  const tableRef = ref(null);
  /** 表单数据 */
  const form = reactive({
    analyseProjectNumber: '',
    projectName: '',
    projectLeader: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    }
    /*{
      type: 'input',
      label: '项目负责人',
      prop: 'projectLeader'
    }*/
  ]);
  // 耗材包列表
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '项目ID',
      prop: 'systemId',
      width: 80
    },
    {
      label: '分析项目编号',
      prop: 'analyseProjectNumber',
      width: 120
    },
    {
      label: '试验方案编号',
      prop: 'schemeNumber',
      width: 120
    },
    {
      label: '项目名称',
      prop: 'projectName'
    },
    {
      label: '项目状态',
      prop: 'projectStatus',
      slot: 'projectStatus',
      width: 100
    }
  ]);

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(where);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };
  const tableData = ref([]);
  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    const { data } = await getOrganizationProjectList({
      ...where,
      ...orders,
      ...filters,
      ...pages,
      organizationId: props.editData.organizationId
    });
    tableData.value = data ?? [];
    return {
      records: data
    };
  };

  const title = computed(() => {
    return `${props.editData.organizationName}-关联的项目(${tableData.value.length})`;
  });
</script>
