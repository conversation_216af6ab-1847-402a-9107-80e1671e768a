<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
          v-permission="'inventoryRecord:inventoryRecord:add'"
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit"
          >
            出入库
          </el-button>
          <el-button v-permission="'inventoryRecord:inventoryRecord:export'" plain @click="exportFun"> 导出 </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        cache-key="inventory-records"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="['columns']"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #type="{ row }">
          <dict-data code="inventory_type" type="tag" :model-value="row.type" />
        </template>
        <template #consumableType="{ row }">
          <dict-data
            code="consumable_type"
            type="tag"
            :model-value="row.consumableType"
          />
        </template>
      </ele-pro-table>
      <handleModal v-model="visibleModal" @success="reload" />
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getInventoryRecordList,
    exportInventoryRecord
  } from '@/api/consumables-management/inventory-records';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import handleModal from './handleModal/index.vue';

  defineOptions({ name: 'InventoryRecords' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    type: '',
    consumableName: '',
    batchNumber: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'select',
      label: '出入库类型',
      prop: 'type',
      type: 'dictSelect',
      props: { code: 'inventory_type' }
    },
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      type: 'input',
      label: '批号',
      prop: 'batchNumber'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'type',
        label: '出入库类型',
        slot: 'type',
        align: 'center'
      },
      {
        prop: 'systemId',
        label: '耗材ID',
        align: 'center'
      },
      {
        prop: 'consumableType',
        label: '耗材类型',
        align: 'center',
        slot: 'consumableType'
      },
      {
        prop: 'consumableName',
        label: '耗材名称',
        align: 'center'
      },
      {
        prop: 'consumableSize',
        label: '规格尺寸',
        align: 'center'
      },
      {
        prop: 'consumableMaterial',
        label: '颜色材质',
        align: 'center'
      },
      {
        prop: 'consumableBrand',
        label: '品牌',
        align: 'center'
      },
      {
        prop: 'batchNumber',
        label: '批号',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '有效期至',
        align: 'center',
        width: 150
      },
      {
        prop: 'quantity',
        label: '数量',
        align: 'center',
        width: 100
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center'
      },
      {
        prop: 'createName',
        label: '操作人',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'createTime',
        label: '操作时间',
        align: 'center',
        hideInTable: true
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getInventoryRecordList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  const openDetail = (row) => {
    push({
      path: '/',
      query: {
        id: row?.userId
      }
    });
  };

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);
  const openEdit = (row) => {
    visibleModal.value = true;
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportInventoryRecord({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
