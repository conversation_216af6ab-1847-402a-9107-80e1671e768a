<template>
  <ele-page :flex-table="false">
    <ele-card>
      <materialBasicInfo :data="basicInfo" />
      <auditInformation :data="basicInfo" />
      <materialPackageAudit :data="basicInfo" />
      <bulkpackageAudit :data="basicInfo" />
      <reagentAudit :data="basicInfo" />
      <template #footer>
        <div style="text-align: right">
          <el-button type="primary" :loading="loading" @click="audit">
            审核
          </el-button>
        </div>
      </template>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, watch, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import materialBasicInfo from '../components/materialBasicInfo.vue';
  import auditInformation from '../components/auditInformation.vue';
  import materialPackageAudit from '../components/materialPackageAudit.vue';
  import bulkpackageAudit from '../components/bulkpackageAudit.vue';
  import reagentAudit from '../components/reagentAudit.vue';
  import { materialApplyDetails } from '@/api/project/material-management/material-application';
  import { materialApplyAudit } from '@/api/project/material-management/material-audit';

  import { useRouter } from 'vue-router';
  const { currentRoute, push } = useRouter();

  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const basicInfo = reactive({});
  const getDetails = async (id) => {
    const { data } = await materialApplyDetails(id);
    Object.assign(basicInfo, data);
  };
  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      getDetails(query.id);
    },
    { immediate: true }
  );
  /** 审核 */
  const audit = async (row) => {
    ElMessageBox.prompt(`是否确认审核通过`, '确认审核通过', {
      inputErrorMessage: '审核备注',
      draggable: true
    })
      .then(({ value }) => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        const params = {
          applyId: basicInfo.applyId,
          auditRemark: value,
          consumablesUpdateBoList: basicInfo.consumablesVoList,
          bulkConsumablesUpdateBoList: basicInfo.bulkConsumablesDetailsVoList,
          bulkReagentUpdateBoList: basicInfo.bulkReagentDetailsVoList
        };
        materialApplyAudit(params)
          .then((msg) => {
            loading.close();
            EleMessage.success('操作成功');
            push('/project/material-management/material-audit');
          })
          .catch((e) => {
            loading.close();
          });
      })
      .catch(() => {});
  };
</script>
