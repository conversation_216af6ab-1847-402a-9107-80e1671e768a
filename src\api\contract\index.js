import request from '@/utils/request';

//查询合同文件列表
export async function getContractFileList(params) {
  const res = await request.get('/project/contractFile/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增合同文件
 */
export async function addContractFile(data) {
  const res = await request.post('/project/contractFile/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 重命名合同文件
 */
export async function renameContractFile(data) {
  const res = await request.post('/project/contractFile/rename', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除合同文件
 */
export async function removeContractFile(fileIds) {
  const res = await request.delete('/project/contractFile/remove/' + fileIds);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//查询合同节点列表
export async function getContractNodeList(params) {
  const res = await request.get('/project/contractNode/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//获取合同节点详细信息
export async function getContractNodeDetail(fileId) {
  const res = await request.get(`/project/contractNode/${fileId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增合同节点
 */
export async function addContractNode(data) {
  const res = await request.post('/project/contractNode/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改合同节点
 */
export async function editContractNode(data) {
  const res = await request.post('/project/contractNode/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 标记合同节点
 */
export async function markContractNode(data) {
  const res = await request.post('/project/contractNode/mark', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除合同节点
 */
export async function removeContractNode(nodeIds) {
  const res = await request.delete('/project/contractNode/remove/' + nodeIds);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
