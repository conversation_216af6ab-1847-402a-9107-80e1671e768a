<!-- 添加和修改的表单 -->
<template>
  <div>
    <div :style="{ maxHeight: '80vh', overflow: 'auto', marginBottom: '60px' }">
      <h3>基本信息</h3>
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="false"
        :label-width="120"
        :grid="{ span: 8 }"
        @updateValue="setFieldValue"
      >
      </pro-form>
      <el-divider />
      <h3>研究信息</h3>
      <ele-data-table row-key="userId" :columns="columns" :data="dataList">
        <template #studyName="{ row }">
          <el-input v-model="row.studyName" />
        </template>
        <template #studyNumber="{ row }">
          <el-input v-model="row.studyNumber" />
        </template>
        <template #checkIndex="{ row }">
          <el-input v-model="row.checkIndex" />
        </template>
        <template #studyLeader="{ row }">
          <el-input v-model="row.studyLeader" />
        </template>
        <template #remark="{ row }">
          <el-input v-model="row.remark" />
        </template>
        <template #action="{ $index }">
          <el-link
            v-if="$index == dataList.length - 1"
            type="primary"
            underline="never"
            @click.stop="addRow"
          >
            新增
          </el-link>
          <el-divider
            v-if="
              $index == dataList.length - 1 &&
              ($index !== 0 || dataList.length != 1)
            "
            direction="vertical"
          />
          <el-link
            v-if="$index !== 0 || dataList.length != 1"
            type="danger"
            underline="never"
            @click.stop="delRow($index)"
          >
            删除
          </el-link>
        </template>
      </ele-data-table>
      <el-divider />
      <h3>申办方联系信息</h3>
      <pro-form
        ref="formRef1"
        :model="form"
        :rules="rules"
        :items="items1"
        :footer="false"
        :label-width="120"
        :grid="{ span: 8 }"
        @updateValue="setFieldValue"
      >
      </pro-form>
      <el-divider />
      <h3>委托方联系信息</h3>
      <pro-form
        ref="formRef2"
        :model="form"
        :rules="rules"
        :items="items2"
        :footer="false"
        :label-width="120"
        :grid="{ span: 8 }"
        @updateValue="setFieldValue"
        class="form2"
      >
      </pro-form>
    </div>
    <div class="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup>
  import ProForm from '@/components/ProForm/index.vue';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import {
    projectAdd,
    projectEdit,
    getProjectDetails
  } from '@/api/project-management';

  const props = defineProps({
    id: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['close', 'success']);

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    analyseProjectNumber: '',
    companyProjectNumber: '',
    schemeNumber: '',
    projectName: '',
    projectAttribute: '',
    projectLeader: '',
    projectClient: '',
    sponsor: '',
    approvalDate: '',
    remark: '',
    customerLeader1: '',
    phone1: '',
    address1: '',
    email1: '',
    customerLeader2: '',
    phone2: '',
    address2: '',
    email2: '',
    projectId: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '分析项目编号',
      prop: 'analyseProjectNumber',
      type: 'input',
      required: true
    },
    {
      label: '公司项目编号',
      prop: 'companyProjectNumber',
      type: 'input',
      required: true
    },
    {
      label: '试验方案编号',
      prop: 'schemeNumber',
      type: 'input',
      required: true
    },
    {
      label: '项目名称',
      prop: 'projectName',
      type: 'input',
      required: true,
      colProps: {
        span: 24
      }
    },
    {
      label: '项目属性',
      prop: 'projectAttribute',
      type: 'dictSelect', // 自定义组件
      props: { code: 'project_attribute' },
      required: true,
      disabled: true
    },
    {
      label: '项目负责人',
      prop: 'projectLeader',
      type: 'input',
      required: true
    },
    {
      label: '委托方',
      prop: 'projectClient',
      type: 'input',
      required: true
    },
    {
      label: '申办方',
      prop: 'sponsor',
      type: 'input',
      required: true
    },
    {
      label: '立项日期',
      prop: 'approvalDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' },
      required: true
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'input',
      colProps: { span: 16 }
    }
  ]);
  /** 基本信息表单验证规则 */
  const rules = reactive({});

  // 研究信息列表配置
  const columns = ref([
    {
      label: '*研究名称',
      prop: 'studyName',
      slot: 'studyName'
    },
    {
      label: '*研究编号',
      prop: 'studyNumber',
      slot: 'studyNumber'
    },
    {
      label: '*检测指标',
      prop: 'checkIndex',
      slot: 'checkIndex'
    },
    {
      label: '*研究负责人',
      prop: 'studyLeader',
      slot: 'studyLeader'
    },
    {
      label: '*备注',
      prop: 'remark',
      slot: 'remark'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 150,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  // 研究信息数据
  const dataList = ref([{}]);
  // 新增研究信息
  const addRow = () => {
    dataList.value.push({});
  };
  // 删除研究信息
  const delRow = (index) => {
    dataList.value.splice(index, 1);
  };

  // 申办方联系信息表单项
  const items1 = ref([
    {
      label: '申办方项目经理',
      prop: 'customerLeader1',
      type: 'input'
    },
    {
      label: '电话',
      prop: 'phone1',
      type: 'input'
    },
    {
      label: '地址',
      prop: 'address1',
      type: 'input'
    },
    {
      label: '邮箱',
      prop: 'email1',
      type: 'input'
    }
  ]);

  // 委托方/客户联系信息
  const items2 = ref([
    {
      label: '委托方项目经理',
      prop: 'customerLeader2',
      type: 'input'
    },
    {
      label: '电话',
      prop: 'phone2',
      type: 'input'
    },
    {
      label: '地址',
      prop: 'address2',
      type: 'input'
    },
    {
      label: '邮箱',
      prop: 'email2',
      type: 'input'
    }
  ]);

  const getParams = () => {
    const customerList = [
      {
        customerType: '1',
        customerLeader: form.customerLeader1,
        phone: form.phone1,
        address: form.address1,
        email: form.email1
      },
      {
        customerType: '2',
        customerLeader: form.customerLeader2,
        phone: form.phone2,
        address: form.address2,
        email: form.email2
      }
    ];
    const params = {
      ...form,
      studyList: dataList.value,
      customerList
    };
    return params;
  };
  const isValit = () => {};
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const tipsObj = {
        studyName: '研究名称不能为空',
        studyNumber: '研究编号不能为空',
        checkIndex: '检测指标不能为空',
        studyLeader: '研究负责人'
      };
      for (let key in tipsObj) {
        const list = dataList.value.filter((el) => !el[key] || el[key] == '');
        if (list.length != 0)
          return EleMessage.warning(`研究信息中${tipsObj[key]}`);
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? projectEdit : projectAdd;
      const params = getParams();
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    emit('close');
  };
  const ObjKey = {
    1: ['customerLeader1', 'phone1', 'address1', 'email1'],
    2: ['customerLeader2', 'phone2', 'address2', 'email2']
  };
  const customKeyObj = {
    customerLeader1: 'customerLeader',
    customerLeader2: 'customerLeader',
    phone1: 'phone',
    phone2: 'phone',
    address1: 'address',
    address2: 'address',
    email1: 'email',
    email2: 'email'
  };
  watch(
    () => props.id,
    async () => {
      if (props.id) {
        isUpdate.value = true;
        const { data } = await getProjectDetails(props.id);
        assignFields({
          ...data
        });
        dataList.value = data?.projectStudyList ?? [{}];
        if (!data?.projectCustomerList || data.projectCustomerList.length == 0)
          return;
        for (let type in ObjKey) {
          const list = data.projectCustomerList.filter(
            (el) => el.customerType == type
          );
          if (list.length == 0) return;
          for (let key of ObjKey[type]) {
            const custonKey = customKeyObj[key];
            form[key] = list[0][custonKey];
          }
        }
      } else {
        dataList.value = [{}];
        isUpdate.value = false;
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
<style lang="scss" scoped>
  .footer {
    position: absolute;
    bottom: 20px;
    right: 30px;
    z-index: 1000;
    background: #fff;
  }
  .form2 {
    padding-bottom: 50px;
  }
</style>
