<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    v-model="visible"
    :title="isUpdate ? '修改问题' : '新增问题'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      @submit.prevent=""
    >
      <el-form-item label="问题类型" prop="problemType">
        <!-- <dict-data
          code="problem_type"
          v-model="form.problemType"
          placeholder="请选择问题类型"
        /> -->
        <el-select v-model="form.problemType" placeholder="请选择问题类型">
          <el-option
            v-for="item in problemTypeList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
            :disabled="item.disabled"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="回答角色" prop="answerRole">
        <el-select v-model="form.answerRole">
          <el-option
            v-for="item in answerRoleList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="问题描述" prop="problemDescribe">
        <el-input
          :rows="4"
          type="textarea"
          maxlength="200"
          show-word-limit
          v-model="form.problemDescribe"
          placeholder="请输入问题描述"
        />
      </el-form-item>
      <el-form-item label="上传附件" prop="sysFileIds">
        <FileUpload
          style="flex: 1"
          ref="fileUploadRef"
          v-model="form.sysFileIds"
          :multiple="true"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { addProblem } from '@/api/problem';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { pageRoles } from '@/api/system/role';
  import { listDictDatas } from '@/api/system/dict-data';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  const answerRoleList = ref([]);
  const problemTypeList = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    projectId: '',
    problemSourceId: '',
    problemType: '',
    problemDescribe: '',
    answerRole: '',
    sysFileIds: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    problemType: [
      {
        required: true,
        message: '请选择问题类型',
        type: 'string',
        trigger: 'change'
      }
    ],
    problemDescribe: [
      {
        required: true,
        message: '请输入问题描述',
        type: 'string',
        trigger: 'change'
      }
    ],
    answerRole: [
      {
        required: true,
        message: '请选择回答角色',
        type: 'string',
        trigger: 'change'
      }
    ]
    // sysFileIds: [
    //   {
    //     required: true,
    //     message: '请上传附件',
    //     type: 'string',
    //     trigger: 'change'
    //   }
    // ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let params = {
        problemSourceId: form.problemSourceId,
        problemType: form.problemType,
        problemDescribe: form.problemDescribe,
        answerRole: form.answerRole,
        sysFileIds: ''
      };
      if (form.sysFileIds) {
        let dd = JSON.parse(form.sysFileIds);
        params.sysFileIds = dd.map((val) => val.ossId).join(',');
      }
      addProblem(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          handleCancel();
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields(props.data);
      isUpdate.value = true;
      getAnswerList();
    } else {
      resetFields();
      isUpdate.value = false;
      getAnswerList();
    }
  };
  nextTick(() => {
    getAnswerList();
  });
  const getAnswerList = () => {
    pageRoles({
      pageNum: 1,
      pageSize: 1000,
      isProblem: 1
    }).then((res) => {
      console.log(res);
      if (res.code == 200) {
        answerRoleList.value = res.records.map((val) => {
          return {
            label: val.roleName,
            value: val.roleId
          };
        });
      }
    });
    listDictDatas('problem_type').then((res) => {
      console.log(res);
      problemTypeList.value = res.map((val) => {
        return {
          label: val.dictLabel,
          value: val.dictValue,
          disabled:
            val.dictValue == 1 ? true : val.dictValue == 2 ? true : false
        };
      });
    });
  };
</script>
