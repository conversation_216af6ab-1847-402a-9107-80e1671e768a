import request from '@/utils/request';

/**
 * 查询OSS对象存储列表
 */
export async function listOss(params) {
  const res = await request.get('/resource/oss/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询OSS对象基于id串
 */
export async function listByIds(ossId) {
  const res = await request.get('/resource/oss/listByIds/' + ossId);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除OSS对象存储
 */
export async function delOss(ossId) {
  const res = await request.delete('/resource/oss/' + ossId);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
