/**
 * 登录用户状态管理
 */
import { defineStore } from 'pinia';
import { mapTree, isExternalLink } from '@hnjing/zxzy-admin-plus/es';
import {
  getUserInfo,
  getUserMenu,
  getUserMenuByProject,
  getProjectInfo,
  getProjectPermissions
} from '@/api/layout';
import { useThemeStore } from './theme';
import router from '@/router';

export const useUserStore = defineStore('user', {
  state: () => ({
    /** 当前登录用户的信息 */
    info: null,
    /** 当前登录用户的菜单 */
    menus: null,
    /** 当前登录用户的权限 */
    authorities: [],
    /** 当前登录用户的角色 */
    roles: [],
    /** 字典数据缓存 */
    dicts: {},
    /** 菜单切换 */
    menusSwitch: false,
    /** 项目id */
    projectId: null,
    /** 项目信息 */
    projectInfo: null,
    /** 菜单返回需要跳转的path **/
    backPath: null,
    /** 项目外的菜单 **/
    menusOutSide: null,
    authoritiesOutSide: [],
    viewLoading: false,
    insideObject: null
  }),
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: [
      'info',
      'projectId',
      'projectInfo',
      'menusSwitch',
      'backPath',
      'insideObject'
    ]
  },
  actions: {
    /**
     * 请求登录用户的个人信息/权限/角色/菜单
     */
    async fetchUserInfo() {
      const result = await getUserInfo().catch((e) => console.error(e));
      if (!result) {
        return {};
      }
      // 用户信息
      this.setInfo(result.user);
      // 用户权限
      this.authorities = result.permissions;
      this.authoritiesOutSide = result.permissions;
      // 用户角色
      this.roles = result.roles;
      // 菜单
      return this.getRouteSetMenus();
    },
    /**
     * 获取项目内用户权限菜单
     */
    async setUserInfoByProject(projectId, backPath = '/') {
      try {
        this.viewLoading = true;
        this.backPath = backPath;
        this.projectId = projectId;
        // // 如果是同一个项目，则不重新请求菜单
        // if (projectId === this.insideObject?.projectId) {
        //   const { menus, projectInfo, projectPermissions } = this.insideObject;
        //   this.setMenusSwitch(true, menus, projectPermissions, projectInfo);
        // } else {
        // 如果不是同一个项目，需要清除项目内页面缓存
        const themeStore = useThemeStore();
        await themeStore.clearProjectTabs();
        const promise = Promise.all([
          getUserMenuByProject(this.projectId),
          getProjectInfo(this.projectId),
          getProjectPermissions(this.projectId)
        ]);
        try {
          let [userMenu, projectInfo, projectPermissions] = await promise;
          /**
           * 项目内，项目状态控制
           * 1在研:项目创建成功后，系统状态默认为在研
           * 2.暂停:更改为暂停状态后，该项目的所有角色成员只允许查看，不允许任何流程操作，不包含导出功能
           * 3.结束:更改为结束状态后，该项目内容仅对【项目管理员可见】，且不支持修改内容，支持导出
           * 4.废弃:更改为废弃状态后，仅支持查看，该项目内容仅对【项目管理员可见】，仅支持无申请记录的项目进行废弃
           * ***/
          if (projectInfo.projectStatus !== '1') {
            projectPermissions = projectPermissions.filter((item) => {
              return item.includes(':export');
            });
          }

          // 这里需要将userMenu替换之前的menus，因为userMenu是项目内权限菜单，menus是项目外权限菜单
          const insideMenus = userMenu?.[0].children.map((item) => ({
            ...item,
            path: `/project/${item.path}`
          }));
          const { menus } = formatMenus(insideMenus);
          this.insideObject = {
            menus,
            projectPermissions,
            projectInfo,
            projectId
          };
          this.setMenusSwitch(true, menus, projectPermissions, projectInfo);
        } catch (error) {
          this.projectId = null;
          router.push('/exception/403');
        }
        // }
      } catch (error) {
        console.error(error);
      } finally {
        this.viewLoading = false;
      }
    },
    // 获取对应菜单，并设置菜单数据
    async getRouteSetMenus() {
      // 用户菜单
      const userMenu = await getUserMenu().catch((e) => console.error(e));
      if (!userMenu) {
        return {};
      }
      const { menus, homePath } = formatMenus(userMenu);
      this.menusOutSide = menus.filter((item) => item.path !== '/project'); // 项目外权限菜单   /project
      this.setMenus(this.menusOutSide);
      this.menusSwitch = false;
      return { menus, homePath };
    },
    // 重置项目信息
    resetProjectInfo() {
      this.projectId = null;
      this.projectInfo = null;
      this.insideObject = null;
    },
    /**
     * 更新用户信息
     */
    setInfo(value) {
      this.info = value;
    },
    /**
     * 更新菜单数据
     */
    setMenus(menus) {
      this.menus = menus;
    },
    /**
     * 菜单切换
     */
    setMenusSwitch(
      value,
      menus = this.menusOutSide,
      authorities = this.authoritiesOutSide,
      projectInfo = null
    ) {
      this.setMenus(menus);
      this.authorities = authorities;
      this.projectInfo = projectInfo;
      this.menusSwitch = value;
    },
    /**
     * 更新菜单的徽章
     * @param path 菜单地址
     * @param value 徽章值
     * @param type 徽章类型
     */
    setMenuBadge(path, value, type) {
      this.menus = mapTree(this.menus, (m) => {
        if (path === m.path) {
          const meta = m.meta || {};
          return {
            ...m,
            meta: {
              ...meta,
              props: {
                ...meta.props,
                badge: value == null ? void 0 : { value, type }
              }
            }
          };
        }
        return m;
      });
    },
    /**
     * 更新字典数据
     */
    setDicts(value, code) {
      if (code == null) {
        this.dicts = value;
        return;
      }
      this.dicts[code] = value;
    }
  }
});

export function formatMenus(data, childField = 'children') {
  let homePath;
  let homeTitle;
  const menus = mapTree(
    data,
    (item, _index, parent) => {
      const meta = item.meta;
      const { path, rPath } = formatPath(item.path, parent?.path, item.query);
      const menu = {
        path: path,
        component: formatComponent(item.component),
        meta: {
          hide: !!item.hidden,
          keepAlive: !meta.noCache,
          routePath: rPath,
          ...meta
        }
      };
      const children = item[childField]
        ? item[childField].filter((d) => !(d.meta?.hide ?? d.hide))
        : void 0;
      if (!children?.length) {
        if (!homePath && menu.path && !isExternalLink(menu.path)) {
          homePath = menu.path;
          homeTitle = menu.meta?.title;
        }
      } else {
        const childPath = children[0].path;
        if (childPath) {
          if (!menu.redirect) {
            menu.redirect = childPath;
          }
          if (!menu.path) {
            menu.path = childPath.substring(0, childPath.lastIndexOf('/'));
          }
        }
      }
      if (!menu.path) {
        console.error('菜单path不能为空且要唯一:', item);
        return;
      }
      return menu;
    },
    childField
  );
  return { menus, homePath, homeTitle };
}

/**
 * 组件路径处理以兼容若依默认数据
 * @param component 组件路径
 */
function formatComponent(component) {
  if (!component || component === 'Layout') {
    return;
  }
  if (isExternalLink(component)) {
    return component;
  }
  return component.startsWith('/') ? component : `/${component}`;
}

/**
 * 菜单地址处理以兼容若依
 * @param mPath 菜单地址
 * @param pPath 父级菜单地址
 * @param query 路由参数
 */
function formatPath(mPath, pPath, query) {
  if (!mPath || isExternalLink(mPath)) {
    return { path: mPath };
  }
  const path = !pPath || mPath.startsWith('/') ? mPath : `${pPath}/${mPath}`;
  if (query) {
    try {
      const params = new URLSearchParams(JSON.parse(query)).toString();
      if (params) {
        return { path: `${path}?${params}`, rPath: path };
      }
    } catch (e) {
      console.error(e);
    }
  }
  return { path };
}
