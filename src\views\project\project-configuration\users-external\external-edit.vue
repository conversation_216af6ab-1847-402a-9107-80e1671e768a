<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    v-model="visible"
    :title="isUpdate ? '修改外部用户授权' : '添加外部用户授权'"
    @open="handleOpen"
  >
    <el-table :data="tableList">
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column prop="roleId" label="授权项目角色">
        <template #default="scope">
          <el-select
            v-model="scope.row.roleId"
            clearable
            @change="handleRoleChange(scope.row)"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in roleList"
              :key="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="userId" label="姓名">
        <template #default="scope">
          <el-select
            v-model="scope.row.userId"
            clearable
            :disabled="!scope.row.roleId"
            @change="handleUserChange(scope.row)"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="item in userList"
              :key="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="账号" />
      <el-table-column prop="hospitalIdList" label="授权医院">
        <template #default="scope">
          <el-select v-model="scope.row.hospitalIdList" multiple>
            <el-option
              v-for="item in hospitalList"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <!-- 操作栏 -->
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="danger" size="small" @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="btn-add">
      <el-button type="text" :icon="PlusOutlined" @click="handleAdd"
        >添加成员</el-button
      >
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        确定
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick,watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import {
    addProjectGrant,
    getProjectRoleList,
    getProjectRoleUserList
  } from '@/api/project-configuration';
  import { getHospitalList } from '@/api/project/project-configuration/institutional-information';
  import { PlusOutlined } from '@/components/icons';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  //监听visible值的变化
  watch(
    () => visible.value,
    (val) => {
      if (val) {
        setTimeout(()=>{
          handleAdd();
        },100)
      }
    }
  );
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  const tableList = ref([]);
  const roleList = ref([]);
  const userList = ref([]);
  const hospitalList = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    postId: void 0,
    postName: '',
    postCode: '',
    postSort: 0,
    status: '0',
    remark: ''
  });
  const handleDelete = (row) => {
    tableList.value = tableList.value.filter((item) => item !== row);
  };
  const handleAdd = () => {
    tableList.value.push({
      projectId: '',
      roleId: '',
      userId: '',
      duties: '',
      hospitalIdList: []
    });
  };
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    if (tableList.value.length == 0) {
      EleMessage.error('请添加外部用户');
      return;
    }
    let dd = tableList.value.filter((item) => !item.roleId);
    if (dd && dd.length > 0) {
      EleMessage.error('请选择授权项目角色');
      return;
    }
    let dd2 = tableList.value.filter((item) => !item.userId);
    if (dd2 && dd2.length > 0) {
      EleMessage.error('请选择姓名');
      return;
    }
    let dd3 = tableList.value.filter(
      (item) => !item.hospitalIdList || item.hospitalIdList.length == 0
    );
    if (dd3 && dd3.length > 0) {
      EleMessage.error('请选择授权医院');
      return;
    }
    // 检测重复项逻辑
    const duplicateMap = new Map();
    const duplicates = [];
    tableList.value.forEach((item, index) => {
      // 创建唯一标识键：roleId + userId组合
      const uniqueKey = `${item.roleId}-${item.userId}`;
      if (duplicateMap.has(uniqueKey)) {
        // 记录重复项信息
        duplicates.push({
          index,
          roleId: item.roleId,
          userId: item.userId,
          userName: item.userName
        });
      } else {
        duplicateMap.set(uniqueKey, index);
      }
    });

    // 如果存在重复项，显示错误信息
    if (duplicates.length > 0) {
      const duplicateInfo = duplicates
        .map((item) => `第${item.index + 1}行（用户: ${item.userName}）`)
        .join('、');

      EleMessage.error(`发现重复授权：${duplicateInfo}，请删除重复项后重试`);
      return;
    }
    console.log(tableList.value);
    loading.value = true;
    let params = {
      userType: 1,
      addList: tableList.value.map((item) => ({
        projectId: item.projectId,
        roleId: item.roleId,
        userId: item.userId,
        duties: item.duties,
        hospitalIdList: item.hospitalIdList
      }))
    };
    addProjectGrant(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    tableList.value = [];
    if (props.data) {
      isUpdate.value = true;
    } else {
      isUpdate.value = false;
      initData();
    }
  };
  nextTick(() => {
    initData();
  });
  const initData = () => {
    getProjectRoleList(1).then((res) => {
      console.log(res);
      if (res.code === 200) {
        roleList.value = res.data.map((val) => {
          return {
            label: val.roleName,
            value: val.roleId,
            roleKey: val.roleKey
          };
        });
      }
    });
    getHospitalList().then((res) => {
      console.log(res);
      if (res.code === 200) {
        hospitalList.value = res.records.map((val) => {
          return {
            label: val.hospitalName,
            value: val.hospitalId,
            projectId: val.projectId,
            organizationId: val.organizationId,
            phone: val.phone
          };
        });
      }
    });
  };
  const handleUserChange = (row) => {
    if (row.userId) {
      row.userName = userList.value.find(
        (item) => item.value == row.userId
      ).userName;
    } else {
      row.userName = '';
    }
  };
  const handleRoleChange = (row) => {
    if (row.roleId) {
      userList.value = [];
      row.userId = '';
      getProjectRoleUserList(row.roleId).then((res) => {
        if (res.code === 200) {
          userList.value = res.data.map((val) => {
            return {
              label: val.nickName,
              value: val.userId,
              userName: val.userName,
              userType: val.userType
            };
          });
          if (userList.value.length == 1) {
            row.userId = userList.value[0].value;
            row.userName = userList.value[0].userName;
          }
        }
      });
    }
  };
</script>
<style lang="scss" scoped>
  .btn-add {
    display: flex;
    justify-content: center;
    padding: 10px 0 10px 0;
    border-bottom: 1px solid #e4e7ed;
  }
</style>
