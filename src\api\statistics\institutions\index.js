import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getOrganizationStatList(params) {
  const res = await request.get(
    '/organizationStat/selectOrganizationStatList',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 机构下 关联项目
export async function getOrganizationProjectList(params) {
  const res = await request.get('/project/selectOrganizationProjectList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//机构信息
export async function getOrganizationInfo(organizationId) {
  const res = await request.get(`/organization/${organizationId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportOrganizationStat(params) {
  const res = await request({
    url: '/organizationStat/exportOrganizationStatList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `机构统计_${Date.now()}.xlsx`);
}
