import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//查询
export async function getMaterialPrepareApplyList(params) {
  const res = await request.get('/materialPrepareApply/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 打印
export async function materialPrepareApplyPrint(data) {
  const res = await request.post('/materialPrepareApply/print', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出 机构物资库存
export async function exportMaterialPrepareApply(params) {
  const res = await request({
    url: '/materialPrepareApply/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资明细清单_${Date.now()}.xlsx`);
}
