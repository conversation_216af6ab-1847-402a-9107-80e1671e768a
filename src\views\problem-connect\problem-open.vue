<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    v-model="visible"
    :title="type == '1' ? '关闭问题' : '回复问题'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      @submit.prevent=""
    >
      <el-form-item label="关闭备注" v-if="type == '1'" prop="remark">
        <el-input
          :rows="4"
          type="textarea"
          v-model="form.remark"
          placeholder="请输入关闭备注"
        />
      </el-form-item>
      <el-form-item label="评论内容" v-if="type == '2'" prop="remark2">
        <el-input
          :rows="4"
          maxlength="200"
          show-word-limit
          type="textarea"
          v-model="form.remark2"
          placeholder="请输入评论内容"
        />
      </el-form-item>
      <el-form-item label="上传附件" v-if="type == '2'">
        <!-- <el-upload
          action="#"
          :on-success="handleAvatarSuccess"
          :show-file-list="false"
        >
          <el-button slot="trigger" type="primary">点击上传</el-button>
        </el-upload> -->
        <FileUpload
          style="flex: 1"
          ref="fileUploadRef"
          v-model="form.sysFileIds"
          :multiple="true"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { replyProblem, closeProblem } from '@/api/problem';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object,
    /** 问题类型 */
    type: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    sysFileIds: [],
    remark: '',
    remark2: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    remark: [
      {
        required: true,
        message: '请输入关闭备注',
        type: 'string',
        trigger: 'blur'
      }
    ],
    remark2: [
      {
        required: true,
        message: '请输入评论内容',
        type: 'string',
        trigger: 'blur'
      }
    ],
    sysFileIds: [
      {
        required: true,
        message: '请上传附件',
        type: 'string',
        trigger: 'change'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      console.log(props.data);
      if (props.type == '1') {
        //关闭问题
        let params = {
          problemId: props.data.problemId,
          closeRemark: form.remark
        };
        loading.value = true;
        closeProblem(params)
          .then((msg) => {
            loading.value = false;
            EleMessage.success(msg);
            handleCancel();
            emit('done');
          })
          .catch((e) => {
            loading.value = false;
            EleMessage.error(e.message);
          });
      } else {
        //回复问题
        loading.value = true;
        let params = {
          problemId: props.data.problemId,
          replyDescribe: form.remark2,
          sysFileIds: '',
          replyUserRole: userStore?.info?.roles[0]?.roleName
        };
        console.log('form.sysFileIds', form.sysFileIds);
        if (form.sysFileIds && form.sysFileIds.length > 0) {
          let dd = JSON.parse(form.sysFileIds);
          params.sysFileIds = dd.map((val) => val.ossId).join(',');
        }
        console.log('params', params);
        replyProblem(params)
          .then((msg) => {
            loading.value = false;
            EleMessage.success(msg);
            handleCancel();
            emit('done');
          })
          .catch((e) => {
            loading.value = false;
            EleMessage.error(e.message);
          });
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields(props.data);
      form.sysFileIds = [];
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
      form.sysFileIds = [];
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
