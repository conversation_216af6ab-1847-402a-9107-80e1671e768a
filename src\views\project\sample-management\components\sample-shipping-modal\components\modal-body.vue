<template>
  <div>
    <!-- 基本信息 -->
    <BasicInfo
      ref="basicInfoRef"
      v-model="formData.basicInfo"
      :mode="mode"
      :formHandle="props.formHandle"
    />

    <!-- 送检数量 -->
    <ShippingQuantity
      ref="quantityRef"
      v-model="formData.listBoList"
      :mode="mode"
      :formHandle="props.formHandle"
      v-if="handle !== 'handle'"
    />

    <!-- 送检样本明细 -->
    <ShippingDetail
      ref="detailRef"
      :formHandle="props.formHandle"
      v-model="formData.detailsBoList"
      :mode="mode"
    />
  </div>
</template>

<script setup>
  import { ref, inject } from 'vue';
  import BasicInfo from './basic-info.vue';
  import ShippingQuantity from './shipping-quantity.vue';
  import ShippingDetail from './shipping-detail.vue';

  const formData = defineModel('formData', {
    type: Object,
    required: true
  });

  const props = defineProps({
    /** 操作模式 */
    mode: {
      type: String,
      default: 'add'
    },
    // 表单处理类型
    formHandle: String
  });

  const handle = inject('handle');

  // 组件引用
  const basicInfoRef = ref(null);
  const quantityRef = ref(null);
  const detailRef = ref(null);

  // 暴露验证方法
  const validate = async () => {
    const basicValid = await basicInfoRef.value?.validate();
    const quantityValid = await quantityRef.value?.validate();
    let detailValid = true;
    // CRC添加可以不用明细，样本管理员独立接收的时候需要明细
    if (handle !== 'despatch') {
      detailValid = await detailRef.value?.validate();
    }

    return handle === 'handle'
      ? basicValid && detailValid
      : basicValid && quantityValid && detailValid;
  };

  defineExpose({
    validate,
    basicInfoRef,
    quantityRef,
    detailRef
  });
</script>
<style scoped lang="less"></style>
