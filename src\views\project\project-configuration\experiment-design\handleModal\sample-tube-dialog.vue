<template>
  <el-dialog
    v-model="visible"
    title="批量配置样本管"
    width="800px"
    :append-to-body="true"
    :z-index="2001"
    @closed="handleClose"
  >
    <SampleTube
      ref="tubeRef"
      @saveTube="handleSave"
      :pointId="props.pointIds"
      :isBatch="true"
    />
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSaveButton">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref } from 'vue';
  import SampleTube from '../components/sample-tube.vue';

  const visible = defineModel({ type: Boolean, default: false });

  const props = defineProps({
    pointIds: {
      type: Array,
      default: () => []
    }
  });

  const emit = defineEmits(['save']);

  const handleSave = (points) => {
    emit('save', points);
    visible.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleClose = () => {
    visible.value = false;
  };

  const tubeRef = ref(null);
  const handleSaveButton = () => {
    tubeRef.value.handleSave();
  };
</script>
