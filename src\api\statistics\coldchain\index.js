import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

/**
 * 查询冷链统计列表
 */
export async function getLenglianStatList(params) {
  const res = await request.get('/stats/lenglian/list', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出冷链统计列表
 */
export async function exportLenglianStat(params) {
  const res = await request({
    url: '/stats/lenglian/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `冷链统计_${Date.now()}.xlsx`);
}

/**
 *编辑冷链
 */
export async function editLenglianStat(data) {
  const res = await request.post('/stats/lenglian/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询样本统计列表
 */
export async function getSampleStatList(params) {
  const res = await request.get('/stats/sample/list', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出样本统计列表
 */
export async function exportSampleStat(params) {
  const res = await request({
    url: '/stats/sample/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `样本统计_${Date.now()}.xlsx`);
}

// 查询指定项目的样本库存
export async function getSampleStock(projectId) {
  const res = await request.get(`/stats/sample/${projectId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
