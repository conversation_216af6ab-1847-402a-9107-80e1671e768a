<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            type="primary"
            :icon="PlusOutlined"
            v-permission="
              handle === 'despatch'
                ? 'sample:sampleForwarding:add'
                : handle === 'receive'
                  ? 'sample:sampleReceive:add'
                  : 'sample:sampleHandle:add'
            "
            @click="handleSampleShipping()"
          >
            {{ title }}
          </el-button>
          <el-button
            class="ele-btn-icon"
            @click="handleExportSampleLabel()"
            v-permission="
              handle === 'despatch'
                ? 'sample:sampleForwarding:export'
                : handle === 'receive'
                  ? 'sample:sampleReceive:export'
                  : 'sample:sampleHandle:export'
            "
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="systemId"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="sampleLabelList"
        :sticky="true"
        :loadOnCreated="false"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <!-- 状态列 -->
        <template #status="{ row }">
          <el-tag :type="options.sendStatus[row.status].type">{{
            options.sendStatus[row.status].label
          }}</el-tag>
          <!-- <ele-dot
            v-if="row.status === '0'"
            type="warning"
            text="待接收"
            size="8px"
          />
          <ele-dot
            v-else-if="row.status === '1'"
            text="已接收"
            type="primary"
            :ripple="false"
            size="8px"
          />
          <ele-dot
            v-else-if="row.status === '2'"
            text="已撤销"
            type="danger"
            :ripple="false"
            size="8px"
          /> -->
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            type="danger"
            underline="never"
            @click.stop="handleCancel(row)"
            v-if="
              handle !== 'receive' &&
              !(row.status !== '0' && handle === 'despatch') && // 样本发运，撤销状态码为2时不显示撤销按钮
              !(row.status === '1' && handle === 'handle') // 样本处理，处理状态码为1时不显示撤销按钮
            "
            v-permission="
              handle === 'despatch'
                ? 'sample:sampleForwarding:revoke'
                : 'sample:sampleHandle:revoke'
            "
            >撤销</el-link
          >
          <!-- 如果是样本接收，且发运状态是待接收，则显示接收按钮 -->
          <el-link
            type="danger"
            underline="never"
            @click.stop="handleSampleShipping('edit', row)"
            v-if="handle === 'receive' && row.status === '0'"
            v-permission="'sample:sampleReceive:confirm'"
            >接收</el-link
          >
          <el-divider
            direction="vertical"
            v-if="
              ((handle === 'receive' && row.status === '0') ||
                handle !== 'receive') &&
              !(row.status !== '0' && handle === 'despatch') && // 样本发运，撤销状态码为2时不显示撤销按钮
              !(row.status === '1' && handle === 'handle') // 样本处理，处理状态码为1时不显示撤销按钮
            "
          />
          <el-link
            type="success"
            underline="never"
            v-if="handle !== 'handle'"
            v-permission="'project:problem:add'"
            @click.stop="questionsClick(row)"
            >提问</el-link
          >
          <el-divider direction="vertical" v-if="handle !== 'handle'" />
          <el-link
            type="primary"
            underline="never"
            @click.stop="handleSampleShipping('view', row)"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>

      <!-- 样本Modal -->
      <SampleShippingModal
        v-model="sampleShippingVisible"
        :mode="mode"
        :rowData="rowData"
        @save="reload"
      />
      <handleModalQuestions v-model="questionsVisibleModal" :data="editData" />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, inject, provide, h, onMounted } from 'vue';
  import { ElMessageBox, ElTag } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getSampleForwardingList,
    getSampleReceiveList,
    getSampleHandleList,
    revokeSampleForwarding,
    revokeSampleHandle,
    exportSampleForwarding,
    exportSampleReceive,
    exportSampleHandle
  } from '@/api/project/sample-management/index.js';
  import { useUserStore } from '@/store/modules/user';
  import SampleShippingModal from './sample-shipping-modal/index.vue';
  import handleModalQuestions from './questions/index.vue';
  import { PlusOutlined } from '@/components/icons';
  import { useDictData } from '@/utils/use-dict-data';
  import { useRoute } from 'vue-router';

  const { query } = useRoute();
  /**
   * 获取项目详情
   * 项目ID    projectId
   * 设计类型  designType
   * 1=研究周期+采样时间
   * 2=周期+年/月/周/日+采样时间
   * 3=访视+采样点
   */
  const userStore = useUserStore();
  const projectInfo = userStore.projectInfo;
  /** 项目信息
   * designStatus, designType
   */
  provide('projectInfo', projectInfo);
  const handle = inject('handle');
  const [logisticsCompany] = useDictData(['logistics_company']);
  const myLogisticsCompany = computed(() =>
    logisticsCompany.value
      ?.filter((item) => item.remark === '冷链')
      ?.map((item) => ({
        label: item.dictLabel,
        value: item.dictValue
      }))
  );

  /** 自定义字典数据 */
  const options = reactive({
    // 发运状态（0代表待接收 1代表已接收 2代表已撤销）
    sendStatus: {
      0: { label: '待接收', value: '0', type: 'pramary' },
      1: { label: '已接收', value: '1', type: 'success' },
      2: { label: '已撤销', value: '2', type: 'danger' }
    },
    // 处理状态 状态（0代表已处理 1代表已撤销）
    handleStatus: [
      { label: '已处理', value: '0', type: 'success' },
      { label: '已撤销', value: '1', type: 'danger' }
    ],
    // 处理类型 处理类型（1代表转移 2代表销毁）
    handleType: [
      { label: '转移', value: '1', type: 'success' },
      { label: '销毁', value: '2', type: 'danger' }
    ]
  });

  const editData = ref({});

  const mode = ref('add');

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({});

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = computed(() => {
    let myItems = [
      {
        type: 'input',
        label: '发运编号',
        prop: 'forwardingNumber',
        props: {
          labelWidth: '120px'
        }
      },
      {
        type: 'select',
        label: '发运状态',
        prop: 'status',
        options: Object.keys(options.sendStatus).map((key) => {
          return options.sendStatus[key];
        })
      },
      {
        type: 'input',
        label: '机构名称',
        prop: 'hospitalName'
      },
      {
        type: 'select',
        label: '运输单位',
        prop: 'logisticsCompany',
        options: myLogisticsCompany.value
      },
      {
        type: 'input',
        label: '运输单号',
        prop: 'bookingNote'
      }
    ];
    if (handle === 'handle') {
      myItems = [
        {
          type: 'select',
          label: '处理状态',
          prop: 'status',
          options: options.handleStatus
        },
        {
          type: 'select',
          label: '处理类型',
          prop: 'handleType',
          options: options.handleType
        },
        {
          type: 'input',
          label: '处理人',
          prop: 'handleName'
        },
        {
          type: 'date',
          label: '处理日期',
          prop: 'handleDate'
        }
      ];
    }
    return myItems;
  });

  /** 表格列配置 */
  const columns = computed(() => {
    let myColumns = [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'forwardingNumber',
        label: '发运编号',
        minWidth: 110
      },
      {
        columnKey: 'status',
        prop: 'status',
        label: '发运状态',
        width: 100,
        align: 'center',
        slot: 'status'
      },
      {
        label: '机构名称',
        prop: 'hospitalName',
        minWidth: 110
      },
      {
        label: '机构代号',
        prop: 'hospitalCode',
        minWidth: 110
      },
      {
        label: '运输单位',
        prop: 'logisticsCompany',
        minWidth: 120
      },
      {
        prop: 'bookingNote',
        label: '运输单号',
        minWidth: 120
      },
      {
        prop: 'forwardingName',
        label: '发运人',
        minWidth: 120
      },
      {
        prop: 'forwardingDate',
        label: '发运日期',
        minWidth: 120
      },
      {
        prop: 'receiveName',
        label: '接收人',
        minWidth: 120
      },
      {
        label: '接收日期',
        prop: 'receiveDate',
        minWidth: 110
      },
      {
        columnKey: 'action',
        label: '操作',
        minWidth: 180,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];

    if (handle === 'handle') {
      myColumns = [
        {
          type: 'index',
          columnKey: 'index',
          width: 50,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'status',
          label: '处理状态',
          width: 100,
          formatter: ({ status }) => {
            return h(
              ElTag,
              { type: status === '0' ? 'success' : 'danger' },
              () =>
                status === '0' ? '已处理' : status === '1' ? '已撤销' : null
            );
          }
        },
        {
          label: '处理类型',
          prop: 'handleType',
          minWidth: 110,
          formatter: ({ handleType }) => {
            return h(
              ElTag,
              { type: handleType === '1' ? 'success' : 'danger' },
              () =>
                handleType === '1' ? '转移' : handleType === '2' ? '销毁' : null
            );
          }
        },
        {
          label: '处理人',
          prop: 'handleName',
          minWidth: 120
        },
        {
          label: '处理日期',
          prop: 'handleDate',
          minWidth: 120
        },
        {
          label: '处理原因',
          prop: 'handleReason',
          minWidth: 120
        },
        {
          label: '处理样本数量',
          prop: 'handleSampleNum',
          minWidth: 120
        },
        {
          label: '撤销人',
          prop: 'revokeName',
          minWidth: 120
        },
        {
          label: '撤销日期',
          prop: 'revokeDate',
          minWidth: 110
        },
        {
          columnKey: 'action',
          label: '操作',
          minWidth: 180,
          align: 'center',
          showOverflowTooltip: false,
          resizable: false,
          slot: 'action',
          fixed: 'right',
          disabledInSetting: true,
          hideInPrint: true,
          hideInExport: true
        }
      ];
    }
    return myColumns;
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  const title = computed(() => {
    return handle === 'despatch'
      ? '样本发运'
      : handle === 'receive'
        ? '样本接收'
        : '样本处理';
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    const ajax =
      handle === 'despatch'
        ? getSampleForwardingList
        : handle === 'receive'
          ? getSampleReceiveList
          : getSampleHandleList;
    return ajax({ ...where, ...orders, ...filters, ...pages });
  };

  // 撤销
  const handleCancel = ({ forwardingId, forwardingNumber, handleId }) => {
    ElMessageBox.confirm(
      `确定要撤销${forwardingNumber ? '发运编号【' + forwardingNumber + '】' : '此次处理'}吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(async () => {
        // 接收撤销，处理撤销
        if (handle === 'despatch') {
          await revokeSampleForwarding(forwardingId);
          EleMessage.success('接收撤销成功');
        } else if (handle === 'handle') {
          await revokeSampleHandle(handleId);
          EleMessage.success('处理撤销成功');
        }
        reload();
      })
      .catch(() => {});
  };

  // 提问
  const questionsVisibleModal = ref(false);
  const questionsClick = (row) => {
    editData.value = row || null;
    questionsVisibleModal.value = true;
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  // 样本发运
  const sampleShippingVisible = ref(false);
  const rowData = ref(null);
  const handleSampleShipping = (setMode = 'add', row) => {
    mode.value = setMode;
    if (mode.value === 'add') {
      rowData.value = null;
    } else {
      rowData.value = row;
    }
    // 试验设计类型，如果未确认，提示不能进行样本发运
    if (projectInfo.designStatus === '0') {
      EleMessage.warning('试验设计未确认，不能进行' + title.value + '操作！');
      return;
    }
    sampleShippingVisible.value = true;
  };

  /** 导出数据 */
  const handleExportSampleLabel = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      const ajax =
        handle === 'receive'
          ? exportSampleReceive
          : handle === 'handle'
            ? exportSampleHandle
            : exportSampleForwarding;
      ajax({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  onMounted(() => {
    if (query.status) {
      form.status = query.status;
    }
    reload(form);
  });
</script>
