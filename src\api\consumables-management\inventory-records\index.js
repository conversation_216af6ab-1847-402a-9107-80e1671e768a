import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getInventoryRecordList(params) {
  const res = await request.get('/inventoryRecord/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//详情
export async function getInventoryRecordDetails(consumableId) {
  const res = await request.get(`/inventoryRecord/${consumableId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//新增
export async function addInventoryRecord(data) {
  const res = await request.post('/inventoryRecord/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportInventoryRecord(params) {
  const res = await request({
    url: '/inventoryRecord/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `库存记录_${Date.now()}.xlsx`);
}

// 查询研究耗材配置列表

export async function getConsumableList(params) {
  const res = await request.get('/system/consumable/query', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
