<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <ele-tabs
        v-model="active"
        flex-table
        :items="[
          { name: 'consumablesPackage', label: '耗材包统计' },
          { name: 'bulkConsumables', label: '散装耗材统计' }
        ]"
      >
        <template #consumablesPackage>
          <EleTableSearch
            :model="form"
            :items="items"
            :show-label="false"
            @updateValue="updateFormValue"
            @submit="onSearch"
            @reset="onSearch"
            style="margin-top: 20px"
          >
            <template #toolbar>
              <el-button
                v-permission="'consumables:stat:export'"
                plain
                @click="exportSource"
              >
                导出
              </el-button>
            </template>
          </EleTableSearch>
          <!-- 表格 -->
          <ele-pro-table
            ref="tableRef"
            row-key="materialConsumablesId"
            :columns="columns"
            :loadOnCreated="false"
            :datasource="datasource"
            :show-overflow-tooltip="true"
            cache-key="materialPackage-statistics"
            :footer-style="{ paddingBottom: '12px' }"
            style="padding-bottom: 0; margin-top: -5px"
          />
        </template>
        <template #bulkConsumables>
          <EleTableSearch
            :model="form1"
            :items="items1"
            :show-label="false"
            @updateValue="updateFormValue"
            @submit="onSearch1"
            @reset="onSearch1"
            style="margin-top: 20px"
          >
            <template #toolbar>
              <el-button
                v-permission="'bulkConsumables:stat:export'"
                plain
                @click="exportSource"
              >
                导出
              </el-button>
            </template>
          </EleTableSearch>
          <!-- 表格 -->
          <ele-pro-table
            ref="tableRef1"
            row-key="materialBulkConsumablesId"
            :columns="columns1"
            :datasource="datasource1"
            :show-overflow-tooltip="true"
            cache-key="materialPackage-statistics"
            :footer-style="{ paddingBottom: '12px' }"
            style="padding-bottom: 0; margin-top: -5px"
          />
        </template>
      </ele-tabs>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, unref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    selectConsumablesPackageStatList,
    exportConsumablesPackageStat,
    selectBulkConsumablesStatList,
    exportBulkConsumablesStat
  } from '@/api/project/material-management/materialPackage-statistics';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'MaterialPackageStatistics' });

  const { currentRoute } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);
  const tableRef1 = ref(null);

  const active = ref('consumablesPackage');

  /** 表单数据 */
  const form = reactive({
    hospitalName: '',
    consumablesName: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    if (active.value == 'consumablesPackage') {
      form[prop] = value;
      if (prop === 'overdue') {
        onSearch();
      }
    } else {
      form1[prop] = value;
    }
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      type: 'checkbox',
      label: '过期状态',
      prop: 'overdue',
      options: [{ label: '已过期', value: '1' }]
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        width: 100,
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '机构名称',
        align: 'center'
      },
      {
        prop: 'consumablesNumber',
        label: '耗材包编号',
        align: 'center'
      },
      {
        prop: 'consumablesName',
        label: '耗材包名称',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '有效期至',
        align: 'center'
      },
      {
        prop: 'prepareQuantity',
        label: '寄出数量',
        align: 'center'
      },
      {
        prop: 'prepareDate',
        label: '准备日期',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '寄出日期',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'receiveDate',
        label: '接收日期',
        align: 'center',
        minWidth: 150
      }
    ];
  });

  /** 表单数据 */
  const form1 = reactive({
    hospitalName: '',
    consumablesName: '',
    consumableSize: '',
    consumableMaterial: '',
    consumableBrand: ''
  });

  /** 表单项 */
  const items1 = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      type: 'input',
      label: '规格尺寸',
      prop: 'consumableSize'
    },
    {
      type: 'input',
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      type: 'input',
      label: '品牌',
      prop: 'consumableBrand'
    }
  ]);

  /** 表格列配置 */
  const columns1 = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        width: 100,
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '机构名称',
        align: 'center'
      },
      {
        prop: 'bulkConsumableNumber',
        label: '耗材编号',
        align: 'center'
      },
      {
        prop: 'consumableName',
        label: '耗材名称',
        align: 'center'
      },
      {
        prop: 'consumableSize',
        label: '规格尺寸',
        align: 'center'
      },
      {
        prop: 'consumableMaterial',
        label: '颜色材质',
        align: 'center'
      },
      {
        prop: 'consumableBrand',
        label: '品牌',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '有效期至',
        align: 'center'
      },
      {
        prop: 'forwardingQuantity',
        label: '寄出数量',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '寄出日期',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'receiveDate',
        label: '接收日期',
        align: 'center',
        minWidth: 150
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return selectConsumablesPackageStatList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };
  /** 表格数据源 */
  const datasource1 = ({ pages, where, orders, filters }) => {
    return selectBulkConsumablesStatList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(form);
  };
  /** 搜索事件 */
  const onSearch1 = (where) => {
    reload(form1);
  };
  /** 刷新表格 */
  const reload = (where) => {
    if (active.value == 'consumablesPackage') {
      tableRef.value?.reload?.({
        page: 1,
        where: { ...where, overdue: form.overdue?.[0] === '1' ? '1' : null }
      });
    } else {
      tableRef1.value?.reload?.({ page: 1, where });
    }
  };

  /** 导出和打印全部数据的数据源 */
  const exportSource = () => {
    const url =
      active.value == 'consumablesPackage'
        ? exportConsumablesPackageStat
        : exportBulkConsumablesStat;
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    const tableNode =
      active.value == 'consumablesPackage' ? tableRef.value : tableRef1.value;
    tableNode?.fetch?.(({ where, orders }) => {
      url({ ...where, ...orders })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
    // url(params)
    //   .then(() => {
    //     loading.close();
    //   })
    //   .catch((e) => {
    //     loading.close();
    //     EleMessage.error(e.message);
    //   });
  };

  onMounted(() => {
    const { query } = unref(currentRoute);
    if (query?.overdue) {
      form.overdue = [query.overdue];
    }
    reload(form);
  });
</script>
