<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        v-model:selections="selections"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button @click="exportData" v-permission="'stats:sample:export'">
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        cache-key="inventory-query"
        :export-config="{ fileName: '库存列表', datasource: exportSource }"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #sampleNum="{ row }">
          <el-link type="primary" :underline="false" @click="handleClick(row)">
            {{ row.sampleNum }}
          </el-link>
        </template>
        <template #projectAttribute="{ row }">
          <span>{{
            row.projectAttribute == 1
              ? '注册'
              : row.projectAttribute == 2
                ? '科研'
                : ''
          }}</span>
        </template>
        <template #registrationDeclaration="{ row }">
          <span>{{
            row.registrationDeclaration == 1
              ? '是'
              : row.registrationDeclaration == 2
                ? '否'
                : ''
          }}</span>
        </template>
        <template #settlementStatus="{ row }">
          <!-- 费用结算状态（1待结算 2已结算 3无费用 4已完结） -->
          <span>{{
            row.settlementStatus == 1
              ? '待结算'
              : row.settlementStatus == 2
                ? '已结算'
                : row.settlementStatus == 3
                  ? '无费用'
                  : row.settlementStatus == 4
                    ? '已完结'
                    : ''
          }}</span>
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="handleClick(row)"
            v-permission="'stats:sample:query'"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <sampleDialog
      ref="sampleDialogRef"
      v-model="visibleModal"
      :data="editData"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useI18n } from 'vue-i18n';
  import {
    getSampleStatList,
    exportSampleStat
  } from '@/api/statistics/coldchain';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import sampleDialog from './sampleDialog.vue';

  const { push } = useRouter();

  const { t } = useI18n();

  const { mobileDevice } = useMobileDevice();
  const visibleModal = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    username: '',
    organizationName: '',
    phone: '',
    email: '',
    sex: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    },
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '公司项目编号',
      prop: 'companyProjectNumber'
    },
    {
      type: 'input',
      label: '申办方',
      prop: 'sponsor'
    },
    {
      type: 'select',
      label: '项目属性',
      prop: 'projectAttribute',
      options: [
        {
          label: '注册',
          value: 1
        },
        {
          label: '科研',
          value: 2
        }
      ]
    },
    {
      type: 'date',
      label: '结束日期',
      prop: 'endDate'
    },
    {
      type: 'select',
      label: '是否仅显示有样本',
      prop: 'onlyShowSample',
      options: [
        {
          label: '是',
          value: 1
        }
      ]
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 55,
        label: '序号',
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'analyseProjectNumber',
        label: '分析项目编号',
        align: 'left',
        width: 150
      },
      {
        prop: 'companyProjectNumber',
        label: '公司项目编号',
        align: 'left',
        width: 150
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        align: 'left',
        width: 150
      },
      {
        prop: 'projectName',
        label: '项目名称',
        align: 'left',
        width: 180
      },
      {
        prop: 'sponsor',
        label: '申办方',
        align: 'left',
        width: 150
      },
      {
        prop: 'projectAttribute',
        label: '项目属性',
        align: 'left',
        width: 120,
        slot: 'projectAttribute'
      },
      {
        prop: 'sampleNum',
        label: '样本总数',
        align: 'center',
        slot: 'sampleNum'
      },
      {
        prop: 'endDate',
        label: '项目结束日期',
        align: 'center',
        width: 150
      },
      {
        prop: 'registrationDeclaration',
        label: '是否申报',
        align: 'center',
        width: 120,
        slot: 'registrationDeclaration'
      },
      // {
      //   prop: 'username3',
      //   label: '是否大包',
      //   align: 'center'
      // },
      {
        prop: 'sampleChargeDate',
        label: '开始计费日期',
        align: 'center',
        width: 150
      },
      {
        prop: 'confirmDate',
        label: '客户确认处理日期',
        align: 'center',
        width: 150
      },
      {
        prop: 'handleDate',
        label: '销毁/取回日期',
        align: 'center',
        width: 150
      },
      {
        prop: 'settlementStatus',
        label: '费用结算状态',
        align: 'center',
        width: 150,
        slot: 'settlementStatus'
      },
      {
        prop: 'payer',
        label: '付款方',
        align: 'left',
        width: 150
      },
      {
        prop: 'action',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 100,
        slot: 'action'
      }
    ];
  });

  const selections = ref([]);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const editData = ref({});

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getSampleStatList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};
  /** 搜索事件 */
  const onSearch = (where) => {
    const [d1, d2] = where.createTime ?? [];
    const time = {
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    };
    Object.assign(lastWhere, where, time);
    doReload();
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 表格搜索 */
  const doReload = () => {
    reload(lastWhere);
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where, orders, filters }) => {
      exportSampleStat({ ...where, ...orders, ...filters })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
  const handleClick = (row) => {
    console.log(row);
    editData.value = row;
    visibleModal.value = true;
  };
</script>
