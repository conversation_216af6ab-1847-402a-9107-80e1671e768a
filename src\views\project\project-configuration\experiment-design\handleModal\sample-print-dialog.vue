<template>
  <div>
    <ele-modal
      form
      :width="750"
      title="样本管标签打印"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <div class="flex">
        <div
          v-for="(item, index) in data"
          :key="index"
          class="flex-col"
        >
          <div class="ellipsis-1" :style="{ fontSize: getDynamicFontSize(schemeNumber) + 'px' }">{{ schemeNumber }}</div>
          <div :class="{'text-algin-right': designType === '3', 'ellipsis-1': true}" :style="{ fontSize: getDynamicFontSize(pointInfo(item)) + 'px' }">
            {{ pointInfo(item) }}
          </div>
          <div class="ellipsis-1" :style="{ fontSize: getDynamicFontSize(tubeInfo(item)) + 'px' }">
            {{ tubeInfo(item) }}
          </div>
          <ele-bar-code
            :value="item.systemId"
            :tag="tag"
            :options="options"
          />
        </div>
      </div>
      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" @click="save"> 确定打印 </el-button>
        </div>
      </template>
    </ele-modal>
    <ele-printer
      v-model="printing"
      margin="0mm 12mm 10mm 12mm"
      :header-style="{
        padding: '26px 0 2px 0',
        fontSize: '13px',
        borderBottom: '1px solid #666',
        marginBottom: '26px'
      }"
      :body-style="{ fontSize: '14px', lineHeight: 2.5 }"
      target="_iframe"
      :static="false"
      @done="handlePrintDone"
    >
      <div v-for="(item, index) in data" :key="index">
        <div class="flex-col">
          <div class="ellipsis-1" :style="{ fontSize: getDynamicFontSize(schemeNumber) + 'px' }">{{ schemeNumber }}</div>
          <span :class="{'text-algin-right': designType === '3', 'ellipsis-1': true}" :style="{ fontSize: getDynamicFontSize(pointInfo(item)) + 'px' }">
           {{ pointInfo(item) }}
          </span>
          <div class="ellipsis-1" :style="{ fontSize: getDynamicFontSize(tubeInfo(item)) + 'px' }">
            {{ tubeInfo(item) }}
          </div>
          <ele-bar-code
            :value="item.systemId"
            :tag="tag"
            :options="options"
          />
        </div>
      </div>
    </ele-printer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, reactive, nextTick, inject} from 'vue';
  import type { MessageHandler as ElMessageHandler } from 'element-plus/es/components/message';
  import { EleMessage } from 'ele-admin-plus';
  let loading: ElMessageHandler;

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    data: {
      type: Array,
      default: () => []
    }
  });

  /** 渲染方式 */
  const tag = ref('img');

  const projectInfo = inject('projectInfo');

  const { schemeNumber, designType } = projectInfo;

  /** 参数配置 */
  const options = reactive({
    height: 20,
    width: 1,
    marginTop: 2,
    marginLeft: 0,
    marginRight: 0,
    marginBottom: 0,
    displayValue: true,
    textPosition: 'bottom',
    fontSize: 12,
    format: 'CODE128'
  });

  // 动态计算字体大小
  const getDynamicFontSize = (text: any): number => {
    if (!text) return 12;

    // 计算字符长度，汉字按2个字符计算
    const getTextLength = (str: string): number => {
      let length = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        // 判断是否为汉字（Unicode范围）
        if (/[\u4e00-\u9fa5]/.test(char)) {
          length += 2; // 汉字按2个字符计算
        } else {
          length += 1; // 英文、数字等按1个字符计算
        }
      }
      return length;
    };

    const textLength = getTextLength(text.toString());

    // 根据字符长度计算字体大小
    // 已知：24字符时字体为8px
    // 最大字体：12px，最小字体：8px
    let fontSize: number;

    if (textLength <= 12) {
      // 字符较少时使用最大字体
      fontSize = 12;
    } else if (textLength >= 24) {
      // 24字符及以上时使用最小字体8px
      fontSize = 8;
    } else {
      // 线性插值计算字体大小 (12字符到24字符之间)
      // 当textLength=12时，fontSize=12
      // 当textLength=24时，fontSize=8
      // 使用线性函数：fontSize = 12 - (textLength - 12) * (12 - 8) / (24 - 12)
      fontSize = 11 - ((textLength - 12) * 4) / 12;
      fontSize = Math.max(8, Math.min(11, fontSize)); // 确保在8-12范围内
    }

    return Math.round(fontSize);
  };

  // 采样点信息
  const pointInfo = (item) => {
    const { content, sampleNumber, groupName, subjectNumber } = item;
    const pointContent = JSON.parse(content);
    // 访视类型 受试者编号/访视/时间/样本序号
    if(designType === '3'){
      const {visitNumber,samplePointTime} = pointContent;
      return `-${visitNumber}-${samplePointTime}-${sampleNumber}`
    } else {
      const { cycleName, samplePointDay,samplePointYear,samplePointTime,samplePointMonth,samplePointWeek } = pointContent;
      const time = [cycleName,
            samplePointYear,
            samplePointMonth,
            samplePointWeek,
            samplePointDay, 
            samplePointTime]
            .filter(item => item) // 过滤掉空值
            .join('-')
      return `${subjectNumber}-${time}-${sampleNumber}`
    }
  }

  // 采样管明细
  const tubeInfo = (item) => {
    const { testingIndex, sampleType , packagingNumber} = item;
     // 访视类型 检测指标/样品类型
      return `${testingIndex}-${sampleType}-${packagingNumber}`
  }

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    loading && loading.close();
  };
  const printing = ref(false);
  const save = () => {
    loading = EleMessage.loading({
      message: '正在打印中..',
      plain: true,
      centered: true,
      mask: true
    });
    nextTick(() => {
      printing.value = true;
    });
  };
  /** 打印结束事件 */
  const handlePrintDone = () => {
    printing.value = false;
    loading && loading.close();
  };
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    max-height: 500px;
    overflow: auto;
  }
  .flex-col {
    width: 120px;
    text-align: center;
    font-size: 11px;
    line-height: 12px;
    height: 110px;
    padding: 5px 15px 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .flex-col-title {
    width: 100%;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ellipsis-1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    width: 110px;
    text-overflow: ellipsis;
    text-align: center;
    white-space: nowrap;
  }
  .text-algin-right{
    margin-left: 30px;
  }
</style>
