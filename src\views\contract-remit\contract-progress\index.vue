<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div class="top-title">
          <div class="line"></div>
          <span style="font-size: 15px; font-weight: bold">进度节点</span>
        </div>
        <el-button
          type="primary"
          :icon="UploadOutlined"
          class="ele-btn-icon"
          @click="openAddNode()"
          v-permission="'project:contractNode:add'"
        >
          新增节点
        </el-button>
      </div>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :pagination="false"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '基础列表数据', datasource: exportSource }"
        :print-config="{ datasource: exportSource }"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="normal-table"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'sort', order: 'asc' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #completedProgress="{ row }">
          <el-select
            v-model="row.completedProgress"
            placeholder="请选择完成进度"
            @change="changeCompletedProgress(row, true)"
            :disabled="!hasPermission('project:contractNode:mark')"
          >
            <el-option label="进展中" value="1" />
            <el-option label="已完成" value="2" />
            <el-option label="取消" value="3" />
          </el-select>
        </template>
        <template #collectionProgress="{ row }">
          <el-select
            :disabled="
              row.completedProgress === '1' ||
              !hasPermission('project:contractNode:mark')
            "
            v-model="row.collectionProgress"
            placeholder="请选择回款进度"
            @change="changeCompletedProgress(row)"
          >
            <el-option label="待结算" value="1" />
            <el-option label="待回款" value="2" />
            <el-option label="已回款" value="3" />
          </el-select>
        </template>
        <template #invoicingProgress="{ row }">
          <el-select
            :disabled="
              row.completedProgress === '1' ||
              !hasPermission('project:contractNode:mark')
            "
            v-model="row.invoicingProgress"
            placeholder="请选择开票进度"
            @change="changeCompletedProgress(row)"
          >
            <el-option label="待开票" value="1" />
            <el-option label="已开票" value="2" />
          </el-select>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="openUpload(row)">
            上传附件
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'project:contractNode:edit'"
          />
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row)"
            v-permission="'project:contractNode:edit'"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'project:contractNode:remove'"
          />
          <el-link
            type="danger"
            underline="never"
            @click.stop="remove(row)"
            v-permission="'project:contractNode:remove'"
          >
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
        <template #fileNum="{ row }">
          <el-link
            v-if="row.fileNum > 0"
            type="primary"
            underline="never"
            @click.stop="openFileName(row)"
          >
            {{ row.fileNum }}
          </el-link>
          <span v-else>{{ row.fileNum }}</span>
        </template>
      </ele-pro-table>
      {{ currentSort }}
    </ele-card>
    <progress-edit
      ref="consumableEditRef"
      v-model="visibleModal"
      :currentSort="currentSort"
      @done="reload"
    />
    <!-- 上传文件弹窗 -->
    <el-dialog
      title="编辑节点"
      v-model="uploadVisible"
      :before-close="handleClose"
      width="480px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="序号" prop="sort">
          <el-input
            type="number"
            v-model="form.sort"
            placeholder="请输入序号"
          />
        </el-form-item>
        <el-form-item label="合同节点" prop="nodeName">
          <el-input
            v-model="form.nodeName"
            maxlength="50"
            show-word-limit
            placeholder="请输入合同节点"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="上传文件"
      v-model="uploadVisible2"
      :before-close="handleClose2"
      width="680px"
      append-to-body
      destroy-on-close
    >
      <div class="upload-form">
        上传文件
        <FileUpload
          style="flex: 1; margin-left: 20px"
          :ref="itemComponentRef"
          v-model="sysFileIds"
          :multiple="true"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose2">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit2"
            >确定上传</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="查看文件"
      v-model="uploadVisible3"
      :before-close="handleClose3"
      width="880px"
      append-to-body
      destroy-on-close
    >
      <!-- <div class="upload-form">
        <div
          v-for="item in fileList"
          :key="item.fileId"
          style="width: 100%; padding-bottom: 10px"
        >
          <el-link
            type="primary"
            :underline="false"
            @click="handleDownload(item)"
            >{{ item.fileName }}</el-link
          >
        </div>
      </div> -->
      <ContractFile :isDialog="true" :nodeId="nodeId" style="height: 60vh" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose3">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useI18n } from 'vue-i18n';
  import { UploadOutlined } from '@/components/icons';
  import FileUpload from '@/components/FileUpload/index.vue';
  import {
    getContractNodeList,
    removeContractNode,
    editContractNode,
    markContractNode,
    addContractFile
  } from '@/api/contract';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import progressEdit from './progress-edit.vue';
  import download from '@/utils/download';
  import { Base64 } from 'js-base64';
  import ContractFile from '../contract-file/index.vue';
  import { usePermission } from '@/utils/use-permission';

  const { hasPermission } = usePermission();
  const { push } = useRouter();

  const { t } = useI18n();

  const { mobileDevice } = useMobileDevice();
  defineOptions({ name: 'ContractProgress' });

  /** 表格实例 */
  const tableRef = ref(null);
  const loading = ref(false);
  const formRef = ref(null);
  const uploadVisible = ref(false);
  const uploadVisible2 = ref(false);
  const uploadVisible3 = ref(false);
  const sysFileIds = ref([]);
  const itemObject = ref({});
  const rules = reactive({
    nodeName: [{ required: true, message: '请输入合同节点', trigger: 'blur' }],
    sort: [{ required: true, message: '请输入序号', trigger: 'blur' }]
  });
  /** 表单数据 */
  const form = reactive({
    nodeName: '',
    nodeId: '',
    sort: ''
  });
  const openUpload = (row) => {
    uploadVisible2.value = true;
    itemObject.value = row;
    sysFileIds.value = [];
  };
  const markNode = (row) => {
    markContractNode({
      nodeId: row.nodeId,
      completedProgress: row.completedProgress,
      collectionProgress: row.collectionProgress,
      invoicingProgress: row.invoicingProgress
    }).then((res) => {
      EleMessage.success('操作成功');
      reload();
    });
  };
  const changeCompletedProgress = (row, isCompletedProgress) => {
    // 选中 已完成 回款/开票进度 需默认填写 待回款/待开票
    if (isCompletedProgress && row.completedProgress === '2') {
      row.collectionProgress = '2';
      row.invoicingProgress = '1';
      markNode(row);
    } else {
      markNode(row);
    }
  };
  const submit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // loading.value = true;
      editContractNode({
        nodeId: form.nodeId,
        nodeName: form.nodeName,
        sort: form.sort
      }).then((res) => {
        EleMessage.success('操作成功');
        handleClose();
        reload();
      });
    });
  };
  const submit2 = () => {
    let params = {
      nodeId: itemObject.value.nodeId,
      addList: []
    };
    if (sysFileIds.value && sysFileIds.value.length == 0) {
      return EleMessage.error('请上传附件');
    }
    const sysFileIdsArray = JSON.parse(sysFileIds.value || '[]');
    if (sysFileIdsArray.length > 0) {
      params.addList = sysFileIdsArray.map((item) => {
        return {
          sysFileIds: item.ossId,
          fileName: item.name
        };
      });
    } else {
      return EleMessage.error('请上传附件');
    }
    console.log('提交参数=====>', params);
    addContractFile(params).then((res) => {
      EleMessage.success('上传成功');
      handleClose2();
      reload();
    });
  };
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      //   {
      //     type: 'selection',
      //     columnKey: 'selection',
      //     width: 50,
      //     align: 'center',
      //     fixed: 'left'
      //   },
      // {
      //   type: 'index',
      //   columnKey: 'index',
      //   label: '序号',
      //   width: 55,
      //   align: 'center',
      //   fixed: 'left'
      // },
      {
        prop: 'sort',
        label: '序号',
        sortable: 'custom'
      },
      {
        prop: 'nodeName',
        label: '合同节点'
      },
      {
        prop: 'completedProgress',
        label: '完成进度',
        slot: 'completedProgress'
      },
      {
        prop: 'collectionProgress',
        label: '回款进度',
        slot: 'collectionProgress'
      },
      {
        prop: 'invoicingProgress',
        label: '开票进度',
        slot: 'invoicingProgress'
      },
      {
        prop: 'fileNum',
        label: '节点证明材料',
        slot: 'fileNum'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 240,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');

  /** 表格数据源 */
  const datasource = async ({ pages, where, orders }) => {
    const data = await getContractNodeList({ ...where, ...pages, ...orders });
    if (data.length) {
      currentSort.value = data[data.length - 1].sort;
    }
    return data;
  };
  const handleClose = () => {
    uploadVisible.value = false;
  };
  const handleClose2 = () => {
    uploadVisible2.value = false;
  };

  const handleClose3 = () => {
    uploadVisible3.value = false;
  };
  /** 表格数据请求完成事件 */
  const onDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
      const ids = [45, 47, 48];
      tableRef.value?.setSelectedRowKeys?.(ids);
    });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 修改搜索框背景色 */
  const currentSort = ref(0);
  const openAddNode = () => {
    handle.value = 'add';
    editData.value = null;
    visibleModal.value = true;
  };
  /** 编辑 */
  const openEdit = (row) => {
    editData.value = row || null;
    form.nodeName = row.nodeName;
    form.nodeId = row.nodeId;
    form.sort = row.sort;
    uploadVisible.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        removeContractNode(row.nodeId).then((res) => {
          EleMessage.success('删除成功');
          reload();
        });
      })
      .catch(() => {});
  };

  /** 导出和打印全部数据的数据源 */
  const exportSource = ({ where, orders, filters }) => {
    return listUsers({ ...where, ...orders, ...filters });
  };
  const nodeId = ref('');
  const openFileName = (row) => {
    console.log('row====>', row);
    // window.open(row.fileName);
    uploadVisible3.value = true;
    nodeId.value = row.nodeId;
  };
  const handleDownload = (row) => {
    // download.oss(row.sysFileIds); // 下载文件
    console.log('row======>handleDownload2', row);
    var url = row.sysFileList[0].url;
    window.open(
      'https://kkfile.520gcp.com/onlinePreview?url=' +
        encodeURIComponent(Base64.encode(url))
    );
  };
</script>
<style lang="scss" scoped>
  .upload-form {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-direction: column;
  }

  .top-title {
    display: flex;
    align-items: center;
    .line {
      width: 3px;
      height: 18px;
      background: #007aff;
      margin-right: 5px;
    }
  }
</style>
