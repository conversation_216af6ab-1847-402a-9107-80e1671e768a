<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="true"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <ele-tooltip
            content="单个新增"
            type="warning"
            placement="top"
            bg="linear-gradient( 135deg, #43CBFF 10%, #9708CC 100%)"
            arrow-bg="#7556E0"
            :offset="8"
          >
            <el-button
              type="primary"
              :icon="PlusOutlined"
              class="ele-btn-icon"
              @click="openEdit()"
              v-permission="'system:consumable:add'"
            >
              新增
            </el-button>
          </ele-tooltip>
          <el-button plain @click="exportData()" v-permission="'system:consumable:export'"> 导出 </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '基础列表数据', datasource: exportSource }"
        :print-config="{ datasource: exportSource }"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="normal-table"
        :border="tableBorder"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #consumableType="{ row }">
          <dict-data
            code="consumable_type"
            type="tag"
            :model-value="row.consumableType"
          />
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <!-- <el-link
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            查看
          </el-link>
          <el-divider direction="vertical" /> -->
          <el-link type="primary" underline="never" @click.stop="openEdit(row)" v-permission="'system:consumable:edit'">
            修改
          </el-link>
          <el-divider direction="vertical" v-permission="'system:consumable:remove'" />
          <el-link type="danger" underline="never" @click.stop="remove(row)" v-permission="'system:consumable:remove'">
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <consumable-edit
      ref="consumableEditRef"
      v-model="visibleModal"
      :data="editData"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    listConsumable,
    removeConsumable,
    exportConsumable
  } from '@/api/system/consumable';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import consumableEdit from './components/consumable-edit.vue';
  import { useDictData } from '@/utils/use-dict-data';
  const { push } = useRouter();
  const { mobileDevice } = useMobileDevice();
  /** 表格实例 */
  const tableRef = ref(null);

  /** 用户名筛选值 */
  const nicknameFilterValue = ref('');

  /** 表单数据 */
  const form = reactive({
    consumableName: '',
    consumableBrand: '',
    consumableType: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName',
      props: {
        labelWidth: '120px'
      }
    },
    {
      type: 'input',
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      type: 'input',
      label: '耗材ID',
      prop: 'systemId'
    },
    {
      type: 'dictSelect',
      label: '耗材类型',
      prop: 'consumableType',
      props: { code: 'consumable_type' }
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'systemId',
        label: '耗材ID',
        width: 110,
        align: 'center'
      },
      {
        prop: 'consumableType',
        label: '耗材类型',
        minWidth: 110,
        align: 'center',
        slot: 'consumableType'
      },
      {
        prop: 'consumableName',
        label: '耗材名称',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableSize',
        label: '规格尺寸',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableMaterial',
        label: '颜色材质',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableBrand',
        label: '品牌',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableCountingUnit',
        label: '计数单位',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumablePackagingUnit',
        label: '包装单位',
        minWidth: 140,
        align: 'center'
      },
      {
        prop: 'consumablePackagingSpecification',
        label: '包装规格',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableDeliveryDay',
        label: '采购货期/天数',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'consumableThreshold',
        label: '耗材库存提醒阈值',
        minWidth: 150,
        align: 'center'
      },
      {
        prop: 'remark',
        label: '备注',
        minWidth: 110,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        minWidth: 160,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');
  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    console.log(pages, where);
    return listConsumable({ ...where, ...pages, orderBy: '', isAsc: 'asc' });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
      const ids = [45, 47, 48];
      tableRef.value?.setSelectedRowKeys?.(ids);
    });
  };
  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportConsumable({ ...where, ...pages, orderBy: '', isAsc: 'asc' })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  /** 搜索事件 */
  const onSearch = (where) => {
    const [d1, d2] = where.createTime ?? [];
    const time = {
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    };
    Object.assign(lastWhere, where, time);
    doReload();
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  /** 编辑 */
  const openEdit = (row) => {
    handle.value = row ? 'edit' : 'add';
    editData.value = row || null;
    visibleModal.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        removeConsumable(row.consumableId).then((res) => {
          if (res.code === 200) {
            EleMessage.success('操作成功');
            doReload();
          }
        });
      })
      .catch(() => {});
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nicknameFilterValue.value) {
      reload({
        ...lastWhere,
        nickname: nicknameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
</script>
