<template>
  <div>
    <ele-modal
      form
      :width="600"
      :title="`${props.handle === 'add' ? '新增' : props.handle === 'edit' ? '编辑' : '确认'}试验设计类型`"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <ele-check-card
        v-model="checked"
        :items="items"
        :disabled="props.handle === 'confirm'"
        :item-style="{ margin: '8px', padding: '8px 12px', display: 'flex' }"
        style="display: flex; flex-wrap: wrap"
      />
      <ele-text
        v-if="props.handle === 'edit'"
        type="warning"
        :icon="Warning"
        size="md"
        >修改后会全部重置已编辑的采样点内容！</ele-text
      >
      <ele-text
        v-if="props.handle === 'confirm'"
        type="success"
        :icon="InfoFilled"
        size="md"
        >确认后试验设计类型将不能再修改！</ele-text
      >
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          确 定
        </el-button>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, watch, inject, computed } from 'vue';
  import { Warning, InfoFilled } from '@element-plus/icons-vue';
  import {
    addSampleDesignType,
    updateSampleDesignType,
    confirmSampleDesignType
  } from '@/api/project/project-configuration/experiment-design';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  const projectInfo = inject('projectInfo');

  // 使用computed来响应projectId的变化
  const projectId = computed(() => projectInfo?.projectId);

  const visible = defineModel({ type: Boolean, default: false });

  const props = defineProps({
    /** 操作 */
    handle: {
      type: String,
      default: 'add'
    }
  });

  const emit = defineEmits(['changeType']);

  const items = ref([
    { label: '研究周期+采样时间', value: '1' },
    { label: '周期+年/月/周/日+采样时间', value: '2' },
    { label: '访视+采样点', value: '3' }
  ]);

  const checked = ref();

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 保存编辑 */
  const save = () => {
    const saveOrUpdate =
      props.handle === 'add'
        ? addSampleDesignType
        : props.handle === 'edit'
          ? updateSampleDesignType
          : confirmSampleDesignType;
    saveOrUpdate({
      projectId: projectId.value,
      designType: checked.value
    }).then((msg) => {
      EleMessage.success(msg);
      emit('changeType', checked.value);
      visible.value = false;
    });
  };

  watch(
    () => projectInfo,
    (val) => {
      checked.value = val?.designType;
    },
    { immediate: true }
  );
</script>
<style lang="less" scoped>
  .ele-text {
    margin-left: 10px;
  }
</style>
