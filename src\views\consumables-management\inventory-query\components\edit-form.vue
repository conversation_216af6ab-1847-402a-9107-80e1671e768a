<!-- 添加和修改的表单 -->
<template>
  <pro-form
    ref="formRef"
    :model="form"
    :rules="rules"
    :items="items"
    :footer="true"
    :label-width="100"
    :grid="{ span: 24 }"
    @updateValue="setFieldValue"
    :footerStyle="{ justifyContent: 'flex-end' }"
  >
    <template #consumableName>
      <el-input v-model="consumableName1" disabled />
    </template>
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </pro-form>
</template>

<script setup>
  import ProForm from '@/components/ProForm/index.vue';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import {
    inventoryCkeck,
    getBatchNumberList
  } from '@/api/consumables-management/inventory-query';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });

  const emit = defineEmits(['close', 'success']);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    consumableType: '',
    consumableName: '',
    batchNumber: '',
    endDate: '',
    quantity: '',
    remark: '',
    consumableId: '',
    inventoryId: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '耗材类型',
      prop: 'consumableType',
      type: 'dictSelect',
      required: true,
      props: {
        code: 'consumable_type',
        disabled: true
      }
    },
    {
      label: '耗材名称',
      prop: 'consumableName',
      type: 'consumableName',
      required: true
    },
    {
      label: '批号',
      prop: 'batchNumber',
      type: 'select', // 自定义组件
      options: [],
      props: {
        filterable: true,
        allowCreate: true
      },
      required: true,
      disabled: true
    },
    {
      label: '有效到期日',
      prop: 'endDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' },
      required: true
    },
    {
      label: '库存数量',
      prop: 'quantity',
      type: 'inputNumber',
      required: true
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea'
    }
  ]);

  /** 表单验证规则 */
  const rules = reactive({});
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      inventoryCkeck(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    emit('close');
  };

  const batchNumberList = ref([]);
  //获取批号列表
  const getBatchNumberListFun = async () => {
    const res = await getBatchNumberList({
      consumableId: props.data.consumableId
    });

    if (res.data.length !== 0) {
      batchNumberList.value = res.data.map((el) => {
        return {
          label: el.batchNumber,
          value: el.batchNumber
        };
      });
    }
    setPropItem('batchNumber', batchNumberList.value);
  };
  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };
  const consumableName1 = ref('');
  watch(
    () => props.data,
    () => {
      if (props.data) {
        getBatchNumberListFun();
        assignFields({
          ...props.data,
          remark: ''
        });
        consumableName1.value = `${props.data.consumableName}/${props.data.consumableSize}/${props.data.consumableMaterial}/${props.data.consumableBrand}`;
      }
    },
    { immediate: true }
  );
</script>
<style lang="scss" scoped>
  flex {
    display: flex;
    justify-content: flex-end;
  }
</style>
