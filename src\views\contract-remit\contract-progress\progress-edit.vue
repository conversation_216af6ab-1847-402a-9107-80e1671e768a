<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="700"
    v-model="visible"
    :title="isUpdate ? '修改合同节点' : '新增合同节点'"
    @open="handleOpen"
  >
    <el-table :data="tableList">
      <!-- <el-table-column type="index" label="序号" width="55" /> -->
      <el-table-column prop="sort" label="序号" width="180">
        <template #default="scope">
          <el-input-number v-model="scope.row.sort" placeholder="请输入序号" />
        </template>
      </el-table-column>
      <el-table-column prop="nodeName" label="合同节点">
        <template #default="scope">
          <el-input
            v-model="scope.row.nodeName"
            maxlength="50"
            show-word-limit
            placeholder="请输入合同节点"
          />
        </template>
      </el-table-column>
      <!-- 操作栏 -->
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-button type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="btn-add">
      <el-button type="text" :icon="PlusOutlined" @click="handleAdd"
        >添加节点</el-button
      >
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="save"
        :disabled="tableList.length === 0"
      >
        确定
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, nextTick, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { addContractNode } from '@/api/contract';
  import { PlusOutlined } from '@/components/icons';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object,
    currentSort: Number
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  const tableList = ref([]);
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    nodeName: '',
    projectId: '',
    sort: ''
  });
  const handleDelete = (row) => {
    tableList.value = tableList.value.filter((item) => item !== row);
  };

  const sort = ref(0);

  const createSort = () => {
    sort.value++;
    return sort.value;
  };

  const handleAdd = () => {
    tableList.value.push({
      nodeName: '',
      projectId: '',
      sort: createSort()
    });
  };
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
    tableList.value = [];
  };

  /** 保存编辑 */
  const save = () => {
    loading.value = true;
    let dd = tableList.value.filter((val) => !val.nodeName);
    console.log(dd);
    if (dd && dd.length > 0) {
      EleMessage.error('请输入合同节点');
      loading.value = false;
      return;
    }
    let dd2 = tableList.value.filter((val) => !val.sort);
    if (dd2 && dd2.length > 0) {
      EleMessage.error('请输入序号');
      loading.value = false;
      return;
    }
    let list = tableList.value.map((item) => {
      return {
        nodeName: item.nodeName,
        projectId: item.projectId,
        sort: item.sort
      };
    });
    console.log('提交参数=====>', list);
    addContractNode({
      addList: list
    })
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields(props.data);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };

  watch(
    () => visible.value,
    (val) => {
      if (val) {
        sort.value = props.currentSort;
        tableList.value = [];
      }
    }
  );
</script>
<style lang="scss" scoped>
  .btn-add {
    display: flex;
    justify-content: center;
    padding: 10px 0 10px 0;
    border-bottom: 1px solid #e4e7ed;
  }
</style>
