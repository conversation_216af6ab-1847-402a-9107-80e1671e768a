<template>
  <ele-card
    :body-style="{ paddingBottom: '4px' }"
    :header="`样本${handle === 'handle' ? '处理' : formHandle === 'despatch' ? '发运' : formHandle === 'receive' ? '接收' : ''}明细`"
    style="margin-bottom: 16px"
  >
    <template #extra>
      <div v-if="mode !== 'view'" style="display: flex; gap: 8px">
        <el-input
          v-model="importText"
          @input="importSamples"
          placeholder="手动输入11位或扫码枪录入自动添加，注意光标在文本框内"
          style="width: 400px"
          clearable
        />
        <!-- <el-button type="primary" @click="handleImportSamples">
          确定添加
        </el-button> -->
        <!-- <el-button type="success" @click="handleSaveSelected">
          检索添加
        </el-button> -->
      </div>
    </template>

    <el-form ref="formRef" :model="formModel">
      <ele-data-table
        row-key="id"
        :columns="columns"
        :data="tableData"
        @selection-change="handleSelectionChange"
        cell-class-name="editable-table-cell"
        class="editable-table"
      >
        <!-- 试管编号 -->
        <template #subjectNumber="{ row, $index }">
          <!-- 类型为1,2时不能修改，类型为3时才能修改 -->
          <el-form-item
            v-if="mode !== 'view' && handle !== 'handle' && designType === '3'"
            :prop="'tableData.' + $index + '.subjectNumber'"
            style="margin-bottom: 0"
          >
            <el-input
              v-model="row.subjectNumber"
              placeholder="请输入试管编号"
              style="width: 100%"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <span v-else>{{ row.subjectNumber }}</span>
        </template>
        <!-- 操作 -->
        <template #action="{ $index }">
          <el-link
            v-if="mode !== 'view'"
            type="primary"
            underline="never"
            @click="handleDeleteRow($index)"
          >
            删除
          </el-link>
        </template>
      </ele-data-table>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, computed, inject, reactive } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useDictData } from '@/utils/use-dict-data';
  import { getSampleLabelInfo } from '@/api/project/project-configuration/experiment-design';
  import { getSampleInventoryInfo } from '@/api/project/sample-management';

  // 使用 defineModel 简化双向绑定
  const tableData = defineModel({
    type: Array,
    default: () => []
  });

  const props = defineProps({
    mode: {
      type: String,
      default: 'add' // add/edit/view
    },
    formHandle: {
      type: String,
      default: 'receive'
    }
  });
  const projectInfo = inject('projectInfo');
  const handle = inject('handle');

  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');
  const [sampleTypeDicts] = useDictData(['sample_type']);

  const formRef = ref(null);
  const importText = ref('');

  /** 表格选中数据 */
  const selections = ref([1, 2]);

  // 表单模型（避免响应式循环）
  const formModel = reactive({
    tableData: tableData
  });

  // 表格列配置
  const columns = computed(() => {
    const baseColumns = [];

    baseColumns.push(
      {
        type: 'index',
        columnKey: 'index',
        width: 60,
        label: '序号',
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'systemId',
        label: '样本ID',
        minWidth: 150
      },
      {
        label: '试验组别',
        prop: 'groupName',
        minWidth: 110
      }
    );

    if (designType.value === '1' || designType.value === '2') {
      baseColumns.push({
        label: '周期',
        prop: 'cycleName',
        minWidth: 80
      });
    }

    /** 类型为2 的列*/
    if (designType.value === '2') {
      baseColumns.push(
        {
          prop: 'samplePointYear',
          label: '年',
          minWidth: 80
        },
        {
          prop: 'samplePointMonth',
          label: '月',
          minWidth: 80
        },
        {
          prop: 'samplePointWeek',
          label: '周',
          minWidth: 80
        },
        {
          prop: 'samplePointDay',
          label: '日',
          minWidth: 80
        }
      );
    }

    /** 类型为3 的列*/
    if (designType.value === '3') {
      baseColumns.push(
        {
          label: '访视号',
          prop: 'visitNumber',
          minWidth: 110,
          hideInTable: designType.value !== '3'
        },
        {
          label: '研究时间',
          prop: 'studyTime',
          minWidth: 110,
          hideInTable: designType.value !== '3'
        }
      );
    }

    baseColumns.push(
      /** 类型为1、2、3 的列*/
      {
        label: '采样时间点',
        prop: 'samplePointTime',
        minWidth: 110
      },
      // 公用字段
      {
        label: '样本序号',
        prop: 'sampleNumber',
        minWidth: 90
      },
      {
        label: '检测指标',
        prop: 'testingIndex',
        minWidth: 110
      },
      {
        label: '样本类型',
        prop: 'sampleType',
        formatter: (row) => {
          return sampleTypeDicts.value.find(
            (d) => d.dictValue === row.sampleType
          )?.dictLabel;
        },
        minWidth: 180
      },
      {
        label: '分装号',
        prop: 'packagingNumber',
        minWidth: 80
      },
      {
        label: '受试者编号',
        prop: 'subjectNumber',
        minWidth: 150,
        fixed: 'right',
        slot: 'subjectNumber'
      }
    );

    if (handle === 'handle') {
      baseColumns.push(
        {
          label: '机构代号',
          prop: 'hospitalCode',
          minWidth: 80
        },
        {
          label: '研究机构',
          prop: 'hospitalName',
          minWidth: 120
        },
        {
          label: '接收日期',
          prop: 'createDate',
          minWidth: 120
        }
      );
    }

    // 只有非查看模式才显示选择框
    if (props.mode !== 'view') {
      baseColumns.push({
        label: '操作',
        columnKey: 'action',
        width: 80,
        fixed: 'right',
        align: 'center',
        slot: 'action'
      });
    }

    return baseColumns;
  });

  const handleSelectionChange = (val) => {
    selections.value = val;
  };

  const importSamples = (value) => {
    if (value.length === 11) {
      handleImportSamples(value);
    }
  };

  // defineModel 会自动处理双向绑定，不需要手动监听

  // 导入样本
  const handleImportSamples = async () => {
    const inputValue = importText.value.trim();
    if (!inputValue) return;
    const hasSamples = tableData.value.filter(
      (item) => item.systemId === inputValue
    );
    if (hasSamples.length) {
      EleMessage.error('样本ID已存在，请勿重复添加！');
      return;
    }
    const ajax =
      handle === 'handle' ? getSampleInventoryInfo : getSampleLabelInfo;
    const res = await ajax(inputValue);
    if (res.code === 200 && res.data) {
      EleMessage.success('样本添加入成功！');
      const content = res.data.content ? JSON.parse(res.data.content) : [];
      tableData.value.push({ ...res.data, ...content });
      importText.value = '';
    } else {
      EleMessage.error('样本ID未在库！');
    }
  };

  // 删除行
  const handleDeleteRow = (index) => {
    tableData.value.splice(index, 1);
  };

  // 验证表单
  const validate = async () => {
    try {
      if (tableData.value.length === 0) {
        EleMessage.error('请至少添加一条样本明细记录！');
        return false;
      }
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('送检明细验证失败:', error);
      return false;
    }
  };

  // 暴露方法给父组件
  defineExpose({
    validate
  });
</script>

<style scoped>
  .editable-table :deep(.editable-table-cell) {
    padding: 4px 8px;
  }

  .editable-table :deep(.el-form-item) {
    margin-bottom: 0;
  }
</style>
