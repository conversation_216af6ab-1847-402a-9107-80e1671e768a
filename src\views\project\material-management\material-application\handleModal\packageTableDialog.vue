<template>
  <div>
    <ele-modal
      form
      :width="1000"
      :title="titleObj[type]"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
      </EleTableSearch>
      <ele-pro-table
        ref="tableRef"
        :row-key="idType"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
        :pagination="false"
      >
        <template #sysFileIds="{ row }">
          <el-link
            v-if="row?.sysFileIds"
            type="primary"
            underline="never"
            @click="lookImg(row)"
            >照片</el-link
          >
        </template>
      </ele-pro-table>
      <template #footer>
        <div style="text-align: right">
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import ProForm from '@/components/ProForm/index.vue';
  import TpyeData from '../config';
  import {
    getConsumablesList,
    getBulkConsumablesList,
    getBulkReagentList
  } from '@/api/project/project-configuration/material-allocation';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const emit = defineEmits(['changeSelection']);
  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    //类型
    type: {
      type: String,
      default: 'packageType'
    },
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);
  /** 表单数据 */
  const form = reactive({
    consumablesName: '',
    consumableName: '',
    reagentName: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(form);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  /** 表单项 */
  const items = ref([]);
  //
  // 耗材包列表
  const columns = ref([]);
  const selections = ref([]);
  /** 保存编辑 */
  const save = () => {
    if (selections.value.length == 0) EleMessage.warning('请选择');
    emit('changeSelection', { data: selections.value, type: props.type });
    cancelDialog();
  };
  watch(
    () => props.type,
    (val) => {
      columns.value = TpyeData[val]['columns'];
      items.value = TpyeData[val]['items'];
    }
  );

  const tableRef = ref(null);
  const idType = ref('');
  const idObj = {
    packageType: 'consumablesId',
    bulkpackageType: 'consumableId',
    reagentType: 'reagentId'
  };
  watch(visible, (val) => {
    if (!val) return;
    idType.value = idObj[props.type];
    tableRef.value?.reload?.({});
  });
  const APIobj = {
    packageType: getConsumablesList,
    bulkpackageType: getBulkConsumablesList,
    reagentType: getBulkReagentList
  };
  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    const URL = APIobj[props.type];
    const projectId = userStore.projectId;
    const res = await URL({
      ...where,
      ...orders,
      ...filters,
      projectId
    });
    return {
      records: res.data ?? []
    };
  };

  const titleObj = {
    packageType: '请选择耗材包',
    bulkpackageType: '请选择散装耗材',
    reagentType: '请选择试剂'
  };

  const lookImg = (row) => {
    const base64Url = btoa(row.sysOssVoList[0].url);
    const url = `https://kkfile.520gcp.com/onlinePreview?url=${base64Url}`;
    window.open(url);
  };
</script>
