<template>
  <ele-pro-table
    row-key="userId"
    :columns="columns"
    :datasource="tableData"
    :span-method="objectSpanMethod"
    :pagination="false"
    border
  >
    <template #theoreticalNumber="{ row }">
      <el-input-number v-model="row.theoreticalNumber" style="width: 100%" />
    </template>
    <template #spareQuantity="{ row }">
      <el-input-number v-model="row.spareQuantity" style="width: 100%" />
    </template>
  </ele-pro-table>
</template>
<script setup>
  import { reactive, ref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  const props = defineProps({
    tableData: {
      type: Array,
      default: () => []
    }
  });
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材包编号',
      prop: 'consumablesNumber',
      align: 'center',
      width: 120
    },
    {
      label: '耗材包名称',
      prop: 'consumablesName',
      align: 'center'
    },
    {
      label: '耗材包备注',
      prop: 'remark',
      align: 'center'
    },
    {
      label: '申请数量',
      prop: 'quantity',
      align: 'center'
    },
    {
      label: '本次准备数量',
      prop: 'readyQuantity',
      align: 'center'
    },
    {
      label: '耗材包ID',
      prop: 'packageCode',
      align: 'center'
    },
    {
      label: '耗材项',
      prop: 'consumableName',
      align: 'center'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize',
      align: 'center'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial',
      align: 'center'
    },
    {
      label: '品牌',
      prop: 'consumableBrand',
      align: 'center'
    },
    {
      label: '耗材项数量',
      prop: 'quantity',
      align: 'center'
    },
    {
      label: '批号',
      prop: 'batchNumber',
      align: 'center'
    },
    {
      label: '有效期至',
      prop: 'endDate',
      align: 'center'
    }
  ]);
  const spanArr = ref([]);
  const getSpanArr = (data) => {
    for (let i = 0; i < data.length; i++) {
      if (
        i === 0 ||
        data[i].materialConsumablesId !== data[i - 1].materialConsumablesId
      ) {
        let count = 1;
        for (let j = i + 1; j < data.length; j++) {
          if (data[j].materialConsumablesId === data[i].materialConsumablesId)
            count++;
          else break;
        }
        spanArr.value.push(count);
      } else {
        spanArr.value.push(0);
      }
    }
  };
  const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < 7) {
      const rowspan = spanArr.value[rowIndex];
      return {
        rowspan: rowspan,
        colspan: rowspan > 0 ? 1 : 0
      };
    }
    return { rowspan: 1, colspan: 1 };
  };
  getSpanArr(props.tableData);
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    justify-content: space-between;
  }
</style>
