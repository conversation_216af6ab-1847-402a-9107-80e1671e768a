<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button
            v-permission="'organization:stat:export'"
            plain
            @click="exportFun"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #organizationName="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click="showInstirution(row)"
            >{{ row.organizationName }}</el-link
          >
        </template>
        <template #projectCount="{ row }">
          <el-link type="primary" underline="never" @click="showProject(row)">{{
            row.projectCount
          }}</el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <institutionsList
      v-if="instirutionVisible"
      v-model="instirutionVisible"
      :editData="editData"
    />
    <projectList
      v-if="projectVisible"
      v-model="projectVisible"
      :editData="editData"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getOrganizationStatList,
    exportOrganizationStat
  } from '@/api/statistics/institutions';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import institutionsList from '../../project/handleModal/institutionsList.vue';
  import projectList from '../../project/handleModal/projectList.vue';

  defineOptions({ name: 'Institutions' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    organizationName: '',
    organizationCode: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'organizationName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'organizationCode'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'organizationName',
        label: '机构名称',
        align: 'center',
        slot: 'organizationName'
      },
      {
        prop: 'organizationCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'projectCount',
        label: '关联项目数',
        align: 'center',
        slot: 'projectCount'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center'
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getOrganizationStatList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(where);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportOrganizationStat({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  const editData = ref(null);
  const instirutionVisible = ref(false);
  const showInstirution = (row) => {
    editData.value = row ?? null;
    instirutionVisible.value = true;
  };

  const projectVisible = ref(false);
  const showProject = (row) => {
    editData.value = row ?? null;
    projectVisible.value = true;
  };
</script>
