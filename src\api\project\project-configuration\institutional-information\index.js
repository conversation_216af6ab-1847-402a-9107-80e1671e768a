import request from '@/utils/request';
// 查询项目研究机构信息列表
export async function getHospitalList(params) {
  const res = await request.get('/project/hospital/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增项目研究机构信息
export async function hospitalAdd(data) {
  const res = await request.post('/project/hospital/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//修改
export async function hospitalEdit(data) {
  const res = await request.post('/project/hospital/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 获取项目研究机构信息详细信息
export async function getHospitalDetails(hospitalId) {
  const res = await request.get(`/project/hospital/${hospitalId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//删除项目研究机构信息
export async function hospitalRemove(hospitalId) {
  const res = await request.delete(`/project/hospital/remove/${hospitalId}`);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
