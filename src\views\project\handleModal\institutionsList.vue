<template>
  <div>
    <ele-modal
      form
      :width="500"
      :title="editData.organizationName"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :items="items"
        :footer="false"
        :label-width="100"
        :grid="{ span: 24 }"
        @updateValue="setFieldValue"
      >
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, onMounted } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { getOrganizationInfo } from '@/api/statistics/institutions';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    organizationName: '',
    organizationCode: '',
    researcher: '',
    phone: '',
    email: '',
    address: '',
    remark: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '机构名称',
      prop: 'organizationName',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '机构代号',
      prop: 'organizationCode',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '研究者',
      prop: 'researcher',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '电话',
      prop: 'phone',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '邮箱',
      prop: 'email',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '地址',
      prop: 'address',
      type: 'input',
      props: {
        disabled: true
      }
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'input',
      props: {
        disabled: true
      }
    }
  ]);

  const getDetails = async () => {
    const { data } = await getOrganizationInfo(props.editData.organizationId);
    assignFields({ ...data });
  };
  onMounted(() => {
    getDetails();
  });
</script>
