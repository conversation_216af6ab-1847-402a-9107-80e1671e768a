<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'materialPrepare:apply:print'"
            type="primary"
            class="ele-btn-icon"
            @click="printFun('batch')"
          >
            批量打印
          </el-button>
          <el-button
            v-permission="'materialPrepare:apply:export'"
            plain
            @click="exportFun"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="materialsdetailed-list"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <template #status="{ row }">
          <el-tag :type="statusType[row.status]">{{
            statusObj[row.status]
          }}</el-tag>
        </template>
        <template #action="{ row }">
          <el-link
            v-permission="'materialPrepare:apply:print'"
            type="primary"
            underline="never"
            @click="printFun('single', row)"
          >
            打印
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <handleModal
      v-model="visibleModal"
      :idList="prepareDetailsIdList"
      @surePrint="surePrint"
    />
    <printBarCode
      v-if="barCodeVisibleModal"
      v-model="barCodeVisibleModal"
      :data="printList"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getMaterialPrepareApplyList,
    exportMaterialPrepareApply
  } from '@/api/project/material-management/materialsdetailed-list';
  import handleModal from './handleModal/index.vue';
  import printBarCode from './handleModal/printBarCode.vue';

  defineOptions({ name: 'MaterialsdetailedList' });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    consumablesName: '',
    consumablesNumber: '',
    hospitalName: '',
    status: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'select',
      label: '状态',
      prop: 'status',
      options: [
        { label: '待发运', value: '0' },
        { label: '已发运', value: '1' },
        { label: '已废弃', value: '2' }
      ]
    },
    {
      type: 'input',
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      type: 'input',
      label: '耗材包编号',
      prop: 'consumablesNumber'
    },
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        selectable: (row) => row.status !== '2',
        reserveSelection: true,
        fixed: 'left'
      },
      {
        prop: 'status',
        label: '状态',
        slot: 'status',
        align: 'center'
      },
      {
        prop: 'consumablesNumber',
        label: '耗材包编号',
        align: 'center'
      },
      {
        prop: 'consumablesName',
        label: '耗材包名称',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '有效期至',
        align: 'center'
      },
      {
        prop: 'prepareDate',
        label: '准备日期',
        align: 'center'
      },
      {
        prop: 'sendDate',
        label: '寄出日期',
        align: 'center'
      },
      {
        prop: 'receiveDate',
        label: '接收日期',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '研究机构',
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 100,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  const statusObj = {
    0: '待发运',
    1: '已发运',
    2: '已废弃'
  };

  const statusType = {
    0: 'info',
    1: 'success',
    2: 'danger'
  };
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialPrepareApplyList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  //批量打印
  const visibleModal = ref(false);
  const prepareDetailsIdList = ref([]);
  const printFun = (type, row = null) => {
    if (type == 'single') {
      prepareDetailsIdList.value = [row.prepareDetailsId];
    } else {
      if (selections.value.length == 0)
        return EleMessage.warning('请选择要打印的耗材包！');
      prepareDetailsIdList.value = selections.value.map(
        (el) => el.prepareDetailsId
      );
    }
    visibleModal.value = true;
  };
  const printList = ref([]);
  const barCodeVisibleModal = ref(false);
  const surePrint = (list) => {
    printList.value = list ?? [];
    barCodeVisibleModal.value = true;
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where, orders }) => {
      exportMaterialPrepareApply({ ...where, ...orders })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
