<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1100"
    v-model="visible"
    title="样本在库详情"
    @open="handleOpen"
    append-to-body
  >
    <div>
      <el-table
        :data="dataList"
        style="width: 100%"
        :cell-style="{ padding: '10px 0' }"
        :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column prop="testingIndex" label="检测指标" />
        <el-table-column prop="sampleType" label="样本类型" />
        <el-table-column prop="packagingNumber" label="分装号" />
        <el-table-column prop="num" label="样本数量" />
      </el-table>
    </div>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import { getSampleStock } from '@/api/statistics/coldchain';
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const emit = defineEmits(['done']);
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const dataList = ref([]);

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    console.log('props.data====>', props.data.projectId);
    initData();
  };
  const initData = () => {
    getSampleStock(props.data.projectId).then((res) => {
      dataList.value = res.data;
    });
  };
  onMounted(() => {
    initData();
  });
</script>
<style lang="scss" scoped>
  .title {
    font-weight: 900;
    font-size: 16px;
  }
</style>
