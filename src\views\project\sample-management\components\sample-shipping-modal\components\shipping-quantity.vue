<template>
  <ele-card
    :body-style="{ paddingBottom: '4px' }"
    :header="`样本${formHandle === 'receive' ? '接收' : '发运'}清单`"
    style="margin-bottom: 16px"
  >
    <template #extra>
      <el-button v-if="mode !== 'view'" type="primary" @click="handleAddRow">
        添加样本类型
      </el-button>
    </template>

    <el-form ref="formRef" :model="formModel" @submit.prevent="">
      <ele-data-table
        row-key="id"
        :columns="columns"
        :data="tableData"
        cell-class-name="editable-table-cell"
        class="editable-table"
      >
        <!-- 样本类型 -->
        <template #sampleType="{ row, $index }">
          <el-form-item
            v-if="mode !== 'view'"
            :prop="'tableData.' + $index + '.sampleType'"
            :rules="{
              required: true,
              message: '请选择样本类型',
              trigger: 'change'
            }"
            style="margin-bottom: 0"
          >
            <dict-data
              v-model="row.sampleType"
              placeholder="请选择样本类型"
              style="width: 100%"
              code="sample_type"
            />
          </el-form-item>
          <span v-else>{{
            sampleTypeDicts.find((d) => d.dictValue === row.sampleType)
              ?.dictLabel
          }}</span>
        </template>

        <!-- 样本数量 -->
        <template #sampleNum="{ row, $index }">
          <el-form-item
            v-if="mode !== 'view'"
            :prop="'tableData.' + $index + '.sampleNum'"
            :rules="[
              {
                required: true,
                message: '请输入样本数量',
                trigger: 'blur'
              },
              {
                type: 'number',
                min: 1,
                message: '样本数量必须是大于0的正整数',
                trigger: 'blur'
              }
            ]"
            style="margin-bottom: 0"
          >
            <el-input-number
              v-model="row.sampleNum"
              :min="1"
              :max="999999"
              :precision="0"
              :step="1"
              placeholder="请输入正整数"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
          <span v-else>{{ row.sampleNum }}</span>
        </template>

        <!-- 备注 -->
        <template #remark="{ row, $index }">
          <el-form-item
            v-if="mode !== 'view'"
            :prop="'tableData.' + $index + '.remark'"
            style="margin-bottom: 0"
          >
            <el-input
              v-model="row.remark"
              placeholder="请输入备注"
              style="width: 100%"
              clearable
              show-word-limit
              maxlength="100"
            />
          </el-form-item>
          <span v-else>{{ row.remark || '-' }}</span>
        </template>

        <!-- 操作 -->
        <template #action="{ $index }">
          <el-link
            v-if="mode !== 'view'"
            type="primary"
            underline="never"
            @click="handleDeleteRow($index)"
          >
            删除
          </el-link>
        </template>
      </ele-data-table>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, computed, reactive } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useDictData } from '@/utils/use-dict-data';

  // 使用 defineModel 简化双向绑定
  const tableData = defineModel({
    type: Array,
    default: () => []
  });

  const props = defineProps({
    mode: {
      type: String,
      default: 'add' // add/edit/view
    },
    formHandle: {
      type: String,
      default: 'despatch'
    }
  });

  const [sampleTypeDicts] = useDictData(['sample_type']);

  const formRef = ref(null);

  // 表单模型（避免响应式循环）
  const formModel = reactive({
    tableData: tableData
  });

  // 表格列配置
  const columns = computed(() => {
    const baseColumns = [
      {
        type: 'index',
        label: '序号',
        width: 80,
        align: 'center'
      },
      {
        label: '样本类型',
        prop: 'sampleType',
        minWidth: 200,
        slot: 'sampleType'
      },
      {
        label: '样本数量',
        prop: 'sampleNum',
        minWidth: 150,
        slot: 'sampleNum'
      },
      {
        label: '备注',
        prop: 'remark',
        minWidth: 200,
        slot: 'remark'
      }
    ];

    // 只有非查看模式才显示操作列
    if (props.mode !== 'view') {
      baseColumns.push({
        label: '操作',
        columnKey: 'action',
        width: 80,
        align: 'center',
        slot: 'action'
      });
    }

    return baseColumns;
  });

  // defineModel 会自动处理双向绑定，不需要手动监听

  // 添加行
  const handleAddRow = () => {
    tableData.value.push({
      id: Date.now(),
      sampleType: '',
      sampleNum: 1,
      remark: ''
    });
  };

  // 删除行
  const handleDeleteRow = (index) => {
    tableData.value.splice(index, 1);
  };

  // 验证表单
  const validate = async () => {
    try {
      if (tableData.value.length === 0) {
        EleMessage.error(
          `${props.formHandle === 'despatch' ? '发运' : '接收'}数量不能为空，请添加！`
        );
        return false;
      }
      const res = await formRef.value.validate();
      return res;
    } catch (error) {
      console.error('送检数量验证失败:', error);
      return false;
    }
  };

  // 暴露方法给父组件
  defineExpose({
    validate
  });
</script>

<style scoped>
  .editable-table :deep(.editable-table-cell) {
    padding: 4px 8px;
  }

  .editable-table :deep(.el-form-item) {
    margin-bottom: 0;
  }
</style>
