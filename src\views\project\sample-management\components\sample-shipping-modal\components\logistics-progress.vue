<template>
  <ele-card>
    <el-timeline style="max-width: 600px">
      <el-timeline-item
        v-for="(activity, index) in logisticsInfoList"
        :key="index"
        :type="index === '0' ? 'primary' : ''"
        :timestamp="activity.time"
      >
        {{ activity.msg }}
      </el-timeline-item>
    </el-timeline>
  </ele-card>
</template>

<script setup>
  import { toRefs, reactive } from 'vue';
  const props = defineProps({
    /** 操作类型 add/edit/view */
    logisticsInfo: {
      type: String
    }
  });
  const { logisticsInfo } = toRefs(props);
  let logisticsInfoList = reactive([]);
  if (logisticsInfo.value) {
    logisticsInfoList = Object.assign({}, JSON.parse(logisticsInfo.value));
  }
</script>
<style scoped lang="less"></style>
