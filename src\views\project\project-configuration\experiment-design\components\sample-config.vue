<template>
  <div>
    <!-- 组别组件 -->
    <SampleGroup @changeSampleGroup="changeSampleGroup" />
    <ele-split-panel
      flex-table
      :size="designType === '3' ? '700px' : '30vw'"
      allow-collapse
      v-model:collapse="collapse"
      v-show="groupId"
      :allowCollapse="designType !== '3'"
      :custom-style="{
        borderWidth: '0 1px 0 0',
        padding: '16px 0',
        background: 'none'
      }"
      :body-style="{
        padding: '16px 16px 0 0',
        overflow: 'auto',
        minWidth: '556px'
      }"
      :style="{
        height: 'calc(100vh - 250px)',
        overflow: 'visible',
        border: '1px solid #dcdfe6'
      }"
    >
      <SamplePoint
        ref="samplePointRef"
        :groupId="groupId"
        :isView="isView"
        @changeSamplePoint="changeSamplePoint"
      />
      <template #body>
        <SampleTube
          v-show="designType !== '3'"
          @saveTube="handleSampleTubeSave"
          :pointId="pointId"
          :isView="isView"
        />
      </template>
    </ele-split-panel>
  </div>
</template>
<script setup>
  import { ref, inject, computed } from 'vue';
  import SampleGroup from './sample-group.vue';
  import SamplePoint from './sample-point.vue';
  import SampleTube from './sample-tube.vue';

  const projectInfo = inject('projectInfo');

  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');

  const collapse = ref(false);
  // 组别
  const groupId = ref('');
  // 组别状态; 组别一旦确定（样本接收） 即status 为 1时 ，就只能新增受试者数量了，下面的采样点，样本管，包括自身的编辑，删除
  const isView = ref('');
  // 样本点
  const pointId = ref('');

  // 修改组别
  const changeSampleGroup = (group) => {
    groupId.value = group.groupId;
    isView.value = group.status === '1';
  };

  // 修改样本点
  const changeSamplePoint = (data) => {
    pointId.value = data;
  };

  // 处理样本管配置保存
  const samplePointRef = ref(null);
  const handleSampleTubeSave = () => {
    // 更新对应采样点状态
    samplePointRef.value.loadSamplePoints();
  };
</script>
