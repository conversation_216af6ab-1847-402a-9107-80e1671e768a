<template>
  <div>
    <div v-if="tableList.length !== 0">
      <ele-card
        :flex-table="fixedHeight"
        :body-style="{ paddingBottom: '20px' }"
        :style="{
          minHeight: fixedHeight ? '100px' : void 0,
          marginBottom: fixedHeight ? '10px' : void 0
        }"
        bordered
        v-for="(data, index) in tableList"
        :key="index"
      >
        <div class="flex">
          <div>
            <span>状态：<span style="color:#2F54EB;">{{ statusObj[data.status] }}</span></span>
            <span style="margin: 0 20px">准备时间：<span style="color:#2F54EB;">{{ data.createTime }}</span></span>
            <span>准备人：<span style="color:#2F54EB;">{{ data.createName }}</span></span>
            <span style="margin: 0 20px"
              >运输单位：<span style="color:#2F54EB;">{{ data.logisticsCompany || '/' }}</span></span
            >
            <span>运输单号：<span style="color:#2F54EB;">{{ data.bookingNote || '/' }}</span></span>
          </div>
          <el-button
            v-permission="'materialPrepare:prepare:discard'"
            type="danger"
            plain
            v-if="data.status == '0'"
            @click="discard(data)"
            >废弃</el-button
          >
        </div>
        <raedyTable :tableData="data.datasource" />
      </ele-card>
    </div>
    <el-empty v-else description="暂无数据" style="height: 85vh" />
    <handleModalDiscard
          v-model="visibleModalDiscard"
          :id="prepareId"
          @success="getSelectPrepareApplyList"
        />
  </div>
</template>
<script setup>
  import { reactive, ref, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import raedyTable from './raedyTable.vue';
  import handleModalDiscard from '../material-applicationOther/handleModal/discard.vue';
  import { selectPrepareApplyList } from '@/api/project/material-management/material-applicationOther';

  const props = defineProps({
    id: {
      type: String,
      default: ''
    }
  });

  const statusObj = {
    0: '待发运',
    1: '已发运',
    2: '已废弃'
  };
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const tableList = ref([]);

  const getSelectPrepareApplyList = async () => {
    const { data } = await selectPrepareApplyList({ applyId: props.id });
    tableList.value = data ?? [];
    if (!data || data.length == 0) return;
    data.forEach((dataItem) => {
      let list = [];
      if (
        !dataItem?.detailsInfoVoList ||
        dataItem.detailsInfoVoList.length == 0
      )
        return;
      dataItem.detailsInfoVoList.forEach((item) => {
        if (!item?.inventoryInfoVoList || item.inventoryInfoVoList.length == 0)
          return;
        item.inventoryInfoVoList.forEach((el) => {
          list.push({
            prepareDetailsId: item.prepareDetailsId,
            prepareId: item.prepareId,
            projectId: item.projectId,
            applyId: item.applyId,
            materialConsumablesId: item.materialConsumablesId,
            consumablesName: item.consumablesName,
            consumablesNumber: item.consumablesNumber,
            remark: item.remark,
            ApplyQuantity: item.ApplyQuantity,
            packageCode: item.packageCode,
            readyQuantity: item.quantity,
            ...el
          });
        });
      });
      dataItem.datasource = list ?? [];
    });
  };
  getSelectPrepareApplyList();

  //废弃
  const prepareId = ref('');
  const visibleModalDiscard = ref(false);
  const discard = async (row) => {
    prepareId.value = row.prepareId;
    visibleModalDiscard.value = true;
  };
  watch(
    () => props.id,
    () => {
      getSelectPrepareApplyList();
    }
  );
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    justify-content: space-between;
  }
</style>
