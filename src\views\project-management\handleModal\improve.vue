<template>
  <div>
    <ele-modal
      form
      :width="550"
      title="补充样本信息"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { projectImprove } from '@/api/project-management';
  import ProForm from '@/components/ProForm/index.vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    id: {
      type: String,
      default: ''
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    registrationDeclaration: '',
    sampleChargeDate: '',
    handleDate: '',
    sampleStatus: '',
    payer: '',
    confirmDate: '',
    settlementStatus: '',
    projectId: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '是否注册申报',
      prop: 'registrationDeclaration',
      type: 'select',
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '2' }
      ]
    },
    {
      label: '样本计费日期',
      prop: 'sampleChargeDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' }
    },
    {
      label: '实际处理日期',
      prop: 'handleDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' }
    },
    {
      label: '样本状态',
      prop: 'sampleStatus',
      type: 'select',
      options: [
        { label: '保存中', value: '1' },
        { label: '已销毁', value: '2' },
        { label: '客户取回', value: '3' }
      ]
    },
    {
      label: '付款方',
      prop: 'payer',
      type: 'input'
    },
    {
      label: '客户确认日期',
      prop: 'confirmDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' }
    },
    {
      label: '费用结算状态',
      prop: 'settlementStatus',
      type: 'select',
      options: [
        { label: '待结算', value: '1' },
        { label: '已结算', value: '2' },
        { label: '无费用', value: '3' },
        { label: '已完结', value: '4' }
      ]
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      projectImprove(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  watch(visible, (val) => {
    if (val) {
      assignFields({ projectId: props.id });
    }
  });
</script>
