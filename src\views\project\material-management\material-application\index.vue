<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'material:apply:add'"
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button>
          <el-button
            v-permission="'material:apply:export'"
            @click="exportFun"
            plain
            >导出</el-button
          >
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="applyId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="material-application"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #auditStatus="{ row }">
          <el-tag :type="statusColor[row.auditStatus]">{{
            statusObj[row.auditStatus]
          }}</el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'material:apply:query'"
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
          <el-divider
            v-permission="['material:apply:query', 'material:apply:revoke']"
            v-if="row.auditStatus == '0'"
            direction="vertical"
          />
          <el-link
            v-permission="'material:apply:revoke'"
            v-if="row.auditStatus == '0'"
            type="danger"
            underline="never"
            @click.stop="revoke(row)"
          >
            撤销
          </el-link>
        </template>
      </ele-pro-table>

      <handleModal
        v-model="visibleModal"
        :handle="handle"
        :editData="editData"
        @success="reload"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, watch, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getMaterialApplyList,
    materialApplyRevoke,
    exportMaterialApply
  } from '@/api/project/material-management/material-application';
  import handleModal from './handleModal/index.vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';

  defineOptions({ name: 'MaterialApplication' });

  const userStore = useUserStore();

  const { currentRoute, push } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    hospitalName: '',
    applyNumber: '',
    auditStatus: '',
    hospitalCode: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      type: 'input',
      label: '申请编号',
      prop: 'applyNumber'
    },
    {
      type: 'select',
      label: '申请状态',
      prop: 'auditStatus',
      options: [
        { label: '待审核', value: '0' },
        { label: '已通过', value: '1' },
        { label: '已撤销', value: '2' }
      ]
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'auditStatus',
        label: '申请状态',
        slot: 'auditStatus',
        width: 100,
        align: 'center'
      },
      {
        prop: 'applyNumber',
        label: '申请编号',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '研究机构',
        align: 'center'
      },
      {
        prop: 'createName',
        label: '申请人',
        align: 'center'
      },
      {
        prop: 'createTime',
        label: '申请日期',
        align: 'center'
      },
      {
        prop: 'recipientName',
        label: '收件人姓名',
        align: 'center'
      },
      {
        prop: 'deliverDate',
        label: '期望送达日期',
        align: 'center'
      },
      {
        prop: 'remark',
        label: '申请备注',
        align: 'center'
      },
      {
        prop: 'auditName',
        label: '审核人',
        align: 'center'
      },
      {
        prop: 'auditTime',
        label: '审核时间',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 150,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  const statusObj = {
    0: '待审核',
    1: '已通过',
    2: '已撤销'
  };
  const statusColor = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  };
  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialApplyList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  const openDetail = (row) => {
    push({
      path: '/project/material-management/material-application/details',
      query: {
        id: row?.applyId,
        actionType: 'revoke',
        isEdit: 'details'
      }
    });
  };

  /** 编辑 */
  const openEdit = (row) => {
    console.log(userStore.projectInfo, 'userStore.projectInfo');
    //if (userStore.projectInfo?.designType != '3')
    //return EleMessage.warning('当前设计类型不可新增编辑物资申请');
    //if (userStore.projectInfo?.designStatus != '1')
    //return EleMessage.warning('当前设计类型未确认');
    handle.value = row ? 'edit' : 'add';
    editData.value = row || null;
    visibleModal.value = true;
  };

  /** 撤销 */
  const revoke = (row) => {
    ElMessageBox.confirm(`确定要撤销该申请数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        materialApplyRevoke({ applyId: row.applyId })
          .then(() => {
            loading.close();
            EleMessage.success('操作成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportMaterialApply({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      const isReload = query?.isReload || '';
      if (!isReload) return;
      reload();
    },
    { immediate: true }
  );
</script>
