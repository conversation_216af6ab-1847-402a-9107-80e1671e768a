import request from '@/utils/request';
// 查询项目耗材包关系列表
export async function getConsumablesList(params) {
  const res = await request.get('/project/consumables/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//耗材包新增
export async function consumablesAdd(data) {
  const res = await request.post('/project/consumables/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//耗材包修改
export async function consumablesEdit(data) {
  const res = await request.post('/project/consumables/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 耗材包删除
export async function consumablesRemove(consumablesId) {
  const res = await request.delete(
    `/project/consumables/remove/${consumablesId}`
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//查询项目散装耗材关系列表
export async function getBulkConsumablesList(params) {
  const res = await request.get('/project/bulkConsumables/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增项目散装耗材关系
export async function bulkConsumablesAdd(data) {
  const res = await request.post('/project/bulkConsumables/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 删除项目散装耗材关系
export async function bulkConsumablesRemove(id) {
  const res = await request.delete(`/project/bulkConsumables/remove/${id}`);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 查询项目散装试剂关系列表
export async function getBulkReagentList(params) {
  const res = await request.get('/project/bulkReagent/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增项目散装试剂关系
export async function bulkReagentAdd(data) {
  const res = await request.post('/project/bulkReagent/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 编辑项目散装试剂关系
export async function bulkReagentEdit(data) {
  const res = await request.post('/project/bulkReagent/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 删除项目散装试剂关系
export async function bulkReagentRemove(reagentId) {
  const res = await request.delete(`/project/bulkReagent/remove/${reagentId}`);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 耗材信息下拉框-通过耗材类型查询(不分页)
export async function selectInventoryInfoList(params) {
  const res = await request.get('/inventory/selectInventoryInfoList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 项目下-组别/访视-时间-
export async function selectProjectConsumablesSamplePointVoList(params) {
  const res = await request.get(
    '/project/consumables/selectProjectConsumablesSamplePointVoList',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 耗材信息下拉框（分页）
export async function selectPageInventoryInfoList(params) {
  const res = await request.get('/inventory/selectPageInventoryInfoList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
