import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//查询物资发运列表
export async function getMaterialForwardingList(params) {
  const res = await request.get('/materialForwarding/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 获取物资发运详细信息
export async function getMaterialForwardingDetails(forwardingId) {
  const res = await request.get(`/materialForwarding/${forwardingId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增物资发运
export async function getMaterialForwardingAdd(data) {
  const res = await request.post(`/materialForwarding/add`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 撤销
export async function materialForwardingRevoke(params) {
  const res = await request.get(`/materialForwarding/revoke`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

// 新建发运物资-接收机构
export async function selectHospitalList(params) {
  const res = await request.get(`/materialForwarding/selectHospitalList`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新建发运物资-机构的申请编号
export async function selectHospitalApplyList(params) {
  const res = await request.get(`/materialForwarding/selectHospitalApplyList`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新建发运物资-机构的申请编号 物资包发运、散装耗材发运、试剂发运
export async function selectHospitalApplyDetailsList(params) {
  const res = await request.get(
    `/materialForwarding/selectHospitalApplyDetailsList`,
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出物资发运列表
export async function exportMaterialForwarding(params) {
  const res = await request({
    url: '/materialForwarding/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资发运_${Date.now()}.xlsx`);
}
