<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table v-if="!designType">
      <el-empty description="当前还未进行研究试验设计，赶快去设计吧">
        <el-button
          type="primary"
          @click="handleAdd"
          :icon="PlusOutlined"
          v-permission="'sample:designType:add'"
          >新增试验设计类型</el-button
        >
      </el-empty>
    </ele-card>
    <ele-card style="position: relative" flex-table v-else>
      <div class="page-buttons">
        <div v-if="active === 'pointTable' && designStatus === '0'">
          <el-button
            type="warning"
            @click="handleEdit"
            :icon="EditOutlined"
            v-permission="'sample:designType:edit'"
            >编辑试验设计类型</el-button
          >
          <el-button
            type="success"
            @click="handleSave"
            :icon="CheckOutlined"
            v-permission="'sample:designType:confirm'"
            >确认试验设计类型</el-button
          >
        </div>
        <div
          v-if="active === 'sampleDetail' && projectInfo.genLabelStatus !== '2'"
          v-permission="'sample:sampleTubeLabel:generate'"
          style="display: flex; gap: 15px; align-items: center"
        >
          <ele-text
            type="warning"
            :icon="InfoCircleFilled"
            v-if="projectInfo.genLabelStatus === '1'"
            >试验设计存在更新，请确认生成新的样本管ID明细</ele-text
          >
          <el-button
            type="success"
            @click="handleGenSampleLabel"
            :icon="CheckOutlined"
            >生成样本ID明细</el-button
          >
        </div>
      </div>
      <ele-tabs
        v-model="active"
        :items="[
          { name: 'pointTable', label: '试验组别&采样点' },
          { name: 'sampleDetail', label: '样本管ID明细' }
        ]"
        @tab-change="handleTabChange"
        flex-table
      >
        <!-- 通过标签页数据的 name 作为插槽名渲染每个标签的内容, 插槽还会传递 item 参数 -->
        <template #pointTable>
          <div style="padding: 20px 0">
            <sampleConfig />
          </div>
        </template>
        <template #sampleDetail>
          <sampleDetail ref="sampleDetailRef" />
        </template>
      </ele-tabs>
    </ele-card>
    <!-- 设置试验类型 -->
    <sampleTypeDialog
      v-model="setTypeVisibleModal"
      :handle="handle"
      @changeType="changeType"
    />
  </ele-page>
</template>
<script setup>
  import { ref, provide, onMounted } from 'vue';
  import sampleConfig from './components/sample-config.vue';
  import sampleDetail from './components/sample-detail.vue';
  import sampleTypeDialog from './handleModal/sample-type-dialog.vue';
  import { useUserStore } from '@/store/modules/user';
  import {
    PlusOutlined,
    EditOutlined,
    CheckOutlined,
    InfoCircleFilled
  } from '@/components/icons';
  import {
    genSampleLabel,
    getSampleDesignType
  } from '@/api/project/project-configuration/experiment-design';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  defineOptions({ name: 'ExperimentDesign' });

  /**
   * 获取项目详情
   * 项目ID    projectId
   * 设计类型  designType
   * 1=研究周期+采样时间
   * 2=周期+年/月/周/日+采样时间
   * 3=访视+采样点
   */
  const userStore = useUserStore();
  const projectInfo = userStore.projectInfo;
  const { projectId, designStatus, designType } = projectInfo;

  // 立即provide响应式数据，子组件可以监听变化
  provide('projectInfo', projectInfo);

  /** 激活标签 */
  const active = ref('pointTable');

  const handle = ref('add');

  const setTypeVisibleModal = ref(false);

  const handleAdd = () => {
    handle.value = 'add';
    setTypeVisibleModal.value = true;
  };

  const handleEdit = () => {
    handle.value = 'edit';
    setTypeVisibleModal.value = true;
  };

  // 确认类型
  const handleSave = () => {
    handle.value = 'confirm';
    setTypeVisibleModal.value = true;
  };

  // 生成样本管ID明细
  const sampleDetailRef = ref(null);
  const handleGenSampleLabel = async () => {
    const res = await genSampleLabel(projectId);
    if (res.code === 200) {
      EleMessage.success('生成成功');
      sampleDetailRef.value.reload();
      projectInfo.genLabelStatus = '2';
    } else {
      EleMessage.error(res.msg);
    }
  };

  // 切换tabs时，如果切换到明细，需要获取状态，才能控制 生成标签的状态
  const handleTabChange = (value) => {
    if (value === 'sampleDetail') {
      init();
    }
  };

  const init = async () => {
    try {
      const res = await getSampleDesignType(projectId);
      if (res.code === 200) {
        projectInfo.designType = res.data.designType;
        projectInfo.designStatus = res.data.status;
        projectInfo.genLabelStatus = res.data.genLabelStatus;
      } else {
        EleMessage.error(res.message || '获取设计类型失败');
      }
    } catch (error) {
      console.error('获取设计类型失败:', error);
      EleMessage.error('获取设计类型失败');
    }
  };

  const changeType = async () => {
    await init();
    window.location.reload();
  };

  // 组件挂载时初始化数据
  onMounted(() => {
    if (projectInfo.designType) {
      init();
    }
  });
</script>

<style lang="scss" scoped>
  .page-buttons {
    position: absolute;
    z-index: 10;
    top: 20px;
    right: 35px;
  }
</style>
