<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'materialForwarding:forwarding:receiveExport'"
            @click="exportFun"
            plain
            >导出</el-button
          >
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :loadOnCreated="false"
        :show-overflow-tooltip="true"
        cache-key="material-receiving"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <template #status="{ row }">
          <el-tag :type="statusColor[row.status]">
            {{ statusObj[row.status] }}
          </el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'materialForwarding:forwarding:query'"
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
          <el-divider
            v-permission="'materialForwarding:forwarding:query'"
            direction="vertical"
          />
          <el-link
            type="primary"
            underline="never"
            v-permission="'project:problem:add'"
            @click.stop="questionsClick(row)"
          >
            提问
          </el-link>
          <el-divider
            v-permission="'materialForwarding:forwarding:receive'"
            v-if="row.status == '0'"
            direction="vertical"
          />
          <el-link
            v-permission="'materialForwarding:forwarding:receive'"
            v-if="row.status == '0'"
            type="danger"
            underline="never"
            @click.stop="receiving(row)"
          >
            接收
          </el-link>
        </template>
      </ele-pro-table>

      <handleModalQuestions v-model="questionsVisibleModal" :data="editData" />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, unref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getMaterialForwardingReceiveList,
    exportReceiveList
  } from '@/api/project/material-management/material-receiving';
  import handleModalQuestions from '../material-shipment/handleModal/questions.vue';
  import { useRouter } from 'vue-router';
  const { push, currentRoute } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    status: '',
    hospitalName: '',
    hospitalCode: '',
    forwardingNumber: '',
    bookingNote: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  const statusObj = {
    0: '待接收',
    1: '已接收',
    2: '已撤销'
  };

  const statusColor = {
    0: 'info',
    1: 'success',
    2: 'danger'
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'select',
      label: '状态',
      prop: 'status',
      options: Object.keys(statusObj).map((key) => {
        return {
          label: statusObj[key],
          value: key
        };
      })
    },
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      type: 'input',
      label: '发运编号',
      prop: 'forwardingNumber'
    },
    {
      type: 'input',
      label: '运输单号',
      prop: 'bookingNote'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'status',
        label: '发运状态',
        slot: 'status',
        align: 'center'
      },
      {
        prop: 'forwardingNumber',
        label: '发运编号',
        align: 'center'
      },
      {
        prop: 'logisticsCompany',
        label: '运输单位',
        align: 'center'
      },
      {
        prop: 'bookingNote',
        label: '运输单号',
        align: 'center'
      },
      {
        prop: 'applyNumbers',
        label: '申请编号',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '研究机构',
        align: 'center'
      },
      {
        prop: 'recipientName',
        label: '收件人姓名',
        align: 'center'
      },
      {
        prop: 'phone',
        label: '电话',
        align: 'center'
      },
      {
        prop: 'address',
        label: '地址',
        align: 'center'
      },
      {
        prop: 'shipperName',
        label: '发运人',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'forwardingDate',
        label: '发运日期',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'receiveName',
        label: '接收人',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'receiveTime',
        label: '接收时间',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  const editData = ref({});

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialForwardingReceiveList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  const openDetail = (row) => {
    push({
      path: '/project/material-management/material-shipment/details',
      query: {
        id: row?.forwardingId
      }
    });
  };
  // 提问
  const questionsVisibleModal = ref(false);
  const questionsClick = (row) => {
    editData.value = row || null;
    questionsVisibleModal.value = true;
  };
  const receiving = (row) => {
    push({
      path: '/project/material-management/material-shipment/details',
      query: {
        id: row?.forwardingId,
        type: 'receive'
      }
    });
  };
  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportReceiveList({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  onMounted(() => {
    const { query } = unref(currentRoute);
    if (query?.status) {
      form.status = query.status;
    }
    reload(form);
  });
</script>
