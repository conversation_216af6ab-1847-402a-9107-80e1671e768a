<template>
  <div style="display: flex; align-items: center">
    <el-button
      class="custom-blue"
      @click="projectOnclick"
      style="
        font-size: 18px;
        background-color: #fff7e8;
        color: #ffb457;
        border-color: #ffb457;
      "
      >选择项目</el-button
    >
    <div class="projectChangeprojectName" v-if="info.projectName">
      <el-tooltip
        effect="dark"
        :content="`（${orgDisabled ? info.code : info.fullname}） ${
          info.projectName
        }`"
        placement="top-start"
        :show-after="100"
      >
        <span @click="dialogVisible = true">
          {{ `（${info.schemeNumber}）${info.projectName}` }}
        </span>
      </el-tooltip>
    </div>
    <span @click="dialogVisible = true" class="projectChangeprojectName" v-else>
      请先选择试验项目
    </span>
  </div>
  <ele-modal
    title="选择项目"
    :width="1000"
    :body-style="{ paddingTop: '6px' }"
    v-model="dialogVisible"
  >
    <el-tabs
      v-model="currentStatus"
      class="demo-tabs"
      @tab-click="currentStatusChange"
    >
      <el-tab-pane label="进行中" name="incomplete" />
      <el-tab-pane label="已完成" name="completed" />
    </el-tabs>
    <div class="tableForm">
      <el-form inline :model="params">
        <el-form-item label="关键字：">
          <el-input
            v-model="params.keywords"
            placeholder="请输入关键字"
            clearable
            style="width: 200px"
            maxlength="99"
          />
        </el-form-item>
        <el-form-item>
          <el-button class="custom-blue" @click="reload">搜索</el-button>
          <el-button
            class="custom-info"
            @click="(params.keywords = ''), reload()"
            >重置</el-button
          >
          <el-button
            v-if="loginUser.role == 2"
            class="custom-primary"
            @click="handleAdd"
            >新增项目</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <ele-pro-table
      ref="tableRef"
      :columns="columns"
      :datasource="datasource"
      :toolbar="false"
      :loadOnCreated="orgDisabled"
      highlight-current-row
      :bottom-line="tableFullHeight"
      emptyText="暂无数据"
      max-height="400"
    >
      <template #status="{ row }">
        <span v-if="row.status == 1" style="color: #67c32a">进行中</span>
        <span v-if="row.status == 2" style="color: #999999">已完成</span>
        <!-- <span v-if="row.status == 3" style="color: #a69ea8">中止</span> -->
        <!-- <span v-if="row.status == 4" style="color: #656ad2">终止</span> -->
      </template>
      <template #action="{ row }">
        <el-space>
          <el-link type="primary" @click="change(row)">选择</el-link>
        </el-space>
      </template>
    </ele-pro-table>
  </ele-modal>
  <!-- <addProject
    v-model="showCreate"
    :data="current"
    @done="reload"
    :isCopyAdd="isCopyAdd"
  /> -->
</template>

<script setup>
  import { ref, nextTick, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { selectProject } from '@/api/monitor/operlog';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});
  const emit = defineEmits(['done']);
  const dialogVisible = ref(false);

  const info = ref({ projectName: '' });
  const params = ref({ status: '1' });
  const props = defineProps({
    orgDisabled: Boolean,
    projectInfo: Object
  });

  const loading = ref(false);
  const options = ref([]);
  const isCopyAdd = ref(false);
  // 新增修改弹框控制
  const showCreate = ref(false);
  // 当前选中数据
  const current = ref({});
  const handleAdd = () => {
    isCopyAdd.value = false;
    showCreate.value = true;
    current.value = null;
  };
  const change = (row) => {
    console.log('change====>', row);
    info.value.projectName = row.projectName;
    info.value.analyseProjectNumber = row.analyseProjectNumber;
    info.value.projectId = row.projectId;
    info.value.schemeNumber = row.schemeNumber;
    info.value.projectStatus = row.projectStatus;
    info.value = { ...info.value, ...row };
    emit('done', info.value);
    EleMessage.success('切换成功');
    dialogVisible.value = false;
  };
  const tableRef = ref(null);
  const projectOnclick = () => {
    dialogVisible.value = true;
    console.log('选择项目=====>');
    nextTick(() => {
      reload();
    });
  };

  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload?.();
  };
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'schemeNumber',
      label: '试验方案编号',
      width: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'analyseProjectNumber',
      label: '分析项目编号',
      width: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'projectName',
      label: '项目名称',
      showOverflowTooltip: true
    },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 80,
      slot: 'action',
      hideInPrint: true,
      hideInExport: true,
      fixed: 'right'
    }
  ]);
  const currentStatus = ref('incomplete');
  const currentStatusChange = (type) => {
    currentStatus.value = type.props.name;
    if (currentStatus.value == 'incomplete') {
      params.value.status = 1;
    }
    if (currentStatus.value == 'completed') {
      params.value.status = 2;
    }
    reload();
  };
  const datasource = async ({ page, limit, where, orders, filters }) => {
    return selectProject({
      ...filters,
      ...where,
      orderBy: orders.sort,
      isAsc: 'asc',
      ...limit,
      ...page
    });
  };
  nextTick(() => {
    reload();
  });
</script>
<style>
  .projectChangeprojectName {
    margin-left: 10px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    cursor: pointer;
    font-size: 22px;
    font-weight: bold;
    color: #3b426f;
  }
</style>
<style lang="scss" scoped>
  .tableForm {
    display: flex;
    justify-content: space-between;
  }
</style>
