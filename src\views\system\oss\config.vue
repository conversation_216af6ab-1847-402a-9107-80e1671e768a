<template>
  <ele-page :flex-table="true" hide-footer>
    <ele-card :flex-table="true">
      <ele-table-search
        :model="queryParams"
        :items="searchItems"
        @submit="onSearch"
        @updateValue="updateFormValue"
        @reset="resetQuery"
      >
        <template #toolbar>
          <el-button
            v-permission="'system:ossConfig:add'"
            type="primary"
            :icon="PlusOutlined"
            @click="handleAdd"
          >
            新增
          </el-button>
        </template>
      </ele-table-search>

      <ele-pro-table
        ref="tableRef"
        row-key="ossConfigId"
        :columns="columns"
        :datasource="datasource"
        :sticky="true"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="oss-config-table"
      >
        <!-- 桶权限类型列 -->
        <template #accessPolicy="{ row }">
          <el-tag v-if="row.accessPolicy === '0'" type="warning"
            >private</el-tag
          >
          <el-tag v-if="row.accessPolicy === '1'" type="success">public</el-tag>
          <el-tag v-if="row.accessPolicy === '2'" type="info">custom</el-tag>
        </template>
        <!-- 是否默认列 -->
        <template #status="{ row }">
          <el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
          />
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'system:ossConfig:edit'"
            type="primary"
            underline="never"
            @click="handleUpdate(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="'system:ossConfig:remove'"
            direction="vertical"
          />
          <el-link
            v-permission="'system:ossConfig:remove'"
            type="danger"
            underline="never"
            @click="handleDelete(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
      <!-- 添加或修改对象存储配置对话框 -->
      <el-dialog
        v-model="dialog.visible"
        :title="dialog.title"
        width="800px"
        append-to-body
      >
        <el-form
          ref="ossConfigFormRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="配置key" prop="configKey">
            <el-input v-model="form.configKey" placeholder="请输入配置key" />
          </el-form-item>
          <el-form-item label="访问站点" prop="endpoint">
            <el-input v-model="form.endpoint" placeholder="请输入访问站点" />
          </el-form-item>
          <el-form-item label="自定义域名" prop="domain">
            <el-input v-model="form.domain" placeholder="请输入自定义域名" />
          </el-form-item>
          <el-form-item label="accessKey" prop="accessKey">
            <el-input v-model="form.accessKey" placeholder="请输入accessKey" />
          </el-form-item>
          <el-form-item label="secretKey" prop="secretKey">
            <el-input
              v-model="form.secretKey"
              placeholder="请输入秘钥"
              show-password
            />
          </el-form-item>
          <el-form-item label="桶名称" prop="bucketName">
            <el-input v-model="form.bucketName" placeholder="请输入桶名称" />
          </el-form-item>
          <el-form-item label="前缀" prop="prefix">
            <el-input v-model="form.prefix" placeholder="请输入前缀" />
          </el-form-item>
          <el-form-item label="是否HTTPS">
            <dict-data code="sys_yes_no" type="radio" v-model="form.isHttps" />
          </el-form-item>
          <el-form-item label="桶权限类型">
            <el-radio-group v-model="form.accessPolicy">
              <el-radio value="0">private</el-radio>
              <el-radio value="1">public</el-radio>
              <el-radio value="2">custom</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="域" prop="region">
            <el-input v-model="form.region" placeholder="请输入域" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button
              :loading="buttonLoading"
              type="primary"
              @click="submitForm"
              >确 定</el-button
            >
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, toRefs, watch } from 'vue';
  import {
    listOssConfig,
    getOssConfig,
    delOssConfig,
    addOssConfig,
    updateOssConfig,
    changeOssConfigStatus
  } from '@/api/system/ossConfig';
  import { PlusOutlined } from '@/components/icons';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus';

  defineOptions({ name: 'OssConfig' });

  // const ossConfigList = ref([]);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  // const total = ref(0);
  const ossConfigFormRef = ref();
  const tableRef = ref();

  // 表格选中数据
  const selections = ref([]);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '配置key',
      prop: 'configKey',
      placeholder: '配置key'
    },
    {
      type: 'input',
      label: '桶名称',
      prop: 'bucketName',
      placeholder: '请输入桶名称'
    },
    {
      type: 'select',
      label: '是否默认',
      prop: 'status',
      placeholder: '请选择状态',
      options: [
        { label: '是', value: '0' },
        { label: '否', value: '1' }
      ]
    }
  ]);

  const dialog = reactive({
    visible: false,
    title: ''
  });

  // 表格列配置
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'ossConfigId',
      label: '主建',
      width: 100,
      align: 'center',
      hideInTable: true
    },
    {
      prop: 'configKey',
      label: '配置key',
      minWidth: 120,
      align: 'center'
    },
    {
      prop: 'endpoint',
      label: '访问站点',
      minWidth: 200,
      align: 'center'
    },
    {
      prop: 'domain',
      label: '自定义域名',
      minWidth: 200,
      align: 'center'
    },
    {
      prop: 'bucketName',
      label: '桶名称',
      minWidth: 120,
      align: 'center'
    },
    {
      prop: 'prefix',
      label: '前缀',
      minWidth: 100,
      align: 'center'
    },
    {
      prop: 'region',
      label: '域',
      minWidth: 100,
      align: 'center'
    },
    {
      columnKey: 'accessPolicy',
      prop: 'accessPolicy',
      label: '桶权限类型',
      minWidth: 120,
      align: 'center',
      slot: 'accessPolicy'
    },
    {
      columnKey: 'status',
      prop: 'status',
      label: '是否默认',
      minWidth: 100,
      align: 'center',
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      minWidth: 150,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  const initFormData = {
    ossConfigId: undefined,
    configKey: '',
    accessKey: '',
    secretKey: '',
    bucketName: '',
    prefix: '',
    endpoint: '',
    domain: '',
    isHttps: 'N',
    accessPolicy: '1',
    region: '',
    status: '1',
    remark: ''
  };

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    queryParams.value[prop] = value;
  };

  const data = reactive({
    form: { ...initFormData },
    // 查询参数
    queryParams: {
      configKey: '',
      bucketName: '',
      status: ''
    },
    rules: {
      configKey: [
        { required: true, message: 'configKey不能为空', trigger: 'blur' }
      ],
      accessKey: [
        { required: true, message: 'accessKey不能为空', trigger: 'blur' },
        {
          min: 2,
          max: 200,
          message: 'accessKey长度必须介于 2 和 100 之间',
          trigger: 'blur'
        }
      ],
      secretKey: [
        { required: true, message: 'secretKey不能为空', trigger: 'blur' },
        {
          min: 2,
          max: 100,
          message: 'secretKey长度必须介于 2 和 100 之间',
          trigger: 'blur'
        }
      ],
      bucketName: [
        { required: true, message: 'bucketName不能为空', trigger: 'blur' },
        {
          min: 2,
          max: 100,
          message: 'bucketName长度必须介于 2 和 100 之间',
          trigger: 'blur'
        }
      ],
      endpoint: [
        { required: true, message: 'endpoint不能为空', trigger: 'blur' },
        {
          min: 2,
          max: 100,
          message: 'endpoint名称长度必须介于 2 和 100 之间',
          trigger: 'blur'
        }
      ],
      accessPolicy: [
        { required: true, message: 'accessPolicy不能为空', trigger: 'blur' }
      ]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return listOssConfig({ ...where, ...orders, ...filters, ...pages });
  };

  /** 查询对象存储配置列表 */
  const getList = async () => {
    reload(data.queryParams);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  /** 取消按钮 */
  const cancel = () => {
    dialog.visible = false;
    reset();
  };
  /** 表单重置 */
  const reset = () => {
    form.value = { ...initFormData };
    ossConfigFormRef.value?.resetFields();
  };
  /** 搜索按钮操作 */
  const onSearch = (where) => {
    Object.assign(data.queryParams, where);
    reload(data.queryParams);
  };
  /** 重置按钮操作 */
  const resetQuery = () => {
    reload({});
  };

  // 监听表格选择变化
  const handleSelectionsChange = () => {
    ids.value = selections.value.map((item) => item.ossConfigId);
    single.value = selections.value.length != 1;
    multiple.value = !selections.value.length;
  };

  // 监听 selections 变化
  watch(selections, handleSelectionsChange, { immediate: true });
  /** 新增按钮操作 */
  const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = '添加对象存储配置';
  };
  /** 修改按钮操作 */
  const handleUpdate = async (row) => {
    reset();
    const ossConfigId = row?.ossConfigId || ids.value[0];
    const res = await getOssConfig(ossConfigId);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = '修改对象存储配置';
  };
  /** 提交按钮 */
  const submitForm = () => {
    ossConfigFormRef.value?.validate(async (valid) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.ossConfigId) {
          updateOssConfig(form.value)
            .then(async () => {
              EleMessage.success({ message: '修改成功', plain: true });
              dialog.visible = false;
              await getList();
            })
            .catch((e) => {
              EleMessage.error({ message: e.message, plain: true });
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          addOssConfig(form.value)
            .then(async () => {
              EleMessage.success({ message: '新增成功', plain: true });
              dialog.visible = false;
              await getList();
            })
            .catch((e) => {
              EleMessage.error({ message: e.message, plain: true });
            })
            .finally(() => (buttonLoading.value = false));
        }
      }
    });
  };
  /** 状态修改  */
  const handleStatusChange = async (row) => {
    let text = row.status === '0' ? '启用' : '停用';
    try {
      await ElMessageBox.confirm(
        `确认要"${text}"${row.configKey}配置吗?`,
        '系统提示',
        {
          type: 'warning',
          draggable: true
        }
      );
      await changeOssConfigStatus(row.ossConfigId, row.status, row.configKey);
      await getList();
      EleMessage.success({ message: text + '成功', plain: true });
    } catch {
      return;
    } finally {
      row.status = row.status === '0' ? '1' : '0';
    }
  };
  /** 删除按钮操作 */
  const handleDelete = async (row) => {
    const ossConfigIds = row?.ossConfigId || ids.value;
    ElMessageBox.confirm(
      '是否确认删除OSS配置编号为"' + ossConfigIds + '"的数据项?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(async () => {
        loading.value = true;
        await delOssConfig(ossConfigIds).finally(() => (loading.value = false));
        await getList();
        EleMessage.success({ message: '删除成功', plain: true });
      })
      .catch(() => {
        loading.value = false;
      });
  };
</script>
