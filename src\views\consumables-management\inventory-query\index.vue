<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <EleTableSearch
        :model="form"
        :items="items"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'inventory:inventory:export'"
            @click="exportFun"
            plain
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        :loadOnCreated="false"
        :show-overflow-tooltip="true"
        cache-key="inventory-query"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <!-- 库存总量 -->

        <template #inventoryNumber="{ row }">
          <span
            :class="
              row.consumableThreshold >= Number(row.inventoryNumber || 0)
                ? 'red'
                : ''
            "
            >{{ row.inventoryNumber }}</span
          >
        </template>
        <template #endDate="{ row }">
          <span :class="isFutureDate(row.endDate) ? 'red' : ''">{{
            row.endDate
          }}</span>
        </template>
        <template #consumableType="{ row }">
          <dict-data
            code="consumable_type"
            type="tag"
            :model-value="row.consumableType"
          />
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'inventory:inventory:query'"
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, unref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getInventoryList,
    exportInventory
  } from '@/api/consumables-management/inventory-query';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'InventoryQuery' });

  const { push, currentRoute } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    consumableName: '',
    consumableBrand: '',
    consumableType: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
    if (prop === 'inventoryAlert') {
      reload(form);
    }
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      type: 'input',
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '耗材类型',
      type: 'dictSelect',
      prop: 'consumableType',
      props: { code: 'consumable_type' }
    },
    {
      label: '库存状态',
      type: 'checkbox',
      prop: 'inventoryAlert',
      options: [{ label: '库存不足', value: '1' }]
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'systemId',
        label: '耗材ID',
        width: 100,
        align: 'center'
      },
      {
        prop: 'consumableType',
        label: '耗材类型',
        slot: 'consumableType',
        align: 'center'
      },
      {
        prop: 'consumableName',
        label: '耗材名称',
        align: 'center'
      },
      {
        prop: 'consumableSize',
        label: '规格尺寸',
        align: 'center'
      },
      {
        prop: 'consumableMaterial',
        label: '颜色材质',
        align: 'center'
      },
      {
        prop: 'consumableBrand',
        label: '品牌',
        align: 'center'
      },
      {
        prop: 'inventoryNumber',
        label: '库存总量',
        align: 'center',
        slot: 'inventoryNumber'
      },
      {
        prop: 'endDate',
        label: '最近批号过期日期',
        align: 'center',
        minWidth: 150,
        slot: 'endDate'
      },
      {
        prop: 'consumablePackagingUnit',
        label: '包装单位',
        align: 'center'
      },
      {
        prop: 'consumablePackagingSpecification',
        label: '包装规格',
        align: 'center'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 120,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getInventoryList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    const myWhere = { ...where };
    if (myWhere?.inventoryAlert?.length > 0) {
      myWhere.inventoryAlert = 1;
    } else {
      myWhere.inventoryAlert = null;
    }
    tableRef.value?.reload?.({ where: myWhere, page });
  };

  const openDetail = (row) => {
    push({
      path: '/consumables-management/inventory-query/details',
      query: {
        id: row.consumableId
      }
    });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportInventory({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
  //判断日期时间
  function isFutureDate(dateToCheck) {
    const currentDate = new Date(); // 当前日期
    const dateToCheck1 = new Date(dateToCheck);
    return dateToCheck1 < currentDate;
  }

  onMounted(() => {
    const { query } = unref(currentRoute);
    if (query.inventoryAlert) {
      form.inventoryAlert = [query.inventoryAlert];
    }
    reload(form);
  });
</script>
<style lang="scss" scoped>
  .red {
    color: red;
  }
</style>
