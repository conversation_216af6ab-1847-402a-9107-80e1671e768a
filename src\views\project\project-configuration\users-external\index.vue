<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="true"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <!-- <ele-tooltip
            content="单个新增"
            type="warning"
            placement="top"
            bg="linear-gradient( 135deg, #43CBFF 10%, #9708CC 100%)"
            arrow-bg="#7556E0"
            :offset="8"
          > -->
          <el-button
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openAdd()"
            v-permission="'project:grant:add'"
          >
            新增
          </el-button>
          <!-- </ele-tooltip> -->
          <el-button
            plain
            @click="openImport()"
            v-permission="'project:grant:externalImport'"
          >
            导入
          </el-button>
          <el-button
            plain
            @click="openExternalEdit()"
            v-permission="'project:grant:externalExport'"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :pagination="false"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '基础列表数据', datasource: exportSource }"
        :print-config="{ datasource: exportSource }"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="users-external-table"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #hospitalNum="{ row }">
          {{ row.hospitalList.map((item) => item.hospitalName).join(',') }}
        </template>
        <!-- 状态列 -->
        <template #impowerStatus="{ row }">
          <el-tag v-if="row.impowerStatus == 0"> 授权中 </el-tag>
          <el-tag type="danger" v-if="row.impowerStatus == 1">
            终止授权
          </el-tag>
        </template>
        <!-- 角色列 -->
        <template #roles="{ row }">
          <el-tag
            v-for="item in row.roles"
            :key="item.roleId"
            size="small"
            :disable-transitions="true"
            style="margin-right: 6px"
          >
            {{ item.roleName }}
          </el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <!-- <el-link
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            查看
          </el-link>
          <el-divider direction="vertical" /> -->
          <el-link
            v-if="row.impowerStatus != 1"
            type="primary"
            underline="never"
            @click.stop="openCancel(row)"
            v-permission="'project:grant:cancel'"
          >
            取消授权
          </el-link>
          <!-- <el-divider direction="vertical" />
          <el-link type="danger" underline="never" @click.stop="remove(row)">
            删除
          </el-link> -->
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <external-edit
      ref="consumableEditRef"
      v-model="visibleModal"
      @done="reload"
    />
    <external-import v-model="showImport" @done="reload" />
    <passwordAuth
      v-model="visiblePasswordModal"
      title="取消授权"
      @success="saveCancelAuth"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getProjectGrantExternalList,
    removeProjectGrant,
    importProjectGrantExternalList,
    exportProjectGrantExternalList
  } from '@/api/project-configuration';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import externalEdit from './external-edit.vue';
  import externalImport from './external-import.vue';
  import passwordAuth from '@/components/PasswordAuth/index.vue';

  const { push } = useRouter();
  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 用户名筛选值 */
  const nicknameFilterValue = ref('');

  const visiblePasswordModal = ref(false);

  /** 表单数据 */
  const form = reactive({
    username: '',
    organizationName: '',
    phone: '',
    email: '',
    createTime: ['', ''],
    sex: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '账号',
      prop: 'userName',
      props: {
        labelWidth: '120px'
      }
    },
    {
      type: 'input',
      label: '用户姓名',
      prop: 'nickName'
    },
    {
      type: 'input',
      label: '联系方式',
      prop: 'phonenumber'
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 55,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'nickName',
        label: '用户姓名',
        minWidth: 110
      },
      {
        prop: 'userName',
        label: '账号',
        minWidth: 110
      },
      {
        prop: 'phonenumber',
        label: '联系方式',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'roleName',
        label: '项目角色',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'hospitalNum',
        label: '授权医院',
        minWidth: 110,
        slot: 'hospitalNum',
        align: 'left',
        showOverflowTooltip: true
      },
      {
        prop: 'impowerStatus',
        label: '授权状态',
        align: 'center',
        minWidth: 110,
        slot: 'impowerStatus'
      },
      {
        prop: 'impowerBy',
        label: '授权人',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'impowerTime',
        label: '授权日期',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'cancelImpowerBy',
        label: '终止授权人',
        minWidth: 110,
        align: 'center'
      },
      {
        prop: 'cancelImpowerTime',
        label: '终止授权日期',
        minWidth: 110,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        minWidth: 100,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);
  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return getProjectGrantExternalList({ ...where, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
      const ids = [45, 47, 48];
      tableRef.value?.setSelectedRowKeys?.(ids);
    });
  };

  /** 搜索事件 */
  const onSearch = (where) => {
    const [d1, d2] = where.createTime ?? [];
    const time = {
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    };
    Object.assign(lastWhere, where, time);
    doReload();
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 删除 */
  const handleRow = ref(null);
  const openCancel = (row) => {
    handleRow.value = row;
    visiblePasswordModal.value = true;
  };

  const saveCancelAuth = (params) => {
    removeProjectGrant({ ...params, grantIds: [handleRow.value.grantId] }).then(
      () => {
        EleMessage.success('操作成功');
        visiblePasswordModal.value = false;
        reload();
      }
    );
  };

  /** 表格搜索 */
  const doReload = () => {
    if (nicknameFilterValue.value) {
      reload({
        ...lastWhere,
        nickname: nicknameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 导出数据 */
  const openExternalEdit = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportProjectGrantExternalList({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
  /** 导入数据 */
  const openImport = () => {
    showImport.value = true;
    // const loading = EleMessage.loading({
    //   message: '请求中..',
    //   plain: true
    // });
    // tableRef.value?.fetch?.(({ pages, where }) => {
    //   importProjectGrantExternalList({ updateSupport: false })
    //     .then(() => {
    //       loading.close();
    //     })
    //     .catch((e) => {
    //       loading.close();
    //       EleMessage.error(e.message);
    //     });
    // });
  };
  /** 新增 */
  const openAdd = (row) => {
    visibleModal.value = true;
  };
</script>
