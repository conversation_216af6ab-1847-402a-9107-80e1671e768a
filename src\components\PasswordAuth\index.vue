<template>
  <ele-modal
    form
    :width="550"
    :title="title"
    v-model="visible"
    :move-out="moveOut"
    :resizable="modalResizable"
    :maxable="maxable"
    :inner="inner"
    :reset-on-close="resetOnClose"
    :position="position"
    :append-to-body="true"
    :z-index="2001"
    @closed="cancelDialog"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :footer="true"
      :label-width="120"
      :grid="{ span: 24 }"
      :footerStyle="{ justifyContent: 'flex-end' }"
      @updateValue="setFieldValue"
    >
      <template #footer>
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="save"> 保存 </el-button>
      </template>
    </pro-form>
  </ele-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '作废'
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    password: '',
    reason: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '密码验证',
      prop: 'password',
      type: 'input',
      props: {
        showPassword: true
      },
      required: true
    },
    {
      label: '操作原因',
      prop: 'reason',
      type: 'input',
      required: true
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      emit('success', form);
    });
  };

  watch(
    () => visible.value,
    (value) => {
      if (value) {
        resetFields();
      }
    }
  );
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
</style>
