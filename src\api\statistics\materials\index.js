import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//列表
export async function getMaterialStatList(params) {
  const res = await request.get('/materialStat/selectMaterialStatList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 获取项目耗材包下耗材明细
export async function getConsumablesDetailsVoList(params) {
  const res = await request.get(
    '/project/consumables/selectConsumablesDetailsVoList',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportMaterialStat(params) {
  const res = await request({
    url: '/materialStat/exportConsumablesPackageStat',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资统计_${Date.now()}.xlsx`);
}
