<template>
  <ele-page :flex-table="true" hide-footer>
    <ele-card :flex-table="true">
      <ele-table-search
        :model="queryParams"
        :items="searchItems"
        @updateValue="updateFormValue"
        @submit="handleQuery"
        @reset="resetQuery"
      />
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="ossId"
        :columns="columns"
        :sticky="true"
        :datasource="datasource"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="oss-table"
        @done="onDone"
      >
        <template #toolbar>
          <el-button
            v-permission="'system:oss:upload'"
            type="primary"
            :icon="UploadOutlined"
            @click="handleFile"
          >
            上传文件
          </el-button>
          <el-button
            v-permission="'system:oss:upload'"
            type="primary"
            :icon="UploadOutlined"
            @click="handleImage"
          >
            上传图片
          </el-button>
          <el-button
            v-permission="'system:oss:remove'"
            type="danger"
            :icon="DeleteOutlined"
            :disabled="multiple"
            @click="handleDelete()"
          >
            删除
          </el-button>
          <!-- <el-button
            v-permission="'system:oss:edit'"
            :type="previewListResource ? 'danger' : 'warning'"
            @click="handlePreviewListResource(!previewListResource)"
          >
            预览开关 : {{ previewListResource ? '禁用' : '启用' }}
          </el-button> -->
          <el-button
            v-permission="'system:ossConfig:list'"
            type="info"
            @click="handleOssConfig"
          >
            配置管理
          </el-button>
        </template>
        <!-- 文件展示列 -->
        <template #url="{ row }">
          <ImagePreview
            v-if="previewListResource && checkFileSuffix(row.fileSuffix)"
            :width="100"
            :height="100"
            :src="row.url"
            :preview-src-list="[row.url]"
          />
          <span
            v-if="!checkFileSuffix(row.fileSuffix) || !previewListResource"
            v-text="row.url"
          ></span>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            type="primary"
            underline="never"
            v-permission="'system:oss:download'"
            @click.stop="handleDownload(row)"
          >
            下载
          </el-link>
          <el-divider v-permission="'system:oss:remove'" direction="vertical" />
          <el-link
            v-permission="'system:oss:remove'"
            type="danger"
            underline="never"
            @click.stop="handleDelete(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 添加或修改OSS对象存储对话框 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      append-to-body
    >
      <el-form ref="ossFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件名">
          <fileUpload
            ref="fileUploadRef"
            v-if="type === 0"
            :limit="4"
            v-model="form.file"
          />
          <imageUpload
            ref="imageUploadRef"
            v-if="type === 1"
            :limit="1"
            v-model="form.file"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, toRefs, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { listOss, delOss } from '@/api/system/oss';
  import ImagePreview from '@/components/ImagePreview/index.vue';
  import { UploadOutlined, DeleteOutlined } from '@/components/icons';
  import fileUpload from '@/components/FileUpload/index.vue';
  import imageUpload from '@/components/ImageUpload/index.vue';
  // import { downloadUrl } from '@/utils/common';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import download from '@/utils/download';

  defineOptions({ name: 'Oss' });

  const router = useRouter();

  // const ossList = ref([]);
  const showTable = ref(true);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const type = ref(0);
  const previewListResource = ref(true);

  const dialog = reactive({
    visible: false,
    title: ''
  });

  // 默认排序
  const defaultSort = ref({ prop: 'createTime', order: 'ascending' });

  const ossFormRef = ref();
  const tableRef = ref();

  // 表格选中数据
  const selections = ref([]);

  // 搜索项配置
  const searchItems = ref([
    {
      type: 'input',
      label: '文件名',
      prop: 'fileName',
      placeholder: '请输入文件名'
    },
    {
      type: 'input',
      label: '原名',
      prop: 'originalName',
      placeholder: '请输入原名'
    },
    {
      type: 'input',
      label: '文件后缀',
      prop: 'fileSuffix',
      placeholder: '请输入文件后缀'
    },
    {
      type: 'daterange',
      label: '创建时间',
      prop: 'createTime',
      placeholder: ['开始日期', '结束日期']
    },
    {
      type: 'input',
      label: '服务商',
      prop: 'service',
      placeholder: '请输入服务商'
    }
  ]);

  // 表格列配置
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'ossId',
      label: '对象存储主键',
      width: 100,
      align: 'center',
      hideInTable: true
    },
    {
      prop: 'fileName',
      label: '文件名',
      minWidth: 120,
      align: 'center'
    },
    {
      prop: 'originalName',
      label: '原名',
      minWidth: 120,
      align: 'center'
    },
    {
      prop: 'fileSuffix',
      label: '文件后缀',
      minWidth: 100,
      align: 'center'
    },
    {
      columnKey: 'url',
      prop: 'url',
      label: '文件展示',
      minWidth: 150,
      align: 'center',
      slot: 'url'
    },
    {
      columnKey: 'createTime',
      prop: 'createTime',
      label: '创建时间',
      minWidth: 120,
      align: 'center',
      sortable: 'custom'
    },
    {
      prop: 'createByName',
      label: '上传人',
      minWidth: 100,
      align: 'center'
    },
    {
      prop: 'service',
      label: '服务商',
      minWidth: 100,
      align: 'center',
      sortable: 'custom'
    },
    {
      columnKey: 'action',
      label: '操作',
      minWidth: 120,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    queryParams.value[prop] = value;
  };

  const initFormData = {
    file: []
  };
  const data = reactive({
    form: { ...initFormData },
    // 查询参数
    queryParams: {
      fileName: '',
      originalName: '',
      fileSuffix: '',
      createTime: '',
      service: '',
      orderByColumn: defaultSort.value.prop,
      isAsc: defaultSort.value.order
    },
    rules: {
      file: [{ required: true, message: '文件不能为空', trigger: 'blur' }]
    }
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    // 处理日期范围
    const params = { ...where, ...orders, ...filters, ...pages };
    if (where.createTime && Array.isArray(where.createTime)) {
      const [startTime, endTime] = where.createTime;
      params.params = {
        beginTime: startTime,
        endTime: endTime
      };
      // params.parmas['beginCreateTime'] = startTime
      //   ? `${startTime} 00:00:00`
      //   : '';
      // params.params['endCreateTime'] = endTime ? `${endTime} 23:59:59` : '';
      delete params.createTime;
    }

    return listOss(params);
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {
    showTable.value = true;
  };

  /** 查询OSS对象存储列表 */
  const getList = async () => {
    reload(data.queryParams);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  function checkFileSuffix(fileSuffix) {
    const arr = ['.png', '.jpg', '.jpeg'];
    const suffixArray = Array.isArray(fileSuffix) ? fileSuffix : [fileSuffix];
    return suffixArray.some((suffix) => arr.includes(suffix.toLowerCase()));
  }
  /** 取消按钮 */
  function cancel() {
    dialog.visible = false;
    reset();
  }
  /** 表单重置 */
  const fileUploadRef = ref();
  const imageUploadRef = ref();
  function reset() {
    if (type.value === 0) {
      fileUploadRef.value?.clearData();
    } else {
      imageUploadRef.value?.clearData();
    }
    form.value = { ...initFormData };
    ossFormRef.value?.resetFields();
  }
  /** 搜索按钮操作 */
  function handleQuery(where) {
    reload(where);
  }
  /** 重置按钮操作 */
  function resetQuery() {
    reload({});
  }

  // 监听表格选择变化
  const handleSelectionsChange = () => {
    ids.value = selections.value.map((item) => item.ossId);
    single.value = selections.value.length != 1;
    multiple.value = !selections.value.length;
  };

  // 监听 selections 变化
  watch(selections, handleSelectionsChange, { immediate: true });
  /** 任务日志列表查询 */
  const handleOssConfig = () => {
    router.push('/system/oss/config');
  };
  /** 文件按钮操作 */
  const handleFile = () => {
    type.value = 0;
    dialog.visible = true;
    dialog.title = '上传文件';
    reset();
  };
  /** 图片按钮操作 */
  const handleImage = () => {
    type.value = 1;
    dialog.visible = true;
    dialog.title = '上传图片';
    // reset();
  };
  /** 提交按钮 */
  const submitForm = () => {
    console.log(form.value.file);
    dialog.visible = false;
    getList();
  };
  /** 下载按钮操作 */
  const handleDownload = (row) => {
    // downloadUrl(row.url, row.name);
    download.oss(row.ossId); // 下载文件
  };
  /** 预览开关按钮  */
  // const handlePreviewListResource = async (preview) => {
  //   let text = preview ? '启用' : '停用';
  //   try {
  //     await ElMessageBox.confirm(
  //       '确认要"' + text + '""预览列表图片"配置吗?',
  //       '系统提示',
  //       {
  //         type: 'warning',
  //         draggable: true
  //       }
  //     );
  //     // await proxy?.$modal.confirm('确认要"' + text + '""预览列表图片"配置吗?');
  //     // await proxy?.updateConfigByKey('sys.oss.previewListResource', preview);
  //     await getList();
  //     EleMessage.success({ message: text + '成功', plain: true });
  //     // proxy?.$modal.msgSuccess(text + '成功');
  //   } catch {
  //     return;
  //   }
  // };
  /** 删除按钮操作 */
  const handleDelete = async (row) => {
    const ossIds = row?.ossId || ids.value;
    await ElMessageBox.confirm(
      '是否确认删除OSS对象存储编号为"' + ossIds + '"的数据项?',
      '系统提示',
      {
        type: 'warning',
        draggable: true
      }
    );
    // await proxy?.$modal.confirm(
    //   '是否确认删除OSS对象存储编号为"' + ossIds + '"的数据项?'
    // );
    loading.value = true;
    await delOss(ossIds).finally(() => (loading.value = false));
    await getList();
    EleMessage.success({ message: '删除成功', plain: true });
    // proxy?.$modal.msgSuccess('删除成功');
  };
</script>
