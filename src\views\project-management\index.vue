<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'project:project:add'"
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            v-permission="'project:project:export'"
            plain
            @click="exportFun"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="project-management"
        :border="tableBorder"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #projectStatus="{ row }">
          <el-tag :type="statusMap[row.projectStatus]">{{
            statusObJ[row.projectStatus]
          }}</el-tag>
        </template>
        <template #projectAttribute="{ row }">
          <dict-data
            code="project_attribute"
            type="tag"
            :model-value="row.projectAttribute"
          />
        </template>
        <template #projectHospitalList="{ row }">
          <el-link
            v-if="
              !row?.projectHospitalList || row.projectHospitalList.length == 0
            "
            type="primary"
            underline="never"
            >未配置</el-link
          >
          <el-link
            v-if="row.projectHospitalList.length == 1"
            type="primary"
            underline="never"
          >
            {{ row.projectHospitalList[0].hospitalName }}
          </el-link>
          <el-tooltip
            v-if="row.projectHospitalList.length > 1"
            class="box-item"
            effect="dark"
            :content="getContent(row.projectHospitalList)"
            placement="top"
          >
            <el-link type="primary" underline="never">
              {{ row.projectHospitalList.length }}
            </el-link>
          </el-tooltip>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'project:project:edit'"
            :disabled="row.projectStatus != '1'"
            type="primary"
            underline="never"
            @click.stop="openEdit(row.projectId)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['project:project:edit', 'project:project:query']"
            direction="vertical"
          />
          <el-link
            type="primary"
            underline="never"
            v-permission="['project:project:query']"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
          <el-divider
            v-permission="'project:project:query'"
            direction="vertical"
          />
          <ele-dropdown
            :items="buttonItems(row)"
            :icon-props="{ size: 15 }"
            placement="bottom-start"
            style="margin-left: 12px"
            v-model="commandName"
            @command="onDropClick(row)"
          >
            <el-link type="primary" class="ele-btn-icon">
              <span>更多</span>
              <el-icon :size="12" style="margin: 0 -4px 0 2px">
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>

      <handleModal
        v-model="visibleModal"
        :handle="handle"
        :id="projectId"
        @success="reload"
      />
      <handleModalClose
        v-model="visibleModalClose"
        :editData="editData || {}"
        @success="reload"
      />
      <handleModalSupplementary
        v-model="visibleModalSupplementary"
        :editData="editData || {}"
      />
      <handleModalDiscard
        v-model="visibleModalDiscard"
        :id="projectId"
        @success="reload"
      />
      <handleModalImprove v-model="visibleModalImprove" :id="projectId" />
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined, ArrowDown } from '@/components/icons';
  import {
    getProjectList,
    exportProject,
    projectPause,
    projectOpen
  } from '@/api/project-management';
  import handleModal from './handleModal/index.vue';
  import handleModalClose from './handleModal/close.vue';
  import handleModalSupplementary from './handleModal/supplementary.vue';
  import handleModalDiscard from './handleModal/discard.vue';
  import handleModalImprove from './handleModal/improve.vue';

  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { usePermission } from '@/utils/use-permission';

  defineOptions({ name: 'ProjectManagement' });

  const { push } = useRouter();
  const userStore = useUserStore();
  const { hasPermission } = usePermission();

  const buttonItems = (row) => {
    const items = [];
    if (hasPermission('project:project:close')) {
      items.push({
        title: '关闭',
        command: 'close',
        disabled: row.projectStatus == '3'
      });
    }
    if (hasPermission('project:project:pause')) {
      items.push({
        title: '暂停',
        command: 'pause',
        disabled: row.projectStatus != '1'
      });
    }
    if (hasPermission('project:project:open')) {
      items.push({
        title: '开启',
        command: 'open',
        disabled: row.projectStatus == '1'
      });
    }
    if (hasPermission('project:project:discard')) {
      items.push({
        title: '废弃',
        command: 'discard',
        disabled: row.projectStatus != '1'
      });
    }
    if (hasPermission('project:project:improve')) {
      items.push({
        title: '补充样本',
        command: 'supplementary',
        disabled: row.projectStatus != '3'
      });
    }
    return items;
  };

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    analyseProjectNumber: '',
    schemeNumber: '',
    projectClient: '',
    companyProjectNumber: '',
    projectName: '',
    sponsor: '',
    projectAttribute: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '委托方',
      prop: 'projectClient'
    },
    {
      type: 'input',
      label: '公司项目编号',
      prop: 'companyProjectNumber'
    },
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    },
    {
      type: 'input',
      label: '申办方',
      prop: 'sponsor'
    },
    {
      type: 'dictSelect',
      label: '项目属性',
      prop: 'projectAttribute',
      props: { code: 'project_attribute' }
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'projectStatus',
        label: '项目状态',
        slot: 'projectStatus',
        align: 'center'
      },
      {
        prop: 'systemId',
        label: '项目ID',
        align: 'center'
      },
      {
        prop: 'analyseProjectNumber',
        label: '分析项目编号',
        align: 'center'
      },
      {
        prop: 'projectLeader',
        label: '项目负责人',
        align: 'center'
      },
      {
        prop: 'projectName',
        label: '项目名称',
        align: 'center'
      },
      {
        prop: 'projectStudyName',
        label: '研究名称',
        align: 'center'
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        align: 'center'
      },
      {
        prop: 'sponsor',
        label: '申办方',
        align: 'center'
      },
      {
        prop: 'projectClient',
        label: '委托方',
        align: 'center'
      },
      {
        prop: 'sponsor',
        label: '申办方',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'projectHospitalList',
        label: '研究机构',
        align: 'center',
        slot: 'projectHospitalList',
        hideInTable: true
      },
      {
        prop: 'projectAttribute',
        label: '项目属性',
        align: 'center',
        slot: 'projectAttribute',
        hideInTable: true
      },
      {
        prop: 'approvalDate',
        label: '立项日期',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'endDate',
        label: '结束日期',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  const statusObJ = {
    1: '在研中',
    2: '暂停',
    3: '结束',
    4: '废弃'
  };
  const statusMap = {
    1: 'primary',
    2: 'warning',
    3: 'danger',
    4: 'info'
  };

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getProjectList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  const changeTableBorder = () => {
    tableBorder.value = !tableBorder.value;
  };

  const supplementaryDisabled = (row) => {
    console.log(row.projectStatus != '3', 'llll');
    return row.projectStatus != '3';
  };

  const goConfig = (row) => {
    userStore.setUserInfoByProject(row.projectId, '/project-management');
    push({
      path: '/project/project-configuration/institutional-information'
    });
  };

  const getContent = (list) => {
    return list.map((el) => el.hospitalName).join(',');
  };

  const openDetail = (row) => {
    userStore.setUserInfoByProject(row.projectId, '/project-management');
    push({
      path: '/project/information',
      query: {
        id: row?.projectId
      }
    });
  };

  /** 编辑 */
  const openEdit = (id) => {
    handle.value = id ? 'edit' : 'add';
    projectId.value = id || null;
    visibleModal.value = true;
  };

  const visibleModalClose = ref(false);
  const visibleModalSupplementary = ref(false);
  const visibleModalDiscard = ref(false);
  const visibleModalImprove = ref(false);
  const projectId = ref('');
  const commandName = ref('');
  /** 下拉按钮点击 */
  const onDropClick = (row) => {
    if (commandName.value === 'close') {
      visibleModalClose.value = true;
      editData.value = row || null;
    } else if (commandName.value === 'pause' || commandName.value === 'open') {
      pauseOrOpenClick(row, commandName.value);
    } else if (commandName.value == 'discard') {
      projectId.value = row.projectId;
      visibleModalDiscard.value = true;
    } else if (commandName.value == 'supplementary') {
      projectId.value = row.projectId;
      visibleModalImprove.value = true;
    }
  };
  //暂停项目|开启项目
  const pauseOrOpenClick = (row, type) => {
    const title =
      type == 'open'
        ? '你确认开启该项目么？开启后，所有流程都会被继续运转'
        : '你确认暂停该项目么？暂停后，所有流程都会被冻结且不允许操作（暂停后可再次开启）';
    const typeName = type == 'open' ? '开启' : '暂停';
    const url = type == 'open' ? projectOpen : projectPause;
    ElMessageBox.confirm(title, `${typeName}项目`, {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        url({ projectId: row.projectId })
          .then(() => {
            loading.close();
            EleMessage.success('操作成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  // 废弃项目
  const suspendedClick = () => {
    ElMessageBox.prompt(
      '你确认暂停该项目么？暂停后，所有流程都会被冻结且不允许操作（暂停后可再次开启）',
      '暂停项目',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入密码验证'
      }
    )
      .then(({ value }) => {})
      .catch(() => {});
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportProject({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
