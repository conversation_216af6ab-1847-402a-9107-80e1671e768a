<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '20px', paddingTop: '0px' }"
    :style="{
      minHeight: fixedHeight ? '100px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="耗材包信息"
    bordered
  >
    <ele-pro-table
      row-key="userId"
      :columns="columns"
      :datasource="datasource"
      :pagination="false"
      border
    >
      <template #quantity="{ row }">
        <el-input-number
          v-if="isEdit"
          v-model="row.quantity"
          style="width: 100%"
        />
        <span v-else>{{ row.quantity }}</span>
      </template>
    </ele-pro-table>
  </ele-card>
</template>
<script setup>
  import { ref, watch } from 'vue';
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const columns = ref([
    {
      label: '耗材包编号',
      prop: 'consumablesNumber',
      width: 100
    },
    {
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      label: '耗材包备注',
      prop: 'remark'
    },
    {
      label: '申请数量',
      prop: 'quantity',
      slot: 'quantity'
    },
    {
      label: '已发运数量',
      prop: 'sendQuantity'
    },
    {
      label: '已接收数量',
      prop: 'receiveQuantity'
    }
  ]);
  const datasource = ref([]);
  watch(
    () => props.data,
    () => {
      datasource.value = props.data?.consumablesVoList;
    },
    { immediate: true, deep: true }
  );
</script>
