// vite.config.js
import { defineConfig, loadEnv } from "file:///D:/code/zxzy/cmms-web/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/code/zxzy/cmms-web/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import Compression from "file:///D:/code/zxzy/cmms-web/node_modules/vite-plugin-compression/dist/index.mjs";
import Components from "file:///D:/code/zxzy/cmms-web/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///D:/code/zxzy/cmms-web/node_modules/unplugin-vue-components/dist/resolvers.js";
import { EleAdminResolver } from "file:///D:/code/zxzy/cmms-web/node_modules/@hnjing/zxzy-admin-plus/es/utils/resolvers.js";
import VueDevTools from "file:///D:/code/zxzy/cmms-web/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var vite_config_default = defineConfig(({ command, mode }) => {
  const isBuild = command === "build";
  const env = loadEnv(mode, process.cwd());
  const alias = {
    "@/": resolve("src") + "/",
    "ele-admin-plus": resolve("node_modules/@hnjing/zxzy-admin-plus") + "/",
    "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
  };
  const plugins = [vue()];
  if (isBuild) {
    plugins.push(
      Components({
        dts: false,
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
          }),
          EleAdminResolver({
            importStyle: "sass"
          })
        ]
      })
    );
    plugins.push(
      Compression({
        disable: !isBuild,
        threshold: 10240,
        algorithm: "gzip",
        ext: ".gz"
      })
    );
    alias["./as-needed"] = "./global-import";
  } else {
    plugins.push(VueDevTools());
    alias["./as-needed"] = "./global-import";
  }
  return {
    resolve: { alias },
    plugins,
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    },
    base: "/",
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_APP_PORT),
      open: true,
      // 运行是否自动打开浏览器
      proxy: {
        // 反向代理解决跨域
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_BASE_API_URL,
          // 线上接口地址
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
          // 替换 /dev-api 为 target 接口地址
        },
        // [env.VITE_APP_ADMIN_BASE_API]: {
        //   target: env.VITE_APP_ADMIN_BASE_API_URL,
        //   changeOrigin: true,
        //   rewrite: path =>
        //     path.replace(new RegExp("^" + env.VITE_APP_ADMIN_BASE_API), "") // 文件分片微服务
        // },
        [env.VITE_APP_BUSINESS_BASE_API]: {
          target: env.VITE_APP_BUSINESS_BASE_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp("^" + env.VITE_APP_BUSINESS_BASE_API), "")
          // 文件分片微服务
        },
        warmup: {
          clientFiles: ["./index.html", "./src/{views,components}/*"]
        }
      }
    },
    optimizeDeps: {
      include: [
        "echarts/core",
        "echarts/charts",
        "echarts/renderers",
        "echarts/components",
        "vue-echarts",
        "echarts-wordcloud",
        "sortablejs",
        "vuedraggable"
      ]
    },
    build: {
      target: "chrome63",
      chunkSizeWarningLimit: 2e3,
      terserOptions: command === "build" ? {
        // 仅在构建时生效
        compress: {
          drop_console: true,
          // 移除console
          drop_debugger: true
          // 移除debugger
        }
      } : {}
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
