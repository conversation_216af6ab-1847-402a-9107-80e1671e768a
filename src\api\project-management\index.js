import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

//列表
export async function getProjectList(params) {
  const res = await request.get('/project/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 获取项目信息详细信息
export async function getProjectDetails(projectId) {
  const res = await request.get(`/project/${projectId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增项目信息
export async function projectAdd(data) {
  const res = await request.post('/project/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 修改项目信息
export async function projectEdit(data) {
  const res = await request.post('/project/edit', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 关闭
export async function projectClose(data) {
  const res = await request.post('/project/close', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//暂停
export async function projectPause(params) {
  const res = await request.get('/project/pause', { params });
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 开启
export async function projectOpen(params) {
  const res = await request.get('/project/open', { params });
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//废弃
export async function projectDiscard(data) {
  const res = await request.post('/project/discard', data, {
    headers: {
      isEncrypt: 'true'
    }
  });
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
//补充样本信息
export async function projectImprove(params) {
  const res = await request.get('/project/improve', { params });
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportProject(params) {
  const res = await request({
    url: '/project/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `项目管理_${Date.now()}.xlsx`);
}
