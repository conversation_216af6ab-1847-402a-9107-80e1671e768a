<template>
  <div>
    <ele-modal
      form
      :width="1200"
      :title="handle == 'edit' ? '编辑耗材包' : '新增耗材包'"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <h3>基本信息</h3>
      <pro-form
        ref="formRef"
        :model="form"
        :items="items"
        :footer="false"
        :label-width="120"
        :grid="{ span: 12 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
      </pro-form>
      <el-divider />
      <h3>耗材包明细</h3>
      <div style="text-align: right">
        <el-button
          type="primary"
          style="margin-bottom: 10px"
          @click="openEdit()"
          >添加耗材</el-button
        >
        <ele-data-table row-key="userId" :columns="columns" :data="detailsList">
          <template #substrate="{ row }">
            <dict-data
              code="sample_type"
              type="tag"
              :model-value="row.substrate"
            />
          </template>
          <template #action="{ row, $index }">
            <el-link
              type="primary"
              underline="never"
              @click.stop="openEdit(row, $index)"
            >
              编辑
            </el-link>
            <el-divider direction="vertical" />
            <el-link
              type="danger"
              underline="never"
              @click.stop="delRow($index)"
            >
              删除
            </el-link>
          </template>
        </ele-data-table>
      </div>
      <template #footer>
        <el-button @click="cancelDialog">关闭</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          保存
        </el-button>
      </template>
    </ele-modal>
    <Consumables
      :handle="handle1"
      v-model="visibleConsumables"
      :editData="InfoObj"
      @success="successAddInfo"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import ProForm from '@/components/ProForm/index.vue';
  import Consumables from './consumables.vue';
  import {
    consumablesAdd,
    consumablesEdit,
    selectProjectConsumablesSamplePointVoList
  } from '@/api/project/project-configuration/material-allocation';

  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    handle: {
      type: String,
      default: 'add'
    },
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    resetFields();
    detailsList.value = [];
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    consumablesId: void 0,
    consumablesName: '',
    pointId: '',
    sysFileIds: '',
    remark: ''
  });
  const detailsList = ref([]);

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '耗材包名称',
      prop: 'consumablesName',
      type: 'input',
      required: true
    },
    {
      label: '访视/时间',
      prop: 'pointId',
      type: 'select',
      options: [],
      required: true
    },
    {
      label: '耗材包照片',
      prop: 'sysFileIds',
      type: 'imageUpload',
      props: {
        limit: 1
      }
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea'
    }
  ]);

  // 耗材项
  // 研究信息列表配置
  const columns = ref([
    {
      label: '耗材ID',
      prop: 'systemId'
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '数量',
      prop: 'quantity'
    },
    {
      label: '样本序号',
      prop: 'sampleNumber'
    },
    {
      label: '样本类型',
      prop: 'substrate',
      slot: 'substrate',
      width: 160
    },
    {
      label: '分装号',
      prop: 'packageCode'
    },
    {
      label: '检测内容',
      prop: 'detectionContent'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 150,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  // 新增编辑耗材项
  const visibleConsumables = ref(false);
  const InfoObj = ref({});
  const handle1 = ref('add');
  const currentIndex = ref(0);
  const openEdit = (row, index) => {
    handle1.value = row ? 'edit' : 'add';
    currentIndex.value = index;
    visibleConsumables.value = true;
    InfoObj.value = row ?? null;
  };
  const successAddInfo = (info) => {
    const info1 = JSON.parse(JSON.stringify(info));
    if (handle1.value == 'add') {
      detailsList.value.push(info1);
    } else {
      detailsList.value.splice(currentIndex.value, 1, info1);
    }
  };
  //删除耗材项
  const delRow = (index) => {
    detailsList.value.splice(index, 1);
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }

      if (detailsList.value.length == 0)
        return EleMessage.warning('请添加耗材包明细');
      loading.value = true;
      const saveOrUpdate =
        props.handle == 'edit' ? consumablesEdit : consumablesAdd;
      let sysFileIds = '';
      if (form?.sysFileIds) {
        sysFileIds = JSON.parse(form.sysFileIds).ossId;
      }
      const params = {
        ...form,
        detailsList: detailsList.value,
        projectId: userStore.projectId,
        sysFileIds
      };
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  const getPoint = async () => {
    const res = await selectProjectConsumablesSamplePointVoList({
      projectId: userStore.projectId
    });
    let list = [];
    if (res.data.length !== 0) {
      list = res.data.map((el) => {
        return {
          label: `${el.groupName}/${el.visitNumber}-${el.studyTime}${el.samplePointTime ? '-' : ''}${el.samplePointTime}`,
          value: el.pointId,
          ...el
        };
      });
    }
    setPropItem('pointId', list);
  };
  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };
  watch(
    visible,
    (val) => {
      if (val) {
        getPoint();
        if (!props.editData) return;
        let sysFileIdsList = [];
        if (
          props.editData?.sysOssVoList &&
          props.editData.sysOssVoList.length !== 0
        ) {
          sysFileIdsList = props.editData.sysOssVoList.map((val) => {
            return {
              ossId: val.ossId,
              name: val.originalName,
              url: val.url
            };
          });
        }
        const sysFileIds = sysFileIdsList.length !== 0 ? sysFileIdsList : '';
        const info = JSON.parse(JSON.stringify(props.editData));
        const data = {
          ...info,
          sysFileIds
        };
        assignFields(data);
        detailsList.value = info?.consumablesDetailsVoList;
      } else {
        detailsList.value = [];
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
