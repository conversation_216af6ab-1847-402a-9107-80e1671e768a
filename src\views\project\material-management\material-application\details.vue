<template>
  <ele-page plain :multi-card="false">
    <ele-page>
      <ele-card>
        <materialBasicInfo :data="basicInfo" />
        <auditInformation
          v-if="basicInfo.auditStatus == '1'"
          :data="basicInfo"
        />
        <materialPackageAudit :data="basicInfo" :isEdit="isEdit" />
        <bulkpackageAudit :data="basicInfo" :isEdit="isEdit" />
        <reagentAudit :data="basicInfo" :isEdit="isEdit" />
      </ele-card>
    </ele-page>
    <ele-bottom-bar
      v-if="basicInfo.auditStatus == '0' && currentAction"
      teleported
    >
      <template #extra>
        <el-button type="primary" :loading="loading" @click="actionFun">
          {{ ActionObj[currentAction] }}
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import materialBasicInfo from '../components/materialBasicInfo.vue';
  import auditInformation from '../components/auditInformation.vue';
  import materialPackageAudit from '../components/materialPackageAudit.vue';
  import bulkpackageAudit from '../components/bulkpackageAudit.vue';
  import reagentAudit from '../components/reagentAudit.vue';
  import {
    materialApplyDetails,
    materialApplyRevoke
  } from '@/api/project/material-management/material-application';
  import { materialApplyAudit } from '@/api/project/material-management/material-audit';

  import { useRouter } from 'vue-router';
  const { currentRoute, push } = useRouter();

  const ActionObj = {
    revoke: '撤销',
    audit: '审核'
  };
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const basicInfo = reactive({});
  const getDetails = async (id) => {
    const { data } = await materialApplyDetails(id);
    Object.assign(basicInfo, data);
  };
  const currentAction = ref('');
  const isEdit = ref(false);
  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      currentAction.value = query?.actionType || '';
      isEdit.value = query.isEdit == 'edit';
      getDetails(query.id);
    },
    { immediate: true }
  );
  const actionFun = () => {
    if (currentAction.value == 'revoke') {
      revoke();
    }
    if (currentAction.value == 'audit') {
      audit();
    }
  };
  /** 撤销 */
  const revoke = () => {
    ElMessageBox.confirm(`确定要撤销该申请数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        materialApplyRevoke({ applyId: basicInfo.applyId })
          .then(() => {
            loading.close();
            EleMessage.success('操作成功');
            push({
              path: '/project/material-management/material-application',
              query: {
                isReload: true
              }
            });
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  /** 审核 */
  const audit = async (row) => {
    ElMessageBox.prompt(`审核备注`, '确认审核通过', {
      inputErrorMessage: '审核备注',
      draggable: true
    })
      .then(({ value }) => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        const params = {
          applyId: basicInfo.applyId,
          auditRemark: value,
          consumablesUpdateBoList: basicInfo.consumablesVoList,
          bulkConsumablesUpdateBoList: basicInfo.bulkConsumablesDetailsVoList,
          bulkReagentUpdateBoList: basicInfo.bulkReagentDetailsVoList
        };
        materialApplyAudit(params)
          .then((msg) => {
            loading.close();
            EleMessage.success('操作成功');
            push({
              path: '/project/material-management/material-audit',
              query: {
                isReload: true
              }
            });
          })
          .catch((e) => {
            loading.close();
          });
      })
      .catch(() => {});
  };
</script>
