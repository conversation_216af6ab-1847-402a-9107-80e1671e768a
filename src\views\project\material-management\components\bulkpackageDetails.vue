<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '100px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="散装耗材明细"
    bordered
  >
    <ele-pro-table
      row-key="userId"
      :columns="columns"
      :datasource="datasource"
      :pagination="false"
      border
    >
    </ele-pro-table>
  </ele-card>
</template>
<script setup>
  import { ref, watch } from 'vue';
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const columns = ref([
    {
      label: '耗材编号',
      prop: 'bulkConsumableNumber',
      width: 100
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '申请理论数量',
      prop: 'quantity'
    },
    {
      label: '申请备用数量',
      prop: 'spareQuantity'
    },
    {
      label: '已发运数量',
      prop: 'sendQuantity'
    },
    {
      label: '已接收数量',
      prop: 'receiveQuantity'
    }
  ]);
  const datasource = ref([]);
  watch(
    () => props.data,
    () => {
      datasource.value = props.data?.bulkConsumablesDetailsVoList;
    },
    { immediate: true, deep: true }
  );
</script>
