import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

//查询项目文件列表
export async function getProjectFileList(params) {
  const res = await request.get('/project/file/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//获取项目文件详细信息
export async function getProjectFileDetail(fileId) {
  const res = await request.get(`/project/file/${fileId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增项目文件
 */
export async function addProjectFile(data) {
  const res = await request.post('/project/file/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 重命名项目文件
 */
export async function renameProjectFile(data) {
  const res = await request.post('/project/file/rename', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 删除项目文件
 */
export async function removeProjectFile(fileIds) {
  const res = await request.delete('/project/file/remove/' + fileIds);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//查询项目用户授权内部列表
export async function getProjectGrantInternalList(params) {
  const res = await request.get('/project/grant/internalList', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
/**
 * 导出项目用户授权内部列表
 */
export async function exportProjectGrantInternalList(params) {
  const res = await request({
    url: '/project/grant/internalExport',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `项目用户授权内部列表${Date.now()}.xlsx`);
}
/**
 * 新增项目用户授权
 */
export async function addProjectGrant(data) {
  const res = await request.post('/project/grant/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 取消项目用户授权
 */
export async function removeProjectGrant(params) {
  const res = await request.post('/project/grant/cancel', params, {
    headers: {
      isEncrypt: 'true'
    }
  });
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//查询项目用户授权外部列表
export async function getProjectGrantExternalList(params) {
  const res = await request.get('/project/grant/externalList', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//查询用户是否授权的项目列表
export async function getProjectGrantProjectList(params) {
  const res = await request.get('/project/grant/projectList', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出项目用户授权外部列表
 */
export async function exportProjectGrantExternalList(params) {
  const res = await request({
    url: '/project/grant/externalExport',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `项目用户授权外部列表${Date.now()}.xlsx`);
}

/**
 * 导入外部用户
 */
export async function importProjectGrantExternalList(file, isUpdate) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('updateSupport', isUpdate);
  const res = await request.post('/project/grant/externalImport', formData);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 下载外部用户导入模板
 */
export async function downloadExternalTemplate() {
  const res = await request({
    url: '/project/grant/importTemplate',
    method: 'POST',
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `external_template_${Date.now()}.xlsx`);
}

//查询指定类型的项目角色（内部/外部）
export async function getProjectRoleList(type) {
  const res = await request.get(`/system/role/getProjectRole/${type}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//查询指定角色的用户列表
export async function getProjectRoleUserList(id) {
  const res = await request.get(`/system/role/getUserList/${id}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
