<template>
  <div>
    <ele-modal
      form
      :width="600"
      :title="handle == 'edit' ? '编辑试剂项' : '新增试剂项'"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import ProForm from '@/components/ProForm/index.vue';
  import {
    bulkReagentAdd,
    bulkReagentEdit
  } from '@/api/project/project-configuration/material-allocation';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();
  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    handle: {
      type: String,
      default: 'add'
    },
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    resetFields();
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    reagentId: void 0,
    reagentName: '',
    storageCondition: '',
    sysFileIds: '',
    remark: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '试剂名称',
      prop: 'reagentName',
      type: 'input',
      required: true
    },
    {
      label: '保存条件',
      prop: 'storageCondition',
      type: 'input',
      required: true
    },
    {
      label: '照片',
      prop: 'sysFileIds',
      type: 'imageUpload',
      props: {
        limit: 1
      }
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea'
    }
  ]);
  const isUpdate = ref(false);
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? bulkReagentEdit : bulkReagentAdd;
      const projectId = userStore.projectId;
      let sysFileIds = '';
      if (form?.sysFileIds) {
        sysFileIds = JSON.parse(form.sysFileIds).ossId;
      }
      const params = {
        ...form,
        projectId: projectId,
        sysFileIds
      };
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  watch(
    visible,
    (val) => {
      if (val) {
        isUpdate.value = false;
        if (props.handle == 'add') return;
        let sysFileIdsList = [];
        if (
          props.editData?.sysOssVoList &&
          props.editData.sysOssVoList.length !== 0
        ) {
          sysFileIdsList = props.editData.sysOssVoList.map((val) => {
            return {
              ossId: val.ossId,
              name: val.originalName,
              url: val.url
            };
          });
        }
        const sysFileIds =sysFileIdsList.length!==0?sysFileIdsList:'';
        const data = {
          ...props.editData,
          sysFileIds
        };
        assignFields(data);
        isUpdate.value = true;
      } else {
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
