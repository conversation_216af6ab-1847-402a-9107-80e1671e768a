<template>
  <div>
    <ele-modal
      form
      :width="600"
      title="标记异常"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <ele-tabs
        v-model="active"
        :items="[
          { name: 'mark', label: '新增耗材包标记异常' },
          { name: 'marKed', label: '已标记异常耗材包' }
        ]"
      >
        <template #mark>
          <EleTableSearch
            :model="form1"
            :items="items1"
            filterRowToMore="1"
            :show-label="false"
            @updateValue="updateFormValue"
            @submit="onSearch"
            @reset="reset"
            style="margin-top: 10px"
          />
          <ele-pro-table
            row-key="consumablesLabelId"
            :columns="columns"
            :datasource="datasource"
            v-model:selections="selections"
            :pagination="false"
            border
            style="margin-bottom: 20px; max-height: 400px; overflow: auto"
          >
            <template #status="{ row }">
              {{ row.status }}
            </template>
          </ele-pro-table>
          <pro-form
            ref="formRef"
            :model="form"
            :items="items"
            :footer="false"
            :label-width="120"
            :grid="{ span: 12 }"
            @updateValue="setFieldValue"
          >
          </pro-form>
        </template>
        <template #marKed>
          <EleTableSearch
            :model="form1"
            :items="items1"
            filterRowToMore="1"
            :show-label="false"
            @updateValue="updateFormValue"
            @submit="onSearch"
            @reset="onSearch"
            style="margin-top: 10px"
          />
          <ele-pro-table
            row-key="userId"
            :columns="columns1"
            :datasource="datasource"
            :pagination="false"
            border
            style="margin-bottom: 20px; max-height: 300px; overflow: auto"
          />
        </template>
      </ele-tabs>
      <template #footer>
        <div v-if="active == 'mark'" style="text-align: right">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            确认标记异常
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import {
    hospitalMaterialSelectWaitList,
    hospitalMaterialMarkAbnormal
  } from '@/api/project/material-management/institutionalMaterial-inventory';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };
  const active = ref('mark');
  /** 表单数据 */
  const form1 = reactive({
    packageCode: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form1[prop] = value;
  };

  /** 表单项 */
  const items1 = ref([
    {
      type: 'input',
      label: '耗材包ID',
      prop: 'packageCode'
    }
  ]);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    remark: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '异常备注',
      prop: 'remark',
      type: 'textarea',
      required: true,
      colProps: {
        span: 24
      }
    }
  ]);
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材包ID',
      prop: 'packageCode',
      align: 'center'
    }
    // {
    //   label: '状态',
    //   prop: 'status',
    //   slot: 'slot',
    //   align: 'center'
    // }
  ]);
  const datasource = ref([]);
  const columns1 = ref([
    {
      label: '耗材包ID',
      prop: 'packageCode',
      align: 'center'
    },
    {
      label: '备注',
      prop: 'remark',
      align: 'center'
    }
  ]);
  /** 保存编辑 */
  const selections = ref([]);
  const formRef = ref(null);
  const loading = ref(false);
  const save = () => {
    if (selections.value.length == 0)
      return EleMessage.warning('请选择异常的耗材包');
    const list = selections.value.map((el) => el.consumablesLabelId);
    loading.value = true;
    const params = {
      remark: form.remark,
      consumablesLabelIdList: list
    };
    hospitalMaterialMarkAbnormal(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        resetFields();
        formRef.value?.clearValidate?.();
        selections.value = [];
        cancelDialog();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  const onSearch = () => {
    if (datasourceCopy.value.length == 0) return;
    if (form1.packageCode == '') {
      datasource.value = datasourceCopy.value;
    } else {
      datasource.value = datasourceCopy.value.filter((el) =>
        el.packageCode.includes(form1.packageCode)
      );
    }
  };
  const reset = () => {
    const status = active.value == 'mark' ? '0' : '3';
    getTableList(status);
  };

  const datasourceCopy = ref([]);
  const getTableList = async (status) => {
    const { data } = await hospitalMaterialSelectWaitList({
      prepareDetailsId: props.editData.prepareDetailsId,
      status: status
    });
    datasource.value = data ?? [];
    datasourceCopy.value = JSON.parse(JSON.stringify(datasource.value));
  };

  watch(
    active,
    (val) => {
      const status = val == 'mark' ? '0' : '3';
      getTableList(status);
    },
    { immediate: true }
  );
  watch(visible, (val) => {
    if (!val) return;
    const status = active.value == 'mark' ? '0' : '3';
    getTableList(status);
  });
</script>
