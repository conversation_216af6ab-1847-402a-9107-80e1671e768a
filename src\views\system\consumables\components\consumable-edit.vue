<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    v-model="visible"
    :title="isUpdate ? '修改耗材项' : '新增耗材项'"
    @open="handleOpen"
    align-center
  >
    <div class="form-container">
      <!-- {{ data.isReference == 1 }} -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="110px"
        @submit.prevent=""
      >
        <el-form-item label="耗材类型" prop="consumableType">
          <!-- 耗材类型（1代表采血管 2代表样本管 3代表样本盒 4代表标签 5代表其他） -->
          <dict-data
            code="consumable_type"
            v-model="form.consumableType"
            placeholder="请选择耗材类型"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="耗材名称" prop="consumableName">
          <el-input
            clearable
            v-model="form.consumableName"
            placeholder="请输入耗材名称"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="规格尺寸" prop="consumableSize">
          <el-input
            clearable
            v-model="form.consumableSize"
            placeholder="请输入规格尺寸"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="颜色材质" prop="consumableMaterial">
          <el-input
            clearable
            v-model="form.consumableMaterial"
            placeholder="请输入颜色材质"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="品牌" prop="consumableBrand">
          <el-input
            clearable
            v-model="form.consumableBrand"
            placeholder="请输入品牌"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="计数单位" prop="consumableCountingUnit">
          <el-input
            clearable
            v-model="form.consumableCountingUnit"
            placeholder="请输入计数单位"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="包装单位" prop="consumablePackagingUnit">
          <el-input
            clearable
            v-model="form.consumablePackagingUnit"
            placeholder="请输入包装单位"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="包装规格" prop="consumablePackagingSpecification">
          <el-input
            clearable
            v-model="form.consumablePackagingSpecification"
            placeholder="请输入包装规格"
            :disabled="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="采购货期/天数" prop="consumableDeliveryDay">
          <el-input-number
            :min="0"
            :max="99999"
            v-model="form.consumableDeliveryDay"
            placeholder="请输入采购货期/天数"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="库存提醒阈值" prop="consumableThreshold">
          <el-input-number
            :min="0"
            :max="99999"
            v-model="form.consumableThreshold"
            placeholder="请输入库存提醒阈值"
            controls-position="right"
            class="ele-fluid"
          />
        </el-form-item>
        <el-form-item label="耗材项照片" prop="sysFileIds">
          <!-- <fileUpload ref="fileUploadRef" v-model="url" /> -->
          <imageUpload
            ref="imageUploadRef"
            v-model="form.sysFileIds"
            :limit="1"
            :readonly="data.isReference == 1"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            :rows="4"
            type="textarea"
            v-model="form.remark"
            placeholder="请输入备注"
            :maxlength="200"
            show-word-limit
            :disabled="data.isReference == 1"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { addConsumable, editConsumable } from '@/api/system/consumable';
  // import fileUpload from '@/components/FileUpload/index.vue';
  import imageUpload from '@/components/ImageUpload/index.vue';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    consumableId: '',
    consumableName: '',
    consumableType: '',
    consumableSize: '',
    consumableMaterial: '',
    remark: '',
    consumableBrand: '',
    consumableCountingUnit: '',
    consumablePackagingUnit: '',
    consumablePackagingSpecification: '',
    consumableThreshold: '',
    consumableDeliveryDay: '',
    sysFileIds: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    consumableName: [
      {
        required: true,
        message: '请输入耗材名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableType: [
      {
        required: true,
        message: '请输入耗材类型',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableSize: [
      {
        required: true,
        message: '请输入规格尺寸',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableMaterial: [
      {
        required: true,
        message: '请输入颜色材质',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableBrand: [
      {
        required: true,
        message: '请输入品牌',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumablePackagingUnit: [
      {
        required: true,
        message: '请输入包装单位',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableCountingUnit: [
      {
        required: true,
        message: '请输入计数单位',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumablePackagingSpecification: [
      {
        required: true,
        message: '请输入包装规格',
        type: 'string',
        trigger: 'blur'
      }
    ],
    consumableThreshold: [
      {
        required: true,
        message: '请输入库存提醒阈值',
        type: 'number',
        trigger: ['blur', 'change']
      }
    ],
    consumableDeliveryDay: [
      {
        required: true,
        message: '请输入采购货期/天数',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const isEmptyObject =
        Object.prototype.toString.call(form.sysFileIds) === '[object Object]' &&
        Object.keys(form.sysFileIds).length === 0;
      const hasValue = Array.isArray(form.sysFileIds)
        ? form.sysFileIds.length > 0
        : !isEmptyObject;
      if (hasValue) {
        if (form.sysFileIds) {
          console.log(JSON.parse(form.sysFileIds));
          form.sysFileIds = JSON.parse(form.sysFileIds)?.ossId || '';
        }
      } else {
        form.sysFileIds = '';
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? editConsumable : addConsumable;
      saveOrUpdate(form)
        .then((msg) => {
          console.log(1111, msg);
          loading.value = false;
          EleMessage.success(msg);
          handleCancel();
          emit('done');
        })
        .catch((e) => {
          console.log(2222, e);
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    form.consumableThreshold = '';
    form.consumableDeliveryDay = '';
    if (props.data) {
      const data = {
        ...props.data,
        sysFileIds: props.data.sysFileList.map((val) => {
          return {
            ossId: val.ossId,
            name: val.originalName,
            url: val.url
          };
        })
      };
      console.log(data);
      assignFields(data);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  };
</script>
<style scoped lang="scss">
  .form-container {
    max-height: 600px;
    overflow-y: auto;
  }
</style>
