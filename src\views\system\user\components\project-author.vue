<template>
  <ele-modal
    :width="1100"
    title="新增项目授权"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    v-model="visible"
    align-center
  >
    <EleTableSearch
      :model="form"
      :items="items"
      filterRowToMore="1"
      :show-label="true"
      :label-width="100"
      @updateValue="updateFormValue"
      @submit="onSearch"
      @reset="onSearch"
      style="margin-bottom: 10px"
    >
      <!--  <template #toolbar>
       <ele-tooltip
          content="单个新增"
          type="warning"
          placement="top"
          bg="linear-gradient( 135deg, #43CBFF 10%, #9708CC 100%)"
          arrow-bg="#7556E0"
          :offset="8"
        >
          <el-button
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增
          </el-button>
        </ele-tooltip>
        <el-button plain @click="exportData()"> 导出 </el-button>
      </template> -->
    </EleTableSearch>
    <ele-pro-table
      ref="tableRef"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    >
      <template #roleId="{ row }">
        <el-select
          v-model="row.roleId"
          clearable
          @change="handleRoleChange(row)"
        >
          <el-option
            :label="item.label"
            :value="item.value"
            v-for="item in roleList"
            :key="item.value"
          />
        </el-select>
      </template>
      <template #duties="{ row }">
        <dict-data
          code="duties"
          v-model="row.duties"
          placeholder="请选择职责"
        />
      </template>

      <template #projectStatus="{ row }">
        <!-- 项目状态（1代表在研中 2代表暂停 3代表结束 4代表废弃 -->
        <el-tag v-if="row.projectStatus == 1" type="success">在研中</el-tag>
        <el-tag v-else-if="row.projectStatus == 2" type="info">暂停</el-tag>
        <el-tag v-else-if="row.projectStatus == 3" type="danger">结束</el-tag>
        <el-tag v-else-if="row.projectStatus == 4" type="warning">废弃</el-tag>
      </template>
    </ele-pro-table>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, nextTick, watch, reactive } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { getProjectList, addbatchProjectGrant } from '@/api/system/user';
  import { getProjectRoleList } from '@/api/project-configuration';
  const emit = defineEmits(['done']);
  const props = defineProps({
    /** 用户 */
    data: Object
  });
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '项目名称',
      prop: 'projectName'
    }
  ]);
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center'
    },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 60,
      align: 'center'
    },
    {
      prop: 'roleId',
      label: '授权项目角色',
      align: 'center',
      width: 150,
      slot: 'roleId'
    },
    {
      prop: 'duties',
      label: '项目职责',
      align: 'center',
      width: 150,
      slot: 'duties'
    },
    {
      prop: 'analyseProjectNumber',
      label: '分析项目编号',
      align: 'center',
      width: 150
    },
    {
      prop: 'schemeNumber',
      label: '试验方案编号',
      align: 'center',
      width: 150
    },
    {
      prop: 'projectName',
      label: '项目名称',
      align: 'left'
    },
    {
      prop: 'projectStatus',
      label: '项目状态',
      align: 'center',
      width: 150,
      slot: 'projectStatus'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);
  /** 项目角色列表 */
  const roleList = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 表单数据 */
  const form = reactive({
    consumableName: '',
    consumableBrand: '',
    consumableType: ''
  });

  const handleRoleChange = (row) => {
    roleList.value.forEach((item) => {
      if (item.value == row.roleId) {
        row.duties = item.label;
      }
    });
  };

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 搜索事件 */
  const onSearch = (where) => {
    console.log(where);
    Object.assign(lastWhere, where);
    query(where);
  };
  /** 保存编辑 */
  const save = () => {
    console.log(selections.value, props.data);
    if (selections.value.length == 0) {
      EleMessage.error('请先选择项目进行授权');
      return;
    }
    let dd = selections.value.filter((v) => !v.roleId);
    if (dd && dd.length > 0) {
      EleMessage.error('授权项目角色不能为空');
      return;
    }
    let dd2 = selections.value.filter((v) => !v.duties);
    if (dd2 && dd2.length > 0) {
      EleMessage.error('职责不能为空');
      return;
    }
    let params = selections.value.map((val) => {
      return {
        userId: props.data?.userId,
        projectId: val.projectId,
        roleId: val.roleId,
        duties: val.duties
      };
    });
    console.log(params);
    loading.value = true;
    addbatchProjectGrant({ addList: params })
      .then(() => {
        loading.value = false;
        EleMessage.success({ message: '授权成功', plain: true });
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error({ message: e.message, plain: true });
      });
  };

  /** 查询 */
  const query = (where) => {
    getProjectList({ userId: props.data.userId, isImpower: 0, ...where })
      .then((result) => {
        result.data.forEach((item) => {
          item.roleId = '';
          item.duties = '';
        });
        datasource.value = result.data;
        // tableRef.value?.setSelectedRows([]);
      })
      .catch((e) => {
        EleMessage.error({ message: e.message, plain: true });
      });
    getProjectRoleList(0).then((res) => {
      console.log(res);
      if (res.code === 200) {
        roleList.value = res.data.map((val) => {
          return {
            label: val.roleName,
            value: val.roleId,
            roleKey: val.roleKey
          };
        });
      }
    });
  };

  /** 监听弹窗打开 */
  watch(visible, () => {
    if (visible.value) {
      selections.value = [];
      if (props.data) {
        query();
      }
    }
  });
</script>
