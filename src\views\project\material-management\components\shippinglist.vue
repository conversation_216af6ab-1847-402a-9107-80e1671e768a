<template>
  <div>
    <div style="margin-top: 20px">
      <el-checkbox v-model="checkObj.package">
        <h3>耗材包发运</h3>
      </el-checkbox>
      <ele-pro-table
        row-key="userId"
        :pagination="false"
        :columns="columns"
        :datasource="datasource"
      >
      </ele-pro-table>
    </div>
    <div style="margin: 30px 0">
      <el-checkbox v-model="checkObj.bulkpackage">
        <h3>散装耗材发运</h3>
      </el-checkbox>
      <ele-pro-table
        row-key="userId"
        :pagination="false"
        :columns="columns1"
        :datasource="datasource1"
        :span-method="spanMethod"
      >
        <template #inventoryId="{ row }">
          <el-select
            clearable
            v-model="row.inventoryId"
            placeholder="请选择批号"
            class="ele-fluid"
            @focus="getBatch(row)"
          >
            <el-option
              v-for="(item, index) in row.batchNumberList"
              :value="item.inventoryId"
              :label="item.batchNumber"
              :key="index"
              :disabled="item.quantity == 0"
              @click="clickBatch(row, item)"
            >
              {{ item.batchNumber }} 库存数量：{{ item.quantity }}
            </el-option>
          </el-select>
        </template>
        <template #quantity="{ row }">
          <el-input-number
            v-model="row.quantity"
            :max="row.batchQuantity"
            :min="1"
          />
        </template>
        <template #action="{ row, $index }">
          <el-link
            v-if="showAdd(row, $index)"
            type="primary"
            underline="never"
            @click="addBatch(row, $index)"
            >增加批号</el-link
          >
          <el-link
            v-if="showDel(row, $index)"
            type="primary"
            underline="never"
            @click="remove"
            style="margin-left: 5px"
            >删除</el-link
          >
        </template>
      </ele-pro-table>
    </div>
    <div style="margin-bottom: 70px">
      <el-checkbox v-model="checkObj.reagent"> <h3>试剂发运</h3> </el-checkbox>
      <ele-pro-table
        row-key="userId"
        :pagination="false"
        :columns="columns2"
        :datasource="datasource2"
      >
        <template #batchNumber="{ row }">
          <el-input v-model="row.batchNumber" />
        </template>
        <template #quantity="{ row }">
          <el-input-number v-model="row.quantity" />
        </template>
        <template #endDate="{ row }">
          <el-date-picker
            v-model="row.endDate"
            clearable
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择有效期"
            style="width: 160px"
          />
        </template>
      </ele-pro-table>
    </div>
  </div>
</template>
<script setup>
  import { ref, watch } from 'vue';
  import {
    getMaterialForwardingAdd,
    selectHospitalList,
    selectHospitalApplyList,
    selectHospitalApplyDetailsList
  } from '@/api/project/material-management/material-shipment';
  import { getBatchNumberList } from '@/api/consumables-management/inventory-query';

  const props = defineProps({
    id: {
      type: String,
      default: ''
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const checkObj = ref({
    package: true,
    bulkpackage: true,
    reagent: true
  });

  // 耗材包信息列表配置
  const columns = ref([
    {
      label: '耗材包编号',
      prop: 'consumablesNumber'
    },
    {
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      label: '耗材包备注',
      prop: 'consumablesRemark'
    },
    {
      label: '申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '已发运数量',
      prop: 'forwardingQuantity'
    },
    {
      label: '本次发运数量',
      prop: 'quantity'
    }
  ]);
  // 耗材包信息数据
  const datasource = ref([]);

  // 散装耗材信息列表配置
  const columns1 = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '理论+备用申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '已发运数量',
      prop: 'forwardingQuantity'
    },
    {
      label: '批号',
      prop: 'inventoryId',
      slot: 'inventoryId',
      width: 200
    },
    {
      label: '本次发运数量',
      prop: 'quantity',
      slot: 'quantity',
      width: 200
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  // 散装耗材信息数据
  const datasource1 = ref([]);

  const batchNumberList = ref([]);
  const getBatch = async (row) => {
    row.batchNumberList = await getBatchNumberListFun(row.consumableId);
  };
  const clickBatch = (row, item) => {
    row.batchQuantity = item.quantity;
  };
  //获取批号列表
  const getBatchNumberListFun = async (consumableId) => {
    const res = await getBatchNumberList({
      consumableId: consumableId
    });
    let list = [];
    if (res?.data && res.data.length != 0) {
      list = res.data.map((el) => {
        return {
          ...el,
          batchQuantity: el.quantity
        };
      });
    }
    return list;
  };

  // 新增一行
  const addBatch = (row, index) => {
    datasource1.value.splice(index + 1, 0, { ...row });
  };
  // 删除一行
  const remove = (index) => {
    datasource1.value.splice(index, 1);
  };
  // 预处理数据，计算合并的行数
  const computeSpans = (prop) => {
    const spans = [];
    let pos = 0;

    for (let i = 0; i < datasource1.value.length; i++) {
      if (i === 0) {
        spans.push(1);
        pos = 0;
      } else {
        if (datasource1.value[i][prop] === datasource1.value[i - 1][prop]) {
          spans[pos] += 1;
          spans.push(0);
        } else {
          spans.push(1);
          pos = i;
        }
      }
    }
    return spans;
  };
  /** 合并表格单元格 */
  const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < 7) {
      const rowspan = computeSpans('consumableId')[rowIndex];
      return [rowspan, 1];
    }
    return [1, 1];
  };

  // 试剂信息列表配置
  const columns2 = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '试剂名称',
      prop: 'reagentName'
    },
    {
      label: '保存条件',
      prop: 'storageCondition'
    },
    {
      label: '备注',
      prop: 'remark'
    },
    {
      label: '申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '已发运数量',
      prop: 'forwardingQuantity'
    },

    {
      label: '本次发运数量',
      prop: 'quantity',
      slot: 'quantity',
      align: 'center',
      width: 200
    },
    {
      label: '批号',
      prop: 'batchNumber',
      slot: 'batchNumber',
      align: 'center'
    },
    {
      label: '有效期',
      prop: 'endDate',
      slot: 'endDate',
      align: 'center',
      width: 200
    }
  ]);
  //试剂信息数据
  const datasource2 = ref([]);

  const showAdd = (row, index) => {
    const list = datasource1.value.filter(
      (el) => el.consumableId == row.consumableId
    );
    if (list.length == 1) {
      return true;
    } else {
      let index1;
      datasource1.value.forEach((el, index2) => {
        if (el.consumableId == row.consumableId) {
          index1 = index2;
        }
      });
      return index1 == index;
    }
  };
  const showDel = (row, index) => {
    const list = datasource1.value.filter(
      (el) => el.consumableId == row.consumableId
    );
    return list.length !== 1;
  };

  const infoVo = ref({});
  const getSelectHospitalApplyDetailsList = async () => {
    const { data } = await selectHospitalApplyDetailsList({
      applyId: props.id
    });
    infoVo.value = data ?? {};
    datasource.value = data?.forwardingConsumablesInfoVoList || [];
    datasource1.value = data?.forwardingBulkConsumablesInfoVoList || [];
    datasource2.value = data?.forwardingBulkReagentInfoVoList || [];
  };
  watch(
    () => props.id,
    (val) => {
      if (!val) return;
      getSelectHospitalApplyDetailsList();
    },
    { immediate: true }
  );

  defineExpose({
    infoVo,
    checkObj
  });
</script>
