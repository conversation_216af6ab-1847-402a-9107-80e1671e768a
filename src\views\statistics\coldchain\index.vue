<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        v-model:selections="selections"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button @click="exportData" v-permission="'stats:lenglian:export'">
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        cache-key="inventory-query"
        :export-config="{ fileName: '库存列表', datasource: exportSource }"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <template #materialName="{ row }">
          <el-link type="primary" underline="never" @click="showMaterialItem">{{
            row.materialName
          }}</el-link>
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="handleEdit(row)"
            v-permission="'stats:lenglian:edit'"
          >
            编辑
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <materialItemList v-model="materialItemVisible" />
    <coldchain-edit
      ref="sampleEditRef"
      v-model="visibleModal"
      :data="editData"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getLenglianStatList,
    exportLenglianStat
  } from '@/api/statistics/coldchain';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import materialItemList from '../../project/handleModal/materialItemList.vue';
  import coldchainEdit from './coldchain-edit.vue';

  const { push } = useRouter();
  const { mobileDevice } = useMobileDevice();
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    username: '',
    organizationName: '',
    phone: '',
    email: '',
    createTime: ['', ''],
    sex: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    },
    {
      type: 'input',
      label: '公司项目编号',
      prop: 'companyProjectNumber'
    },
    {
      type: 'input',
      label: '运输单位',
      prop: 'logisticsCompany'
    },
    {
      type: 'date',
      label: '发运日期',
      prop: 'forwardingDate'
    },
    {
      type: 'input',
      label: '发件单位',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '运输单号',
      prop: 'bookingNote'
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 55,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'bookingNote',
        label: '运输单号',
        align: 'left',
        width: 120
      },
      {
        prop: 'logisticsCompany',
        label: '运输单位',
        align: 'left',
        width: 120
      },
      {
        prop: 'forwardingName',
        label: '下单人',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '发件日期',
        align: 'center',
        width: 150
      },
      {
        prop: 'hospitalName',
        label: '发件单位',
        align: 'left',
        width: 150
      },
      {
        prop: 'remark',
        label: '订单备注',
        align: 'left',
        width: 150
      },
      {
        prop: 'cases',
        label: '箱数',
        align: 'center'
      },
      {
        prop: 'specSize',
        label: '规格尺寸',
        align: 'center'
      },
      {
        prop: 'transportationConditions',
        label: '运输条件',
        align: 'center'
      },
      {
        prop: 'expense',
        label: '费用',
        align: 'center'
      },
      {
        prop: 'paymentDate',
        label: '付款日期',
        align: 'center',
        width: 120
      },
      {
        prop: 'invoicingDate',
        label: '开票日期',
        align: 'center',
        width: 120
      },
      {
        prop: 'expenseRemarks',
        label: '费用备注',
        align: 'center'
      },
      {
        prop: 'analyseProjectNumber',
        label: '分析项目编号',
        align: 'center',
        width: 150
      },
      {
        prop: 'companyProjectNumber',
        label: '公司项目编号',
        align: 'center',
        width: 150
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        align: 'center',
        width: 150
      },
      {
        prop: 'action',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 100,
        slot: 'action'
      }
    ];
  });

  const selections = ref([]);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getLenglianStatList({ ...where, ...orders, ...filters, ...pages });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 搜索事件 */
  const onSearch = (where) => {
    const [d1, d2] = where.createTime ?? [];
    const time = {
      createTimeStart: d1 ? `${d1} 00:00:00` : '',
      createTimeEnd: d2 ? `${d2} 23:59:59` : ''
    };
    Object.assign(lastWhere, where, time);
    doReload();
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 表格搜索 */
  const doReload = () => {
    reload(lastWhere);
  };

  const visibleModal = ref(false);
  const editData = ref({});
  const handleEdit = (row) => {
    visibleModal.value = true;
    editData.value = row;
  };

  const materialItemVisible = ref(false);
  const showMaterialItem = () => {
    materialItemVisible.value = true;
  };
  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where, orders, filters }) => {
      exportLenglianStat({ ...where, ...orders, ...filters })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
