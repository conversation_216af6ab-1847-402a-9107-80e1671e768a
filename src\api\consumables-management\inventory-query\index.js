import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

//列表
export async function getInventoryList(params) {
  const res = await request.get('/inventory/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//详情
export async function getInventoryDetails(consumableId) {
  const res = await request.get(`/inventory/${consumableId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 盘库
export async function inventoryCkeck(data) {
  const res = await request.post('/inventory/check', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

//批号列表
export async function getBatchNumberList(params) {
  const res = await request.get(`/inventory/selectBatchNumberByConsumableId`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出
export async function exportInventory(params) {
  const res = await request({
    url: '/inventory/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `库存查询_${Date.now()}.xlsx`);
}
