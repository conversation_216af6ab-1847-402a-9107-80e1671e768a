import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
// CRC-物资申请列表
export async function getMaterialApplyList(params) {
  const res = await request.get('/materialApply/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 新增物资申请

export async function materialApplyAdd(data) {
  const res = await request.post('/materialApply/add', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 撤销物资申请
export async function materialApplyRevoke(params) {
  const res = await request.get('/materialApply/revoke', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 获取物资申请详细信息
export async function materialApplyDetails(applyId) {
  const res = await request.get(`/materialApply/${applyId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//新建申请单-研究机构下拉框 /materialApply/selectHospitalList
export async function selectHospitalList(params) {
  const res = await request.get(`/materialApply/selectHospitalList`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出
export async function exportMaterialApply(params) {
  const res = await request({
    url: '/materialApply/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资申请_${Date.now()}.xlsx`);
}
