<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <div class="top-menu">
        <el-radio-group
          v-model="tabPosition"
          style="margin-bottom: 30px"
          @change="changeTab"
        >
          <el-radio-button value=""
            >全部{{ totalCount ? '(' + totalCount + ')' : '' }}</el-radio-button
          >
          <el-radio-button
            :value="c.value"
            v-for="c in fileTypeDict"
            :key="c.value"
            >{{ c.label
            }}{{ c.number ? '(' + c.number + ')' : '' }}</el-radio-button
          >
          <!-- <el-radio-button value="2">试剂盒更新(2)</el-radio-button>
          <el-radio-button value="3">检测报告单(2)</el-radio-button>
          <el-radio-button value="4">实验小结(2)</el-radio-button>
          <el-radio-button value="5">实验室资质文件(2)</el-radio-button>
          <el-radio-button value="6">其他(2)</el-radio-button> -->
        </el-radio-group>
        <el-button
          type="primary"
          @click="openUpload()"
          v-permission="'project:file:add'"
        >
          上传文件
        </el-button>
      </div>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '基础列表数据', datasource: exportSource }"
        :print-config="{ datasource: exportSource }"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="normal-table"
        :border="tableBorder"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :default-sort="{ prop: 'createTime', order: 'ascending' }"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 25px; margin-top: -25px"
        @done="onDone"
        :pagination="false"
      >
        <template #fileType="{ row }">
          <dict-data code="file_type" type="text" :model-value="row.fileType" />
        </template>
        <template #fileName="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="handleDownload(row)"
          >
            {{ row.fileName }}
          </el-link>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row)"
            v-permission="'project:file:rename'"
          >
            重命名
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'project:file:remove'"
          />
          <el-link
            type="danger"
            underline="never"
            @click.stop="remove(row)"
            v-permission="'project:file:remove'"
          >
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 上传文件弹窗 -->
    <el-dialog
      title="上传文件"
      v-model="uploadVisible"
      :before-close="handleClose"
      width="680px"
      append-to-body
      destroy-on-close
    >
      <div class="upload-form">
        <span style="color: red; margin-right: 3px">*</span> 文件类型
        <dict-data
          style="flex: 1; margin-left: 12px"
          code="file_type"
          v-model="form.fileType"
          placeholder="请选择文件类型"
        />
      </div>
      <div class="upload-form">
        <span style="color: red; margin-right: 3px">*</span> 选择文件
        <FileUpload
          style="flex: 1; margin-left: 12px"
          ref="fileUploadRef"
          v-model="form.sysFileIds"
          :multiple="true"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit"
            >确定上传</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="编辑文件名称"
      v-model="editVisible"
      :before-close="handleClose2"
      width="680px"
      append-to-body
      destroy-on-close
    >
      <div class="upload-form">
        文件名称
        <el-input
          style="flex: 1; margin-left: 20px"
          v-model="fileName"
          clearable
          maxlength="50"
          show-word-limit
          placeholder="请输入文件名称"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose2">取消</el-button>
          <el-button :loading="loading" type="primary" @click="submit2"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { getProjectFileList } from '@/api/project-configuration';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import FileUpload from '@/components/FileUpload/index.vue';
  import {
    addProjectFile,
    removeProjectFile,
    renameProjectFile
  } from '@/api/project-configuration';
  import { useDictData } from '@/utils/use-dict-data';
  import { useUserStore } from '@/store/modules/user';
  import download from '@/utils/download';
  import { Base64 } from 'js-base64';

  const userStore = useUserStore();
  const [fileTypeDicts] = useDictData(['file_type']);
  const fileTypeDict = ref([]);
  const dataList = ref([]);
  watch(fileTypeDicts, (newVal) => {
    fileTypeDict.value = newVal.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue,
      number: ''
    }));
  });
  const { push } = useRouter();
  const { mobileDevice } = useMobileDevice();
  /** 表格实例 */
  const tableRef = ref(null);
  const uploadVisible = ref(false);
  /** 表单数据 */
  const form = reactive({
    fileId: '',
    fileType: '',
    projectId: '',
    sysFileIds: [],
    fileName: ''
  });
  const editVisible = ref(false);
  const fileName = ref('');
  const fileId = ref('');
  const tabPosition = ref('');
  const totalCount = ref(0);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: '序号',
        width: 80,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'fileName',
        label: '文件名称',
        slot: 'fileName'
      },
      {
        prop: 'fileType',
        label: '类型',
        width: 140,
        slot: 'fileType'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 160,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格单选选中数据 */
  const current = ref(null);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const changeTab = (val) => {
    tabPosition.value = val;
    console.log('val', val);
    form.fileType = val;
    reload({
      fileType: val
    });
  };
  const handleClose = () => {
    uploadVisible.value = false;
  };
  const handleClose2 = () => {
    editVisible.value = false;
  };

  /** 表格数据源 */
  const datasource = async ({ pages, where }) => {
    const response = await getProjectFileList({
      ...where,
      ...pages,
      orderBy: '',
      isAsc: 'asc'
    });
    if (where && !where.fileType) {
      totalCount.value = 0;
      dataList.value = response;
      const [fileTypeDicts] = useDictData(['file_type']);
      fileTypeDict.value = fileTypeDicts.value.map((item) => ({
        label: item.dictLabel,
        value: item.dictValue,
        number: ''
      }));
      console.log('response', response, fileTypeDicts.value);
      if (dataList.value.length && fileTypeDict.value.length) {
        // 统计各类型文件数量
        fileTypeDict.value.forEach((type) => {
          const count = dataList.value.filter(
            (file) => file.fileType === type.value
          ).length;
          type.number = count;
        });
        // 统计总数量
        totalCount.value = dataList.value.length;
      }
      if (tabPosition.value) {
        changeTab(tabPosition.value);
      }
    }
    return response;
  };
  /** 表格数据请求完成事件 */
  const onDone = () => {
    // 回显 id 为 45、47、48 的数据的复选框
    nextTick(() => {
      const ids = [45, 47, 48];
      tableRef.value?.setSelectedRowKeys?.(ids);
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  const fileUploadRef = ref();
  const openUpload = () => {
    uploadVisible.value = true;
    form.sysFileIds = [];
    fileUploadRef.value.clearFiles();
    fileUploadRef.value?.clearData();
  };
  /** 编辑 */
  const openEdit = (row) => {
    fileName.value = row.fileName;
    fileId.value = row.fileId;
    editVisible.value = true;
  };
  const handleDownload = (row) => {
    // download.oss(row.sysFileIds); // 下载文件
    // var url = `${import.meta.env.VITE_APP_BASE_USERIMG}${row.fileUri}`; //要预览文件的访问地址
    var url = row.sysFileList[0].url;
    window.open(
      'https://kkfile.520gcp.com/onlinePreview?url=' +
        encodeURIComponent(Base64.encode(url))
    );
  };
  /** 删除 */
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该数据吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        removeProjectFile(row.fileId).then((res) => {
          EleMessage.success('删除成功');
          reload({
            fileType: ''
          });
        });
      })
      .catch(() => {});
  };
  const submit = () => {
    let params = {
      projectId: userStore.projectId || 16,
      fileType: form.fileType,
      addList: []
    };
    if (!form.fileType) {
      return EleMessage.error('请先选择文件类型');
    }
    if (form.sysFileIds && form.sysFileIds.length == 0) {
      return EleMessage.error('请先上传文件');
    }
    let arry = JSON.parse(form.sysFileIds) || [];
    if (arry && arry.length > 0) {
      params.addList = arry.map((val) => {
        return {
          sysFileIds: val.ossId,
          fileName: val.name
        };
      });
    }
    console.log('提交参数===>', params);
    addProjectFile(params).then((res) => {
      EleMessage.success('上传成功');
      handleClose();
      reload({
        fileType: ''
      });
    });
  };
  const submit2 = () => {
    renameProjectFile({
      fileId: fileId.value,
      fileName: fileName.value
    }).then((res) => {
      EleMessage.success('操作成功');
      handleClose2();
      reload({
        fileType: ''
      });
    });
  };
</script>
<style lang="scss" scoped>
  .top-menu {
    display: flex;
    justify-content: space-between;
  }
  .upload-form {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
</style>
