<template>
  <div>
    <ele-modal
      form
      :width="600"
      :title="
        handleType === 1
          ? '新建分组'
          : handleType === 2
            ? '编辑分组'
            : '新增受试者数量'
      "
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <ele-pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="150"
        :grid="{ span: 20 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </ele-pro-form>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, watch, inject, computed } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    editSampleGroup,
    addSampleGroup,
    addSubjectNum
  } from '@/api/project/project-configuration/experiment-design';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const projectInfo = inject('projectInfo');

  // 使用computed来响应数据变化，提供默认值
  const projectId = computed(() => projectInfo?.projectId);
  const designType = computed(() => projectInfo?.designType || '1');

  const groupId = ref(null);

  const handleType = ref(null);
  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  const emit = defineEmits(['refresh']);

  const rules = {};

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    groupId: null,
    projectId: null,
    groupName: null,
    subjectNum: null,
    filterNumberPrefix: null,
    filterNumberStart: null,
    filterNumberLength: null
  });

  /** 基本信息表单项 */
  // vIf 为false时，不显示
  const items = computed(() => [
    {
      label: '组别名称',
      prop: 'groupName',
      type: 'input',
      required: true,
      vIf: handleType.value !== 3,
      props: {
        maxlength: 10,
        showWordLimit: true
      }
    },
    {
      label: '受试者数量',
      prop: 'subjectNum',
      type: 'inputNumber',
      required: true,
      props: {
        min: 1,
        max: 9999
      },
      vIf: designType.value !== '3' || handleType.value === 3
    },
    {
      label: '受试者编号前缀',
      prop: 'filterNumberPrefix',
      type: 'input',
      required: true,
      vIf: designType.value !== '3' && handleType.value !== 3,
      props: {
        maxlength: 5,
        showWordLimit: true
      }
    },
    {
      label: '受试者编号长度',
      prop: 'filterNumberLength',
      type: 'inputNumber',
      props: {
        min: 1,
        max: 5
      },
      required: true,
      vIf: designType.value !== '3' && handleType.value !== 3
    },
    {
      label: '受试者编号起始数字',
      prop: 'filterNumberStart',
      type: 'inputNumber',
      required: true,
      vIf: designType.value !== '3' && handleType.value !== 3,
      props: {
        min: 0,
        max: 9999
      }
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate =
        handleType.value === 1
          ? addSampleGroup
          : handleType.value === 2
            ? editSampleGroup
            : addSubjectNum;
      if (saveOrUpdate === addSubjectNum) {
        form.addNum = form.subjectNum;
      }
      saveOrUpdate({
        ...form,
        projectId: projectId.value,
        groupId: form.groupId || groupId.value
      })
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  const onClose = () => {
    visible.value = false;
    emit('refresh');
    resetFields();
    setTimeout(() => {
      formRef.value?.clearValidate?.();
    }, 0);
  };

  watch(
    () => props.editData,
    () => {
      if (props.editData) {
        // 添加受试者
        if (props.editData.handleType === 3) {
          handleType.value = 3;
          groupId.value = props.editData.groupId;
        } else {
          assignFields({
            ...props.editData
          });
          handleType.value = 2;
        }
      } else {
        handleType.value = 1;
        resetFields();
        setTimeout(() => {
          formRef.value?.clearValidate?.();
        }, 0);
      }
    },
    { immediate: true }
  );
</script>
