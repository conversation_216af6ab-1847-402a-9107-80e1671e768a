<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '100px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="基础信息"
    bordered
  >
    <template #extra>
      <el-link v-if="false" type="primary" plain>申请审核中</el-link>
    </template>
    <el-descriptions>
      <el-descriptions-item label="发运日期：">{{
        data.forwardingDate
      }}</el-descriptions-item>
      <el-descriptions-item label="运输单位：">{{
        data.logisticsCompany
      }}</el-descriptions-item>
      <el-descriptions-item label="物流单号：">{{
        data.bookingNote
      }}</el-descriptions-item>
      <el-descriptions-item label="发运人：">{{
        data.shipperName
      }}</el-descriptions-item>
      <el-descriptions-item label="接收机构：">{{
        data.hospitalName
      }}</el-descriptions-item>
      <el-descriptions-item label="备注：">{{
        data.remark
      }}</el-descriptions-item>
    </el-descriptions>
  </ele-card>
</template>
<script setup>
  import { ref } from 'vue';
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
</script>
<style scoped lang="scss">
  :deep .el-descriptions__label {
    margin-right: 5px !important;
  }
</style>
