<template>
  <ele-modal
    title="详情"
    :width="720"
    :body-style="{ paddingTop: '6px' }"
    v-model="visible"
  >
    <el-descriptions
      :border="true"
      :column="mobile ? 1 : 3"
      class="detail-table"
    >
      <el-descriptions-item label="系统模块">
        <div>
          <span>{{ data.objectNameText }}</span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="操作类型">
        <div>{{ data.operationNameText }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作者">
        <div>{{ data.operatorName }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作Ip">
        <div>{{ data.operationIp }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作地点">
        <div>{{ data.operationLocation }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作时间">
        <div>{{ data.operationTime }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作原因" :span="3">
        <div>{{ data.reason }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="操作内容" :span="3">
        <div
          v-for="(s, sIndex) in data.operationContent.split('\n')"
          :key="sIndex"
          style="color: #000; line-height: 30px"
        >
          <span>{{ getText(s) ? getText(s) + '，' : '' }}</span>
          <span style="color: #999" v-if="getText2(s)">{{ getText2(s) }}</span>
          <span style="color: #999" v-if="getText3(s)">，</span>
          <span style="color: #507aff" v-if="getText3(s)">{{
            getText3(s)
          }}</span>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>

<script setup>
  import { reactive } from 'vue';
  import { useMobile } from '@/utils/use-mobile';

  defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 文字省略组件的提示组件的属性 */
  const ellipsisTooltipProps = reactive({
    popperStyle: {
      width: '580px',
      maxWidth: '90%',
      wordBreak: 'break-all'
    },
    bodyStyle: {
      maxWidth: 'calc(100vw - 32px)',
      maxHeight: '252px',
      overflowY: 'auto',
      '--ele-scrollbar-color': '#5e5e5e',
      '--ele-scrollbar-hover-color': '#707070',
      '--ele-scrollbar-size': '8px'
    },
    offset: 4,
    placement: 'top'
  });

  /** 是否是移动端 */
  const { mobile } = useMobile();

  const getText = (str) => {
    const parts = str.split('，');
    return parts[0];
  };
  const getText2 = (str) => {
    const parts = str.split('，');
    return parts[1];
  };
  const getText3 = (str) => {
    const parts = str.split('，');
    return parts[2];
  };
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 88px;
    text-align: right;
    font-weight: normal;
  }
</style>
