<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'material:apply:auditExport'"
            @click="exportFun"
            plain
            >导出</el-button
          >
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="applyId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="material-audit"
        :loadOnCreated="false"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <template #auditStatus="{ row }">
          <el-tag :type="statusMap[row.auditStatus]">{{
            statusObj[row.auditStatus]
          }}</el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'material:apply:query'"
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            详情
          </el-link>
          <el-divider
            v-permission="['material:apply:query', 'material:apply:audit']"
            v-if="row.auditStatus == '0'"
            direction="vertical"
          />
          <el-link
            v-permission="'material:apply:audit'"
            v-if="row.auditStatus == '0'"
            type="primary"
            underline="never"
            @click.stop="openAudit(row)"
          >
            审核
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, unref, watch, onMounted } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import {
    getMaterialApplyAuditList,
    exportMaterialApplyAudit
  } from '@/api/project/material-management/material-audit';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'MaterialAudit' });

  const { currentRoute, push } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    hospitalName: '',
    applyNumber: '',
    hospitalCode: '',
    auditStatus: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  const statusObj = {
    0: '待审核',
    1: '已通过',
    2: '已撤销'
  };

  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    },
    {
      type: 'input',
      label: '申请编号',
      prop: 'applyNumber'
    },
    {
      type: 'select',
      prop: 'auditStatus',
      label: '申请状态',
      width: 100,
      options: Object.keys(statusObj).map((key) => ({
        label: statusObj[key],
        value: key
      }))
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'auditStatus',
        label: '申请状态',
        slot: 'auditStatus',
        width: 100,
        align: 'center'
      },
      {
        prop: 'applyNumber',
        label: '申请编号',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '研究机构',
        align: 'center'
      },
      {
        prop: 'createName',
        label: '申请人',
        align: 'center'
      },
      {
        prop: 'createTime',
        label: '申请日期',
        align: 'center'
      },
      {
        prop: 'recipientName',
        label: '收件人姓名',
        align: 'center'
      },
      {
        prop: 'deliverDate',
        label: '期望送达日期',
        align: 'center'
      },
      {
        prop: 'remark',
        label: '申请备注',
        align: 'center'
      },
      {
        prop: 'auditName',
        label: '审核人',
        align: 'center'
      },
      {
        prop: 'auditTime',
        label: '审核时间',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 150,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getMaterialApplyAuditList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  const openDetail = (row) => {
    push({
      path: '/project/material-management/material-application/details',
      query: {
        id: row?.applyId,
        isEdit: 'details'
      }
    });
  };
  //审核
  const openAudit = (row) => {
    push({
      path: '/project/material-management/material-application/details',
      query: {
        id: row?.applyId,
        actionType: 'audit',
        isEdit: 'edit'
      }
    });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportMaterialApplyAudit({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
  watch(
    currentRoute,
    (route) => {
      const { query } = unref(route);
      const isReload = query?.isReload || '';
      if (!isReload) return;
      reload();
    },
    { immediate: true }
  );

  onMounted(() => {
    const { query } = unref(currentRoute);
    if (query.auditStatus) {
      form.auditStatus = query.auditStatus;
    }
    reload(form);
  });
</script>
