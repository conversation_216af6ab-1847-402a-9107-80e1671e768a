<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="540"
    title="修改密码"
    :append-to-body="true"
    :show-close="true"
    :close-on-click-modal="false"
    :model-value="modelValue"
    @closed="onCancel"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          show-password
          type="password"
          autocomplete="new-password"
          :maxlength="16"
          v-model="form.oldPassword"
          placeholder="请输入旧密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          show-password
          type="password"
          autocomplete="new-password"
          :maxlength="16"
          v-model="form.newPassword"
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          show-password
          type="password"
          autocomplete="new-password"
          :maxlength="16"
          v-model="form.confirmPassword"
          placeholder="请再次输入新密码"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <!-- <el-button @click="updateModelValue(false)">取 消</el-button> -->
      <div class="dialog-footer flexCenter">
        <el-button
          type="primary"
          style="height: 38px; font-size: 16px"
          :loading="loading"
          @click="onOk"
        >
          确 定
        </el-button>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  // import { updateUserPwd } from '@/api/system/user';
  // import { encrypt } from '@/utils/jsencrypt';
  import { updatePassword } from '@/api/layout';

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    modelValue: Boolean
  });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const { form, resetFields } = useFormData({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    oldPassword: [
      { required: true, message: '旧密码不能为空', trigger: 'change' }
    ],
    newPassword: [
      { required: true, message: '新密码不能为空', trigger: 'change' },
      {
        required: true,
        validator: (rule, value, callback) => {
          const reg =
            /(?=.*[A-Za-z])(?=.*[\d\W])|(?=.*\d)(?=.*[A-Za-z\W])|(?=.*[\d\W])(?=.*[A-Za-z]).{6,}$/;
          if (reg.test(value)) {
            callback();
          } else {
            return callback(
              new Error(
                '密码必须包含字母、数字、符号中的任意2种，不得少于6位数'
              )
            );
          }
        },
        trigger: 'change'
      }
    ],
    confirmPassword: [
      { required: true, message: '确认密码不能为空', trigger: 'change' },
      {
        required: true,
        validator: (rule, value, callback) => {
          if (form.newPassword !== value) {
            callback(new Error('两次输入的密码不一致'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ]
  });
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 保存修改 */
  const onOk = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      let params = {
        oldPassword: form.oldPassword,
        newPassword: form.newPassword
      };
      loading.value = true;
      updatePassword(params)
        .then((res) => {
          loading.value = false;
          updateModelValue(false);
          EleMessage.success({ message: res, plain: true });
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error({ message: e.message, plain: true });
        });
      // updateUserPwd(form)
      //   .then((msg) => {
      //     loading.value = false;
      //     EleMessage.success(msg);
      //     updateModelValue(false);
      //   })
      //   .catch((e) => {
      //     loading.value = false;
      //     EleMessage.error(e);
      //   });
    });
  };

  /** 关闭回调 */
  const onCancel = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };
</script>
