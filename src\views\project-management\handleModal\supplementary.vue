<template>
  <div>
    <ele-modal
      form
      :width="600"
      title="补充样本信息"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="onClose">取消</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    }
  });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    userId: void 0,
    username: '',
    nickname: '',
    sex: void 0,
    roles: [],
    email: '',
    phone: '',
    password: '',
    introduction: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '是否注册申报：',
      prop: 'username',
      type: 'input'
    },
    {
      label: '样本计费日期：',
      prop: 'email',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' },
      required: true
    },
    {
      label: '实际处理日期：',
      prop: 'email',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' },
      required: true
    },
    {
      label: '样本状态',
      prop: 'nickname',
      type: 'select',
      options: [
        { label: '保存中', value: '0' },
        { label: '已销毁', value: '0' },
        { label: '客户取回', value: '0' }
      ]
    },
    {
      label: '付款方：',
      prop: 'nickname',
      type: 'input'
    },
    {
      label: '客户确认日期：',
      prop: 'email',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD' },
      required: true
    },
    {
      label: '费用结算状态',
      prop: 'nickname',
      type: 'select',
      options: [
        { label: '待结算', value: '0' },
        { label: '已结算', value: '1' },
        { label: '无费用', value: '2' },
        { label: '已完结', value: '3' }
      ]
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      //   const saveOrUpdate = isUpdate.value ? updateUser : addUser;
      //   saveOrUpdate(form)
      //     .then((msg) => {
      //       loading.value = false;
      //       EleMessage.success(msg);
      //       onClose();
      //     })
      //     .catch((e) => {
      //       loading.value = false;
      //       EleMessage.error(e.message);
      //     });
    });
  };
</script>
