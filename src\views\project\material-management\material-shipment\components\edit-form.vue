<!-- 添加和修改的表单 -->
<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '180px' : void 0,
      marginBottom: fixedHeight ? '60px' : void 0,
      maxHeight: '80vh',
      overflow: 'auto'
    }"
    header="基本信息"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :footer="false"
      :label-width="120"
      :grid="{ span: 8 }"
      @updateValue="setFieldValue"
    >
    </pro-form>
    <el-divider />
    <ele-tabs ref="tabRef" v-model="active" :items="applyList1"> </ele-tabs>
    <shippinglist
      ref="shipRef"
      v-for="key in applyList1"
      :key="key.name"
      v-show="active == key.value"
      :id="key.value"
    />
    <el-empty v-if="form.applyList.length == 0" description="暂无数据" />
  </ele-card>
  <div class="footer">
    <el-button @click="onClose">取消</el-button>
    <el-button type="primary" :loading="loading" @click="save">
      保存
    </el-button>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs';
  import ProForm from '@/components/ProForm/index.vue';

  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import shippinglist from '../../components/shippinglist.vue';
  import {
    getMaterialForwardingAdd,
    selectHospitalList,
    selectHospitalApplyList,
    selectHospitalApplyDetailsList
  } from '@/api/project/material-management/material-shipment';
  import { getBatchNumberList } from '@/api/consumables-management/inventory-query';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });

  const emit = defineEmits(['close', 'success']);

  const fixedHeight = ref(true);
  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    forwardingDate: dayjs(new Date()).format('YYYY-MM-DD'),
    logisticsCompany: '',
    bookingNote: '',
    shipperName: userStore.info.nickName,
    hospitalId: '',
    remark: '',
    applyList: []
  });

  /** 表单项 */
  const items = ref([
    {
      label: '发运日期',
      prop: 'forwardingDate',
      type: 'date',
      required: true
    },
    {
      label: '运输单位',
      prop: 'logisticsCompany',
      type: 'dictSelect',
      props: { code: 'logistics_company' },
      required: true
    },
    {
      label: '运输单号',
      prop: 'bookingNote',
      type: 'input',
      required: true
    },
    {
      label: '发运人',
      prop: 'shipperName',
      type: 'input',
      required: true
    },
    {
      label: '接收机构',
      prop: 'hospitalId',
      type: 'select',
      required: true,
      options: []
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      colProps: {
        span: 24
      }
    },
    {
      label: '选择发运的申请编号',
      prop: 'applyList',
      type: 'multipleSelect',
      options: [],
      required: true,
      itemProps: {
        labelWidth: '150px'
      },
      colProps: {
        span: 12
      }
    }
  ]);

  const active = ref();

  const hospitalList = ref([]);
  const getSelectHospitalList = async () => {
    const res = await selectHospitalList();
    if (res.data.length !== 0) {
      hospitalList.value = res.data.map((el) => {
        return {
          label: el.hospitalName,
          value: el.hospitalId,
          ...el
        };
      });
    }
    setPropItem('hospitalId', hospitalList.value);
  };
  const applyList = ref([]);
  const getSelectHospitalApplyList = async () => {
    const { data } = await selectHospitalApplyList({
      hospitalId: form.hospitalId
    });
    applyList.value = data.map((el) => {
      return {
        label: el.applyNumber,
        value: el.applyId
      };
    });
    if (applyList.value.length == 0) return;
    setPropItem('applyList', applyList.value);
  };
  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };
  const shipRef = ref(null);
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }

      const params = {
        ...form,
        projectId: userStore.projectId,
        applyAddBoList: []
      };

      for (let i = 0; i < shipRef.value.length; i++) {
        const obj = shipRef.value[i].infoVo;
        const id = applyList1.value[i].value;
        const checkObj = shipRef.value[i].checkObj;
        params.applyAddBoList.push({
          applyId: id,
          projectId: userStore.projectId,
          forwardingConsumablesAddBoList: checkObj.package
            ? obj.forwardingConsumablesInfoVoList
            : [],
          forwardingBulkConsumablesAddBoList: checkObj.bulkpackage
            ? obj.forwardingBulkConsumablesInfoVoList
            : [],
          forwardingBulkReagentAddBoList: checkObj.reagent
            ? obj.forwardingBulkReagentInfoVoList
            : []
        });
        const list = obj.forwardingBulkReagentInfoVoList.filter(
          (el) => !el.batchNumber || !el.quantity || !el.endDate
        );
        if (
          checkObj.reagent &&
          obj.forwardingBulkReagentInfoVoList.length !== 0 &&
          list.length !== 0
        )
          return EleMessage.warning('试剂的本次发运数量，批号和有效期为必填项');
      }
      loading.value = true;
      getMaterialForwardingAdd(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success('操作成功！');
          emit('success');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    emit('close');
  };
  watch(
    () => props.data,
    () => {
      getSelectHospitalList();
    },
    { immediate: true }
  );
  watch(
    () => form.hospitalId,
    (val) => {
      applyList1.value = [];
      form.applyList = [];
      active.value = '';
      setPropItem('applyList', []);
      if (!val) return;
      getSelectHospitalApplyList();
    }
  );
  const applyList1 = ref([]);
  watch(
    () => form.applyList,
    (val) => {
      if (val.length == 0) {
        applyList1.value = [];
        active.value = '';
      } else {
        applyList1.value = form.applyList.map((el) => {
          const obj = applyList.value.filter((item) => item.value == el)[0];
          return {
            ...obj,
            name: obj.value
          };
        });
        active.value = val[0];
      }
    },
    {
      deep: true
    }
  );
</script>
<style lang="scss" scoped>
  .footer {
    position: absolute;
    bottom: 20px;
    right: 30px;
    z-index: 1000;
    background: #fff;
  }
</style>
