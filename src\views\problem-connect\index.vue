<template>
  <ele-page flex-table hide-footer>
    <ele-card flex-table :body-style="{ paddingBottom: '4px' }">
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button
            type="primary"
            :icon="PlusOutlined"
            class="ele-btn-icon"
            @click="openAdd()"
            v-permission="'project:problem:add'"
          >
            新增
          </el-button>
          <el-button
            plain
            @click="openExport()"
            v-permission="'project:problem:export'"
          >
            导出
          </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :pagination="false"
        :loadOnCreated="false"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        cache-key="problem-connect"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
      >
        <template #problemType="{ row }">
          <dict-data
            code="problem_type"
            type="tag"
            :model-value="row.problemType"
          />
        </template>
        <template #problemStatus="{ row }">
          <el-tag
            :type="
              row.problemStatus == 0
                ? 'warning'
                : row.problemStatus == 1
                  ? 'info'
                  : ''
            "
          >
            {{
              row.problemStatus == 0
                ? '待关闭 '
                : row.problemStatus == 1
                  ? '已关闭'
                  : ''
            }}
          </el-tag>
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <!-- <el-link
            type="primary"
            underline="never"
            @click.stop="openDetail(row)"
          >
            查看
          </el-link>
          <el-divider direction="vertical" /> -->
          <el-link type="primary" underline="never" @click.stop="openSee(row)">
            详情
          </el-link>
          <!-- <el-divider direction="vertical" />
          <el-link type="danger" underline="never" @click.stop="remove(row)">
            删除
          </el-link> -->
        </template>
        <!-- 用户名表头 -->
        <template #nicknameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <problem-edit ref="problemEditRef" v-model="visibleModal" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, nextTick, onMounted, unref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import { getProblemList, exportProblemList } from '@/api/problem';
  import { useRouter } from 'vue-router';
  import problemEdit from './problem-edit.vue';

  const { push, currentRoute } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    problemstatus: '',
    overTimeStatus: []
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
    if (prop === 'overTimeStatus') {
      reload(form);
    }
  };

  // 问题状态（0代表待关闭 1代表已关闭）
  const problemStatusMap = {
    0: { label: '未解决', value: '0', color: 'danger' },
    1: { label: '已解决', value: '1', color: 'success' }
  };

  // 超期状态（0代表未超期 1代表已超期）
  const overTimeStatusMap = {
    1: { label: '已超期', value: '1', color: 'danger' }
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'select',
      label: '问题状态',
      prop: 'problemStatus',
      options: Object.keys(problemStatusMap).map((key) => problemStatusMap[key])
    },
    {
      type: 'input',
      label: '问题描述',
      prop: 'problemDescribe',
      props: {
        labelWidth: '120px'
      }
    },
    {
      type: 'input',
      label: '创建人',
      prop: 'createBy'
    },
    {
      type: 'checkbox',
      label: '超期状态',
      prop: 'overTimeStatus',
      options: Object.keys(overTimeStatusMap).map(
        (key) => overTimeStatusMap[key]
      )
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'problemStatus',
        label: '状态',
        width: 110,
        slot: 'problemStatus'
      },
      {
        prop: 'problemDescribe',
        label: '问题描述',
        minWidth: 110
      },
      {
        prop: 'sourceNumber',
        label: '发运/运输编号',
        minWidth: 110
      },
      {
        prop: 'problemType',
        label: '问题类型',
        width: 110,
        slot: 'problemType'
      },
      {
        prop: 'answerRoleName',
        label: '回答角色',
        width: 110
      },
      {
        prop: 'closeByName',
        label: '关闭人',
        width: 110
      },
      {
        prop: 'closeTime',
        label: '关闭时间',
        width: 110,
        align: 'center'
      },
      {
        prop: 'closeRemark',
        label: '关闭备注',
        minWidth: 110
      },
      {
        prop: 'createByName',
        label: '创建人',
        width: 110
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 110,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        minWidth: 100,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);

  const editData = ref({});

  const handle = ref('add');

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return getProblemList({
      ...where,
      ...pages,
      orderBy: '',
      isAsc: 'asc',
      overTimeStatus: form.overTimeStatus.length ? form.overTimeStatus[0] : ''
    });
  };

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(where);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const openSee = (row) => {
    console.log(row);
    push({
      path: '/project/problem-connect/details',
      query: {
        problemId: row?.problemId
      }
    });
  };

  /** 编辑 */
  const openAdd = (row) => {
    handle.value = row ? 'edit' : 'add';
    editData.value = row || null;
    visibleModal.value = true;
  };
  /** 导出数据 */
  const openExport = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportProblemList({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  onMounted(() => {
    const { query } = unref(currentRoute);
    if (query?.problemStatus) {
      form.problemStatus = query.problemStatus;
    }
    if (query?.overTimeStatus) {
      form.overTimeStatus = [query.overTimeStatus];
    }
    reload(form);
  });
</script>
