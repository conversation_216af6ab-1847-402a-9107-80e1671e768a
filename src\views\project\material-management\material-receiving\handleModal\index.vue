<template>
  <div>
    <ele-modal
      form
      :width="600"
      title="确认接收发运单"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <pro-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :items="items"
        :footer="true"
        :label-width="120"
        :grid="{ span: 24 }"
        :footerStyle="{ justifyContent: 'flex-end' }"
        @updateValue="setFieldValue"
      >
        <template #footer>
          <el-button @click="cancelDialog">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="save">
            保存
          </el-button>
        </template>
      </pro-form>
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { materialForwardingReceive } from '@/api/project/material-management/material-receiving';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    id: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    }
  });
  const emit = defineEmits(['success']);

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    resetFields();
    setTimeout(() => {
      formRef.value?.clearValidate?.();
    }, 0);
  };

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    receiveName: '',
    phone: '',
    receiveTime: '',
    sysFileIds: '',
    receiveRemark: ''
  });

  /** 基本信息表单项 */
  const items = ref([
    {
      label: '接收人',
      prop: 'receiveName',
      type: 'input',
      required: true,
      props: { disabled: true }
    },
    {
      label: '接收人电话',
      prop: 'phone',
      type: 'input',
      required: true,
      props: { disabled: true }
    },
    {
      label: '接收日期',
      prop: 'receiveTime',
      type: 'date',
      required: true
    },
    {
      label: '物资签收单',
      prop: 'sysFileIds',
      type: 'fileUpload',
      required: true
    },
    {
      label: '接收备注',
      prop: 'receiveRemark',
      type: 'input'
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const params = {
        ...form,
        forwardingId: props.id
      };
      if (form.sysFileIds) {
        let dd = JSON.parse(form.sysFileIds);
        params.sysFileIds = dd.map((val) => val.ossId).join(',');
      }
      materialForwardingReceive(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success('操作成功！');
          emit('success');
          cancelDialog();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  watch(visible, (val) => {
    if (val) {
      console.log(val, props.data, 'lll');
      const info = {
        receiveName: props.data.forwardingApplyDetailsVoList[0].recipientName,
        phone: props.data.forwardingApplyDetailsVoList[0].phone
      };
      assignFields(info);
    }
  });
</script>
