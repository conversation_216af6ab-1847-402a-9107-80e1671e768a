<template>
  <ele-card
    :flex-table="fixedHeight"
    :body-style="{ paddingBottom: '4px' }"
    :style="{
      minHeight: fixedHeight ? '100px' : void 0,
      marginBottom: fixedHeight ? '10px' : void 0
    }"
    header="接收信息"
    bordered
  >
    <el-empty
      v-if="!data.receiveTime"
      description="暂无接收信息"
      height="100"
    />
    <el-descriptions v-else>
      <el-descriptions-item label="接收人：">{{
        data.receiveName
      }}</el-descriptions-item>
      <el-descriptions-item label="接收时间：">{{
        data.receiveTime
      }}</el-descriptions-item>
      <el-descriptions-item label="接收电话：">{{
        data.forwardingApplyDetailsVoList[0].phone
      }}</el-descriptions-item>
      <el-descriptions-item label="接收备注：">{{
        data.receiveRemark
      }}</el-descriptions-item>

      <el-descriptions-item label="附件：">
        <el-link
          v-if="data?.sysOssVoList && data.sysOssVoList.length !== 0"
          type="primary"
          underline="never"
          @click="uploadFile(data.sysOssVoList[0])"
          >{{ data.sysOssVoList[0].originalName }}</el-link
        >
      </el-descriptions-item>
    </el-descriptions>
  </ele-card>
</template>
<script setup>
  import { ref } from 'vue';
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);

  const uploadFile = (data) => {
    const base64Url = btoa(data.url);
    const url = `https://kkfile.520gcp.com/onlinePreview?url=${base64Url}`;
    window.open(url);
  };
</script>
