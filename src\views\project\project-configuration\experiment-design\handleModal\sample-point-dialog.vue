<template>
  <div>
    <ele-modal
      form
      :width="1200"
      :title="`${props.handle === 'clone' ? '克隆' : props.handle === 'edit' ? '编辑' : '新增'}采样点`"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <el-form
        ref="formRef"
        class="sample-point-form"
        :model="form"
        label-width="0px"
        @submit.prevent=""
      >
        <el-form-item
          label="周期"
          prop="cycleName"
          label-width="50px"
          grid="12"
          v-if="designType === '1'"
          :rules="[{ required: true, message: '请输入周期', trigger: 'blur' }]"
        >
          <el-input
            v-model="form.cycleName"
            style="width: 240px"
            :maxlength="50"
            :show-word-limit="true"
          />
        </el-form-item>
        <ele-data-table
          row-key="cycleGroupId"
          :columns="columns"
          :data="form.points"
          style="max-height: 50vh; overflow-y: auto"
          cell-class-name="editable-table-cell"
          class="editable-table sortable-table"
        >
          <!-- 动态生成可编辑字段模板 -->
          <template
            v-for="field in editableFields"
            :key="field.key"
            #[field.key]="{ row, $index }"
          >
            <el-form-item
              :prop="'points.' + $index + '.' + field.key"
              :rules="getFieldRules(field, $index)"
              class="form-error-popper"
              style="margin-bottom: 0"
            >
              <div class="editable-cell-wrapper">
                <!-- 根据字段类型渲染不同组件 -->
                <el-input
                  v-if="row.isEdit && field.type === 'input'"
                  clearable
                  :maxlength="10"
                  v-model="row[field.key]"
                  :placeholder="field.placeholder"
                />
                <el-input-number
                  v-else-if="row.isEdit && field.type === 'inputNumber'"
                  clearable
                  type="number"
                  :min="0"
                  :max="9999"
                  v-model="row[field.key]"
                  :placeholder="field.placeholder"
                />
                <el-select
                  v-else-if="row.isEdit && field.type === 'select'"
                  clearable
                  v-model="row[field.key]"
                  :placeholder="field.placeholder"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :value="option.value"
                    :label="option.label"
                  />
                </el-select>
                <div v-else class="editable-cell-text">{{
                  row[field.key]
                }}</div>
              </div>
            </el-form-item>
          </template>

          <!-- 拖拽手柄列 -->
          <template #handle>
            <span
              class="drag-handle"
              style="cursor: move; color: #c0c4cc; font-size: 14px"
            >
              ⋮⋮
            </span>
          </template>

          <template #action="{ row, $index }">
            <div style="display: inline; align-items: center">
              <el-link
                type="danger"
                underline="never"
                @click="remove(row, $index)"
              >
                删除
              </el-link>
            </div>
          </template>
        </ele-data-table>
        <el-button
          plain
          type="primary"
          @click="handleAddSamplePoint"
          :icon="PlusOutlined"
          v-if="designType.value === '1' || props.handle !== 'edit'"
          style="width: 100%; margin-top: 5px"
          >添加采样点</el-button
        >
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import {
    watch,
    ref,
    inject,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    nextTick
  } from 'vue';
  import SortableJs from 'sortablejs';
  import {
    addSamplePoint,
    editSamplePoint
  } from '@/api/project/project-configuration/experiment-design';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';

  const visible = defineModel({ type: Boolean, default: false });
  const props = defineProps({
    /** 编辑数据 */
    editData: {
      type: Object,
      default: () => ({})
    },
    handle: {
      type: String,
      default: 'add'
    },
    groupId: String
  });

  const emit = defineEmits(['refresh']);

  const projectInfo = inject('projectInfo');

  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
    resetForm();
  };

  const form = reactive({
    cycleName: '',
    points: []
  });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 可编辑字段配置 */
  const fieldsByType = {
    1: [
      {
        key: 'samplePointTime',
        label: '采样时间',
        placeholder: '请输入采样点时间',
        message: '请输入采样点时间',
        required: true,
        type: 'input'
      },
      {
        key: 'sampleNumber',
        label: '样本序号',
        placeholder: '请输入样本序号',
        message: '请输入样本序号',
        required: true,
        type: 'inputNumber'
      }
    ],
    2: [
      {
        key: 'cycleName',
        label: '周期',
        placeholder: '请输入周期',
        message: '请输入周期',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointYear',
        label: '年',
        placeholder: '请输入年',
        message: '请输入年',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointMonth',
        label: '月',
        placeholder: '请输入月',
        message: '请输入月',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointWeek',
        label: '周',
        placeholder: '请输入周',
        message: '请输入周',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointDay',
        label: '日',
        placeholder: '请输入日',
        message: '请输入日',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointTime',
        label: '采样时间点',
        placeholder: '请输入采样点时间',
        message: '请输入采样点时间',
        type: 'input'
      },
      {
        key: 'sampleNumber',
        label: '样本序号',
        placeholder: '请输入样本序号',
        message: '请输入样本序号',
        required: true,
        type: 'input'
      }
    ],
    3: [
      {
        key: 'visitNumber',
        label: '访视号',
        placeholder: '请输入访视号',
        message: '请输入访视号',
        required: true,
        type: 'input'
      },
      {
        key: 'studyTime',
        label: '研究时间',
        placeholder: '请选择研究时间',
        message: '请选择研究时间',
        required: true,
        type: 'input'
      },
      {
        key: 'samplePointTime',
        label: '采样时间点',
        placeholder: '请输入采样点时间',
        message: '请输入采样点时间',
        type: 'input'
      }
    ]
  };

  // 根据type动态获取可编辑字段
  const editableFields = computed(() => {
    const fields = fieldsByType[designType.value] || [];
    return fields;
  });

  // 获取字段校验规则
  const getFieldRules = (field, rowIndex) => {
    // 对于type=2的情况，实现特殊校验逻辑：只要有一个字段有值就行
    if (designType.value === '2') {
      return {
        validator: (rule, value, callback) => {
          const currentRow = form.points[rowIndex];
          if (!currentRow) {
            callback();
            return;
          }

          // 检查当前行的所有type=2字段是否至少有一个有值
          const type2Fields = [
            'cycleName',
            'samplePointYear',
            'samplePointMonth',
            'samplePointWeek',
            'samplePointDay'
          ];
          const hasValue = type2Fields.some((fieldKey) => {
            const fieldValue = currentRow[fieldKey];
            return fieldValue && fieldValue.toString().trim() !== '';
          });

          if (!hasValue) {
            callback(new Error('周期、年、月、周、日至少需要填写一个'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      };
    }

    // 访视号3的，访视号要保证唯一，不能重复
    if (designType.value === '3' && field.key === 'visitNumber') {
      return {
        validator: (rule, value, callback) => {
          const currentRow = form.points[rowIndex];
          if (!currentRow) {
            callback();
            return;
          }

          // 检查当前行的所有type=2字段是否至少有一个有值
          console.log('field', field);
          const hasValue = form.points.filter((row) => {
            return row.visitNumber === value;
          });

          if (hasValue.length > 1) {
            callback(new Error('访视号不能重复'));
          } else if (!value) {
            callback(new Error('访视号不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      };
    }

    if (field.type === 'inputNumber') {
      return {
        required: field.required,
        message: field.message,
        type: 'number',
        trigger: 'blur'
      };
    }

    // 其他type使用原有的校验规则
    return {
      required: field.required,
      message: field.message,
      type: 'string',
      trigger: 'blur'
    };
  };

  const columsByType = ref({
    1: [
      {
        columnKey: 'samplePointTime',
        label: '采样时间点',
        minWidth: 120,
        slot: 'samplePointTime'
      },
      {
        columnKey: 'sampleNumber',
        label: '样本序号',
        minWidth: 120,
        slot: 'sampleNumber'
      }
    ],
    2: [
      {
        columnKey: 'cycleName',
        label: '周期',
        minWidth: 120,
        slot: 'cycleName'
      },
      {
        columnKey: 'samplePointYear',
        label: '年',
        minWidth: 120,
        slot: 'samplePointYear'
      },
      {
        columnKey: 'samplePointMonth',
        label: '月',
        minWidth: 120,
        slot: 'samplePointMonth'
      },
      {
        columnKey: 'samplePointWeek',
        label: '周',
        minWidth: 120,
        slot: 'samplePointWeek'
      },
      {
        columnKey: 'samplePointDay',
        label: '日',
        minWidth: 120,
        slot: 'samplePointDay'
      },
      {
        columnKey: 'samplePointTime',
        label: '采样时间点',
        minWidth: 120,
        slot: 'samplePointTime'
      },
      {
        columnKey: 'sampleNumber',
        label: '样本序号',
        minWidth: 120,
        slot: 'sampleNumber'
      }
    ],
    3: [
      {
        columnKey: 'visitNumber',
        label: '访视号',
        minWidth: 120,
        slot: 'visitNumber'
      },
      {
        columnKey: 'studyTime',
        label: '研究时间',
        minWidth: 120,
        slot: 'studyTime'
      },
      {
        columnKey: 'samplePointTime',
        label: '采样时间点',
        minWidth: 120,
        slot: 'samplePointTime'
      }
    ]
  });

  // 采样点配置
  const columns = computed(() => {
    const typeColumns = columsByType.value[designType.value] || [];

    const baseColumns = [
      {
        type: 'index',
        columnKey: 'index',
        width: 60,
        label: '序号',
        align: 'center' /* ,
        fixed: 'left' */
      },
      {
        columnKey: 'handle',
        label: '',
        width: 50,
        align: 'center',
        slot: 'handle',
        fixed: 'left'
      },
      // 安全地获取对应类型的列配置，如果不存在则使用空数组
      ...typeColumns
    ];

    if (designType.value === '1' || props.handle !== 'edit') {
      baseColumns.push({
        columnKey: 'action',
        label: '操作',
        width: 150,
        align: 'center',
        fixed: 'right',
        slot: 'action'
      });
    }

    return baseColumns;
  });

  /** 拖拽排序实例 */
  let sortableIns = null;

  /** 添加 */
  const handleAddSamplePoint = () => {
    const newItem = {
      groupId: props.groupId,
      cycleGroupId: Date.now(),
      visitNumber: '',
      studyTime: '',
      cycleName: '',
      samplePointYear: '',
      samplePointMonth: '',
      samplePointWeek: '',
      samplePointDay: '',
      samplePointTime: '',
      sampleNumber: '', // 添加缺失的样本序号字段
      isEdit: true
    };
    form.points.push(newItem);
  };

  /** 删除 */
  const remove = (row, index) => {
    form.points.splice(index, 1);
  };

  const resetForm = () => {
    form.points = [];
    handleAddSamplePoint();
    form.cycleName = '';
    form.cycleGroupId = '';
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }

      // 额外校验：确保至少有一个采样点
      if (!form.points || form.points.length === 0) {
        EleMessage.error('请至少添加一个采样点');
        return;
      }

      loading.value = true;
      const params = form.points?.map((item, index) => {
        return {
          ...item,
          serialNumber: String(index + 1),
          // 如果是1，需要将周期加入进去
          cycleName: designType.value === '1' ? form.cycleName : item.cycleName,
          cycleGroupId:
            designType.value === '1'
              ? form.cycleGroupId || Date.now()
              : item.cycleGroupId
        };
      });
      if (props.handle === 'clone') {
        params.forEach((item) => {
          item.pointId = null;
        });
      }
      const saveOrUpdate =
        props.handle === 'edit' ? editSamplePoint : addSamplePoint;
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  const onClose = () => {
    resetForm();
    emit('refresh');
    visible.value = false;
  };

  /** 初始化拖拽排序 */
  const initSortable = () => {
    // 销毁之前的实例
    if (sortableIns) {
      sortableIns.destroy();
      sortableIns = null;
    }

    // 等待DOM渲染完成后初始化拖拽
    setTimeout(() => {
      const tableBody = document.querySelector('.sortable-table tbody');
      if (tableBody && !('ontouchstart' in document.documentElement)) {
        sortableIns = new SortableJs(tableBody, {
          handle: '.drag-handle',
          animation: 300,
          ghostClass: 'sortable-ghost',
          chosenClass: 'sortable-chosen',
          dragClass: 'sortable-drag',
          disabled: false,
          onUpdate: (evt) => {
            const { oldIndex, newIndex } = evt;
            if (typeof oldIndex === 'number' && typeof newIndex === 'number') {
              const temp = [...form.points];
              temp.splice(newIndex, 0, temp.splice(oldIndex, 1)[0]);
              form.points = temp;
            }
          }
        });
      }
    }, 300);
  };

  onMounted(() => {
    initSortable();
  });

  /** 销毁拖拽排序 */
  onBeforeUnmount(() => {
    if (sortableIns) {
      sortableIns.destroy();
      sortableIns = null;
    }
  });

  // 监听visible变化，重新初始化拖拽和表单
  watch(visible, (newVal) => {
    if (newVal) {
      const value = props.editData;

      // 如果有编辑数据，加载编辑数据；否则重置表单
      if (value && Object.keys(value).length > 0) {
        // 清空表单但不添加空白行
        form.points = [];
        form.cycleName = '';
        form.cycleGroupId = '';

        if (designType.value === '1') {
          form.cycleGroupId = value.cycleGroupId;
          form.points = value.children.map((i) => {
            // 确保每个数据项都包含所有必要字段，并转换数据类型
            return {
              ...i,
              isEdit: true,
              sampleNumber: String(i.sampleNumber || ''), // 确保sampleNumber字段存在且为字符串
              samplePointTime: String(i.samplePointTime || ''), // 确保为字符串
              visitNumber: String(i.visitNumber || ''), // 确保为字符串
              studyTime: String(i.studyTime || '') // 确保为字符串
            };
          });
          form.cycleName = value.treeLabel;
        } else {
          form.points = [
            {
              ...value,
              isEdit: true,
              sampleNumber: String(value.sampleNumber || ''), // 确保sampleNumber字段存在且为字符串
              samplePointTime: String(value.samplePointTime || ''), // 确保为字符串
              visitNumber: String(value.visitNumber || ''), // 确保为字符串
              studyTime: String(value.studyTime || ''), // 确保为字符串
              cycleName: String(value.cycleName || ''), // 确保为字符串
              samplePointYear: String(value.samplePointYear || ''), // 确保为字符串
              samplePointMonth: String(value.samplePointMonth || ''), // 确保为字符串
              samplePointWeek: String(value.samplePointWeek || ''), // 确保为字符串
              samplePointDay: String(value.samplePointDay || '') // 确保为字符串
            }
          ];
        }

        if (props.handle === 'clone') {
          form.cycleGroupId = Date.now();
        }

        // 数据加载完成后，清除校验状态
        nextTick(() => {
          if (formRef.value) {
            formRef.value.clearValidate();
          }
        });
      } else {
        // 没有编辑数据，重置表单
        resetForm();
      }

      setTimeout(() => {
        initSortable();
        // 清除表单校验状态，解决编辑时校验缓存问题
        if (formRef.value) {
          formRef.value.clearValidate();
        }
      }, 300);
    }
  });
</script>

<style scoped>
  .sample-point-form {
    min-height: 50vh;
    overflow: auto;
  }

  .editable-cell-wrapper {
    display: flex;
    align-items: center;
  }

  .drag-handle {
    color: #c0c4cc;
    font-size: 14px;
    cursor: move;
    user-select: none;
    display: inline-block;
    padding: 4px;
    line-height: 1;
    transition: all 0.2s ease;
  }

  .drag-handle:hover {
    color: #409eff;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .sortable-ghost {
    opacity: 0.5;
    background: #f5f7fa;
  }

  .sortable-chosen {
    background: #ecf5ff;
  }

  .sortable-drag {
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .editable-cell-text {
    min-height: 32px;
    line-height: 32px;
    padding: 0 11px;
  }

  :deep(.editable-table-cell) {
    padding: 8px !important;
  }

  :deep(.ele-table) {
    min-height: 300px;
  }

  :deep(.ele-table tbody tr) {
    transition: all 0.3s ease;
  }

  :deep(.ele-table tbody tr:hover) {
    background-color: #f5f7fa;
  }
</style>
