import request from '@/utils/request';
import {
  download,
  toFormData,
  checkDownloadRes,
  transformParams
} from '@/utils/common';

//查询问题列表
export async function getProblemList(params) {
  const res = await request.get('/project/problem/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出问题列表
 */
export async function exportProblemList(params) {
  const res = await request({
    url: '/project/problem/export',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `问题列表${Date.now()}.xlsx`);
}

//获取问题详细信息
export async function getProblemDetail(problemId) {
  const res = await request.get(`/project/problem/${problemId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增问题
 */
export async function addProblem(data) {
  const res = await request.post('/project/problem/add', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 回复问题
 */
export async function replyProblem(data) {
  const res = await request.post('/project/problem/reply', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 关闭问题
 */
export async function closeProblem(data) {
  const res = await request.post('/project/problem/close', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出问题
 */
export async function exportProblem(params) {
  const res = await request({
    url: `/project/problem/exportDetail`,
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  download(res.data, `问题详情${Date.now()}.pdf`);
}
