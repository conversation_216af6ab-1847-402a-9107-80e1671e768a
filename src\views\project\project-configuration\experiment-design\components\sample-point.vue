<template>
  <div class="tree-container">
    <div class="tree-top">
      <span>采样点配置</span>
      <el-button
        type="primary"
        :icon="PlusOutlined"
        link
        v-show="!isView"
        @click="handleAdd"
        v-permission="'sample:samplePoint:add'"
        >新增采样点</el-button
      >
    </div>
    <el-tree
      ref="treeRef"
      :data="treeData"
      highlight-current
      :show-checkbox="showCheckbox && !isView"
      :node-key="designType === '1' ? 'cycleGroupId' : 'pointId'"
      :props="{ label: 'treeLabel' }"
      :expand-on-click-node="false"
      :check-on-click-node="false"
      :default-expand-all="true"
      :style="{ '--ele-tree-item-height': '34px' }"
      @node-click="handleNodeClick"
    >
      <template #default="scope">
        <el-icon style="margin-right: 6px; color: #ffd659; font-size: 16px">
          <FolderOutlined
            v-if="scope.data.children?.length"
            style="fill: currentColor"
          />
          <FileOutlined
            v-else
            style="transform: scale(0.9) translateY(1px); color: #faad14"
          />
        </el-icon>
        <span class="el-tree-node__label" style="margin-top: 2px">
          {{ scope.data.children?.length && '周期：' }}
          {{ scope.data.treeLabel }}
        </span>
        <!-- 按钮组：只有一级或者是一级节点时显示 -->
        <div v-show="shouldShowButtons(scope.data)">
          <el-button
            link
            type="primary"
            :icon="EditOutlined"
            @click.stop="handleEdit(scope.data)"
            :disabled="scope.data.consumablesStatus === '1'"
            v-permission="'sample:samplePoint:edit'"
            >编辑</el-button
          >
          <el-button
            link
            type="warning"
            :icon="EditOutlined"
            @click.stop="handleClone(scope.data)"
            v-show="designType === '1'"
            :disabled="scope.data.consumablesStatus === '1'"
            v-permission="'sample:samplePoint:add'"
            >克隆</el-button
          >
          <el-button
            link
            type="danger"
            :icon="DeleteOutlined"
            @click.stop="handleDelete(scope.data)"
            :disabled="scope.data.consumablesStatus === '1'"
            v-permission="'sample:samplePoint:remove'"
            >删除</el-button
          >
        </div>
        <!-- 标签：只有一级或者是二级节点时显示 -->
        <div
          v-show="shouldShowTags(scope.data) && designType !== '3'"
          style="margin-left: 10px"
        >
          <!-- 样本管状态，0=未配置，1=已配置 -->
          <el-tag type="info" v-if="scope.data.tubeStatus === '0'"
            >待配置</el-tag
          >
          <el-tag type="success" v-else>已配置</el-tag>
        </div>
      </template>
    </el-tree>
    <div class="tree-bottom" v-show="designType !== '3' && !isView">
      <el-checkbox v-model="checkAll" @change="handleCheckAll"
        >全选</el-checkbox
      >
      <el-button
        link
        type="primary"
        :icon="CheckCircleOutlined"
        @click="handleBatchConfig"
        v-permission="'sample:sampleTube:batchAdd'"
        >批量配置样本管</el-button
      >
    </div>
  </div>
  <samplePointDialog
    v-model="dialogVisible"
    :editData="editData"
    :groupId="props.groupId"
    :handle="handle"
    @refresh="loadSamplePoints"
  />
  <SampleTubeDialog
    v-model="tubeDialogVisible"
    @save="handleTubeSave"
    :pointIds="selectPoints"
  />
</template>

<script setup>
  import { ref, watch, inject, computed } from 'vue';
  import {
    FolderOutlined,
    FileOutlined,
    PlusOutlined,
    CheckCircleOutlined,
    EditOutlined,
    DeleteOutlined
  } from '@/components/icons';
  import samplePointDialog from '../handleModal/sample-point-dialog.vue';
  import SampleTubeDialog from '../handleModal/sample-tube-dialog.vue';
  import {
    getSamplePointList,
    deleteSamplePoint
  } from '@/api/project/project-configuration/experiment-design';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';

  const props = defineProps({
    groupId: String,
    isView: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['changeSamplePoint']);

  const projectInfo = inject('projectInfo');

  // 使用computed来响应designType的变化，提供默认值
  const designType = computed(() => projectInfo?.designType || '1');

  const showCheckbox = ref(true);
  const treeRef = ref(null);
  const dialogVisible = ref(false);
  const editData = ref(null);
  const tubeDialogVisible = ref(false);
  const selectPoints = ref([]);
  const handle = ref('add');

  const treeData = ref([]);

  // 批量全选
  const checkAll = ref(false);
  const handleCheckAll = (val) => {
    treeRef.value.setCheckedNodes(val ? treeData.value : []);
  };

  // 加载采样点数据
  const loadSamplePoints = async () => {
    try {
      checkAll.value = false;
      showCheckbox.value = true;
      const result = await getSamplePointList(props.groupId);
      if (!result?.data?.length) {
        emit('changeSamplePoint', null);
      }
      // 设计类型，1=研究周期+采样时间，2=周期+年/月/周/日+采样时间，3=访视+采样点
      if (designType.value === '1') {
        const obj = {};
        result.data?.forEach((item) => {
          if (obj[item.cycleGroupId]) {
            obj[item.cycleGroupId].push(item);
          } else {
            obj[item.cycleGroupId] = [item];
          }
        });
        treeData.value = Object.keys(obj).map((key) => {
          return {
            ...obj[key][0],
            cycleGroupId: key,
            treeLabel: obj[key][0].cycleName,
            children: obj[key].map((item, index) => {
              return {
                ...item,
                cycleGroupId: item.cycleGroupId + index,
                treeLabel: item.samplePointTime + ' - ' + item.sampleNumber
              };
            })
          };
        });
      } else if (designType.value === '2') {
        // 如果是访视，designType为3
        treeData.value = result.data?.map((item) => {
          const {
            cycleName,
            samplePointYear,
            samplePointMonth,
            samplePointWeek,
            samplePointDay,
            sampleNumber
          } = item;
          return {
            ...item,
            treeLabel: [
              cycleName,
              samplePointYear,
              samplePointMonth,
              samplePointWeek,
              samplePointDay,
              sampleNumber
            ]
              .filter((i) => i)
              .join('-')
          };
        });
      } else if (designType.value === '3') {
        showCheckbox.value = false;
        // 如果是访视，designType为3
        treeData.value = result.data?.map((item) => {
          return {
            ...item,
            treeLabel:
              item.visitNumber +
              ' - ' +
              item.studyTime +
              ' - ' +
              item.samplePointTime
          };
        });
      }
    } catch (error) {
      console.error('加载采样点数据失败:', error);
    }
  };

  // 新增采样点
  const handleAdd = () => {
    dialogVisible.value = true;
    handle.value = 'add';
    editData.value = null;
  };

  // 编辑采样点
  const handleEdit = (data) => {
    dialogVisible.value = true;
    handle.value = 'edit';
    editData.value = data;
  };

  // 克隆采样点
  const handleClone = (data) => {
    dialogVisible.value = true;
    handle.value = 'clone';
    editData.value = { ...data };
  };

  // 删除采样点
  const handleDelete = async (data) => {
    try {
      ElMessageBox.confirm(
        `确定要删除【${data.treeLabel}】采样点吗?`,
        '系统提示',
        {
          type: 'warning',
          draggable: true
        }
      )
        .then(async () => {
          await deleteSamplePoint(data.pointId);
          // 重新加载数据
          await loadSamplePoints();
        })
        .catch(() => {});
    } catch (error) {
      console.error('删除采样点失败:', error);
    }
  };

  // 批量配置样本管
  const handleBatchConfig = () => {
    const checkedNodes = treeRef.value.getCheckedNodes(true); // 获取选中的节点
    selectPoints.value = checkedNodes.map((node) => node.pointId);
    if (selectPoints.value.length === 0) {
      EleMessage.warning({ message: '请选择采样点', plain: true });
      return;
    }
    tubeDialogVisible.value = true;
  };

  // 处理样本管配置保存后，更新对应采样点状态
  const handleTubeSave = async () => {
    console.log('handleTubeSave', selectPoints.value);
    loadSamplePoints();
  };

  // 判断是否显示按钮
  const shouldShowButtons = (nodeData) => {
    // 检查树是否有两级结构
    const hasMultiLevel = treeData.value.some(
      (item) => item.children && item.children.length > 0
    );

    if (!hasMultiLevel) {
      // 只有一级，都显示按钮
      return !props.isView;
    } else {
      // 有两级，只有一级节点显示按钮
      return !props.isView && nodeData.children && nodeData.children.length > 0;
    }
  };

  // 判断是否显示标签
  const shouldShowTags = (nodeData) => {
    // 检查树是否有两级结构
    const hasMultiLevel = treeData.value.some(
      (item) => item.children && item.children.length > 0
    );

    if (!hasMultiLevel) {
      // 只有一级，都显示标签
      return true;
    } else {
      // 有两级，只有二级节点显示标签
      return !nodeData.children || nodeData.children.length === 0;
    }
  };

  const handleNodeClick = (data) => {
    if (data.children) {
      return;
    } else {
      emit('changeSamplePoint', data.pointId);
    }
  };

  watch(
    () => props.groupId,
    (newVal) => {
      if (newVal) {
        // 加载采样点数据
        loadSamplePoints();
      } else {
        // 清空数据
        treeData.value = [];
        emit('changeSamplePoint', null);
      }
    },
    { immediate: true }
  );

  defineExpose({
    loadSamplePoints
  });
</script>
<style scoped lang="less">
  .tree-container {
    height: calc(100% - 80px);
    .el-tree {
      height: 100%;
      overflow: auto;
    }
    .tree-top {
      font-size: 16px;
      margin-bottom: 10px;
      padding: 0 10px 10px 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      border-bottom: 1px solid #e9e9e9;
    }
    .tree-bottom {
      border-top: 1px solid #e9e9e9;
      padding: 10px 10px 0 10px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .el-checkbox {
        margin-right: 10px;
      }
    }
  }
</style>
