<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="reload($event, 1)"
        @reset="reload($event, 1)"
      >
        <template #toolbar>
          <el-button
            v-permission="'hospitalMaterial:inventory:export'"
            @click="exportFun"
            plain
            >导出</el-button
          >
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        v-model:current="current"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :sticky="!fixedHeight"
        cache-key="institutionalMaterial-inventory"
        :border="tableBorder"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="['columns']"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <el-link
            v-permission="'hospitalMaterial:inventory:markAbnormal'"
            type="primary"
            underline="never"
            @click.stop="tagsUse(row)"
          >
            标记异常
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <handleModal
      v-if="visibleModal"
      v-model="visibleModal"
      :editData="editData"
    />
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed, nextTick } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getHospitalMaterialInventoryList,
    exportHospitalMaterialInventory
  } from '@/api/project/material-management/institutionalMaterial-inventory';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';
  import handleModal from './handleModal/index.vue';

  defineOptions({ name: 'InstitutionalMaterialInventory' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 用户名筛选值 */
  const nicknameFilterValue = ref('');

  /** 表单数据 */
  const form = reactive({
    username: '',
    organizationName: '',
    phone: '',
    email: '',
    createTime: ['', ''],
    sex: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    console.log(prop, value);
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'input',
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      type: 'input',
      label: '耗材包编号',
      prop: 'consumablesNumber'
    },
    {
      type: 'input',
      label: '机构名称',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '机构代号',
      prop: 'hospitalCode'
    }
  ]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'consumablesNumber',
        label: '耗材包编号',
        width: 100,
        align: 'center'
      },
      {
        prop: 'consumablesName',
        label: '耗材包名称',
        align: 'center'
      },
      {
        prop: 'hospitalCode',
        label: '机构代号',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'hospitalName',
        label: '机构名称',
        align: 'center'
      },
      {
        prop: 'applyQuantity',
        label: '申请数量',
        align: 'center',
        hideInTable: true
      },
      {
        prop: 'waitQuantity',
        label: '待接收',
        align: 'center'
      },
      {
        prop: 'receiveQuantity',
        label: '已接收',
        align: 'center'
      },
      {
        prop: 'useQuantity',
        label: '已使用',
        align: 'center'
      },
      {
        prop: 'notUseQuantity',
        label: '未使用',
        align: 'center'
      },
      {
        prop: 'expireQuantity',
        label: '快过期',
        align: 'center'
      },
      {
        prop: 'endDate',
        label: '最近过期日期',
        align: 'center'
      },
      {
        prop: 'abnormalQuantity',
        label: '异常数量',
        align: 'center',
        hideInTable: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 150,
        align: 'center',
        showOverflowTooltip: false,
        resizable: false,
        slot: 'action',
        fixed: 'right',
        disabledInSetting: true,
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格单选选中数据 */
  const current = ref(null);

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getHospitalMaterialInventoryList({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 刷新表格 */
  const reload = (where, page) => {
    tableRef.value?.reload?.({ where, page });
  };

  /** 修改搜索框背景色 */
  const tableBorder = ref(false);
  const changeTableBorder = () => {
    tableBorder.value = !tableBorder.value;
  };

  //标记使用
  const editData = ref({});
  const visibleModal = ref(false);
  const tagsUse = (row) => {
    editData.value = row || null;
    visibleModal.value = true;
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportHospitalMaterialInventory({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
