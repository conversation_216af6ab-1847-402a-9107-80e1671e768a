<template>
  <ele-page :flex-table="fixedHeight" hide-footer>
    <ele-card
      :flex-table="fixedHeight"
      :body-style="{ paddingBottom: '4px' }"
      :style="{
        minHeight: fixedHeight ? '380px' : void 0,
        marginBottom: fixedHeight ? '10px' : void 0
      }"
    >
      <EleTableSearch
        :model="form"
        :items="items"
        filterRowToMore="1"
        :show-label="false"
        @updateValue="updateFormValue"
        @submit="onSearch"
        @reset="onSearch"
      >
        <template #toolbar>
          <el-button v-permission="'material:stat:export'" plain @click="exportFun"> 导出 </el-button>
        </template>
      </EleTableSearch>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :isMobile="mobileDevice"
        :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
        :tools="false"
        :footer-style="{ paddingBottom: '12px' }"
        style="padding-bottom: 0; margin-top: -5px"
        @done="onDone"
      >
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup name="NormalTable">
  import { ref, reactive, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    getExpressDeliveryStat,
    exportExpressDeliveryStat
  } from '@/api/statistics/express';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { useRouter } from 'vue-router';

  defineOptions({ name: 'Express' });

  const { push } = useRouter();

  const { mobileDevice } = useMobileDevice();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const form = reactive({
    bookingNote: '',
    hospitalName: '',
    analyseProjectNumber: ''
  });

  /** 更新表单数据 */
  const updateFormValue = (prop, value) => {
    form[prop] = value;
  };

  /** 表单项 */
  const items = ref([
    {
      type: 'dictSelect',
      label: '运输单位',
      prop: 'logisticsCompany',
      props: {
        code: 'logistics_company'
      }
    },
    {
      type: 'date',
      label: '发运日期',
      prop: 'forwardingDate'
    },
    {
      type: 'input',
      label: '发件单位',
      prop: 'hospitalName'
    },
    {
      type: 'input',
      label: '分析项目编号',
      prop: 'analyseProjectNumber'
    },
    {
      type: 'input',
      label: '公司项目编号',
      prop: 'companyProjectNumber'
    },
    {
      type: 'input',
      label: '试验方案编号',
      prop: 'schemeNumber'
    }
  ]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'bookingNote',
        label: '运输单号',
        align: 'center'
      },
      {
        prop: 'logisticsCompany',
        label: '运输单位',
        align: 'center'
      },
      {
        prop: 'shipperName',
        label: '下单人',
        align: 'center'
      },
      {
        prop: 'forwardingDate',
        label: '发件日期',
        align: 'center'
      },
      {
        prop: 'hospitalName',
        label: '发件单位',
        align: 'center'
      },
      {
        prop: 'remark',
        label: '订单备注',
        align: 'center'
      },
      {
        prop: 'analyseProjectNumber',
        label: '分析项目编号',
        align: 'center'
      },
      {
        prop: 'companyProjectNumber',
        label: '公司项目编号',
        align: 'center'
      },
      {
        prop: 'schemeNumber',
        label: '试验方案编号',
        align: 'center'
      }
    ];
  });

  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders, filters }) => {
    return getExpressDeliveryStat({
      ...where,
      ...orders,
      ...filters,
      ...pages
    });
  };

  /** 表格数据请求完成事件 */
  const onDone = () => {};

  /** 搜索事件 */
  const onSearch = (where) => {
    reload(where);
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  //导出
  const exportFun = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ pages, where }) => {
      exportExpressDeliveryStat({ ...where, ...pages })
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
