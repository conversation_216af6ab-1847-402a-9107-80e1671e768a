<template>
  <div style="margin-bottom: 30px">
    <div style="font-weight: bold">试剂发运内容</div>
    <ele-pro-table
      row-key="userId"
      :columns="columns"
      :datasource="data"
      :pagination="false"
      border
    >
    </ele-pro-table>
  </div>
</template>
<script setup>
  import { ref } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  /** 表格固定高度 */
  const fixedHeight = ref(true);
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '试剂名称',
      prop: 'reagentName'
    },
    {
      label: '保存条件',
      prop: 'storageCondition'
    },
    {
      label: '备注',
      prop: 'remark'
    },
    {
      label: '申请数量',
      prop: 'applyQuantity'
    },
    {
      label: '本次发运数量',
      prop: 'quantity'
    }
  ]);
</script>
