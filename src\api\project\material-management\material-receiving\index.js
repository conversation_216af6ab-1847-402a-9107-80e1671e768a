import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//物资接收列表
export async function getMaterialForwardingReceiveList(params) {
  const res = await request.get('/materialForwarding/receiveList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
// 接收
export async function materialForwardingReceive(data) {
  const res = await request.post('/materialForwarding/receive', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//导出 物资接收列表
export async function exportReceiveList(params) {
  const res = await request({
    url: '/materialForwarding/exportReceiveList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `物资接收_${Date.now()}.xlsx`);
}
