import request from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
//耗材包统计
export async function selectConsumablesPackageStatList(params) {
  const res = await request.get(
    '/materialStatistics/selectConsumablesPackageStat',
    { params }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出 耗材包统计
export async function exportConsumablesPackageStat(params) {
  const res = await request({
    url: '/materialStatistics/exportConsumablesPackageStat',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `项目物资统计-耗材包_${Date.now()}.xlsx`);
}
//散装耗材统计
export async function selectBulkConsumablesStatList(params) {
  const res = await request.get(
    '/materialStatistics/selectBulkConsumablesStat',
    { params }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
//导出 散装耗材统计
export async function exportBulkConsumablesStat(params) {
  const res = await request({
    url: '/materialStatistics/exportBulkConsumablesStat',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `项目物资统计-散装耗材_${Date.now()}.xlsx`);
}
