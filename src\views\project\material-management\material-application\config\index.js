const packageType = {
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '耗材包编号',
      prop: 'consumablesNumber'
    },
    {
      label: '耗材包名称',
      prop: 'consumablesName'
    },
    {
      label: '耗材包照片',
      prop: 'sysFileIds',
      slot: 'sysFileIds'
    },
    {
      label: '备注',
      prop: 'remark'
    }
  ],
  items: [
    {
      type: 'input',
      label: '耗材包名称',
      prop: 'consumablesName'
    }
  ]
};
const bulkpackageType = {
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      label: '耗材编号',
      width: 100,
      align: 'center',
      fixed: 'left'
    },

    {
      label: '耗材名称',
      prop: 'consumableName'
    },
    {
      label: '规格/尺寸',
      prop: 'consumableSize'
    },
    {
      label: '颜色材质',
      prop: 'consumableMaterial'
    },
    {
      label: '品牌',
      prop: 'consumableBrand'
    },
    {
      label: '备注',
      prop: 'nickname'
    }
  ],
  items: [
    {
      type: 'input',
      label: '耗材名称',
      prop: 'consumableName'
    }
  ]
};
const reagentType = {
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '试剂编号',
      prop: 'bulkReagentNumber',
      align: 'center'
    },
    {
      label: '试剂名称',
      prop: 'reagentName',
      align: 'center'
    },
    {
      label: '保存条件',
      prop: 'storageCondition',
      align: 'center'
    },
    {
      label: '备注',
      prop: 'remark',
      align: 'center'
    }
  ],
  items: [
    {
      type: 'input',
      label: '试剂名称',
      prop: 'reagentName'
    }
  ]
};
export default {
  packageType,
  bulkpackageType,
  reagentType
};
