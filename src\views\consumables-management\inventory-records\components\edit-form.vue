<!-- 添加和修改的表单 -->
<template>
  <pro-form
    ref="formRef"
    :model="form"
    :items="items"
    :footer="true"
    :label-width="100"
    :grid="{ span: 24 }"
    @updateValue="setFieldValue"
    :footerStyle="{ justifyContent: 'flex-end' }"
  >
    <template #RoleSelect>
      <role-select v-model="form.roles" />
    </template>
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </pro-form>
</template>

<script setup>
  import ProForm from '@/components/ProForm/index.vue';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getConsumableList,
    addInventoryRecord
  } from '@/api/consumables-management/inventory-records';
  import { getBatchNumberList } from '@/api/consumables-management/inventory-query';

  const emit = defineEmits(['close', 'success']);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    type: '1',
    consumableType: '',
    consumableId: '',
    batchNumber: '',
    endDate: '',
    quantity: null,
    remark: ''
  });

  /** 表单项 */
  const items = ref([
    {
      label: '出入库类型',
      prop: 'type',
      type: 'select',
      options: [
        { label: '入库', value: '1' },
        { label: '出库', value: '2' }
      ],
      required: true
    },
    {
      label: '耗材类型',
      prop: 'consumableType',
      type: 'dictSelect',
      props: { code: 'consumable_type' },
      required: true
    },
    {
      label: '耗材名称',
      prop: 'consumableId',
      type: 'select',
      options: [],
      required: true
    },
    {
      label: '批号',
      prop: 'batchNumber',
      type: 'select', // 自定义组件
      options: [],
      props: {
        filterable: true,
        allowCreate: true
      },
      required: true
    },
    {
      label: '有效到期日',
      prop: 'endDate',
      type: 'date',
      props: { valueFormat: 'YYYY-MM-DD', disabled: false },
      required: true
    },
    {
      label: '入库数量',
      prop: 'quantity',
      type: 'inputNumber',
      props: {
        max: 10000,
        min: 1
      },
      required: true
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea'
    }
  ]);

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      addInventoryRecord(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          emit('success');
          onClose();
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 关闭当前页面并跳转到列表页面 */
  const onClose = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    emit('close');
  };

  //获取耗材项
  const consumableList = ref([]);
  const getConsumableListFun = async () => {
    const res = await getConsumableList({
      consumableType: form.consumableType
    });
    if (res.data.length !== 0) {
      consumableList.value = res.data.map((el) => {
        return {
          label: `${el.systemId}/${el.consumableName}/${el.consumableSize}/${el.consumableMaterial}/${el.consumableBrand}`,
          value: el.consumableId
        };
      });
    }
    setPropItem('consumableId', consumableList.value);
  };
  const batchNumberList = ref([]);
  //获取批号列表
  const getBatchNumberListFun = async () => {
    const res = await getBatchNumberList({
      consumableId: form.consumableId
    });

    if (res.data.length !== 0) {
      batchNumberList.value = res.data.map((el) => {
        return {
          label: el.batchNumber,
          value: el.batchNumber,
          endDate: el.endDate,
          quantity: el.quantity
        };
      });
    }
    setPropItem('batchNumber', batchNumberList.value);
  };
  const setPropItem = (key, list) => {
    items.value.forEach((el) => {
      if (el.prop == key) {
        el.options = list;
      }
    });
  };

  watch(
    () => form.type,
    (val) => {
      if (!val) return;
      items.value.forEach((el) => {
        if (el.prop == 'quantity') {
          el.label = val == '2' ? '出库数量' : '入库数量';
        }
        if (el.prop == 'endDate') {
          el.props.disabled = val == '2' ? true : false;
        }
      });
    }
  );
  watch(
    () => form.consumableType,
    (val) => {
      if (!val) return;
      getConsumableListFun();
    }
  );
  watch(
    () => form.consumableId,
    (val) => {
      if (!val) return;
      getBatchNumberListFun();
    }
  );

  watch(
    () => form.batchNumber,
    (val) => {
      if (!val) return;
      const list = batchNumberList.value.filter(
        (el) => el.value == form.batchNumber
      );
      if (list.length == 0) {
        form.endDate = '';
        form.quantity = null;
        const obj = items.value.filter((el) => el.prop == 'endDate')[0];
        obj.props.disabled = false;
      } else {
        form.endDate = list[0].endDate;
        form.quantity = list[0].quantity;
        items.value.forEach((el) => {
          if (el.prop == 'quantity' && form.type == '2') {
            el.props.max = list[0].quantity;
          }
          if (el.prop == 'endDate' && form.type == '1') {
            el.props.disabled = true;
          }
        });
      }
    }
  );
</script>
