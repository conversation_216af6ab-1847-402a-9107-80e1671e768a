<template>
  <div style="text-align: right">
    <el-button
      v-permission="'project:bulkReagent:add'"
      type="primary"
      @click="openEdit()"
      >新增试剂项</el-button
    >
    <ele-pro-table
      ref="tableRef"
      row-key="userId"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      :sticky="!fixedHeight"
      cache-key="package-table"
      :border="false"
      :toolbar="{ theme: toolDefault ? 'default' : 'plain' }"
      :tools="false"
      :footer-style="{ paddingBottom: '12px' }"
      :pagination="false"
      style="padding-bottom: 0; margin-top: -5px"
    >
      <template #sysFileIds="{ row }">
        <el-link
          v-if="row?.sysFileIds"
          type="primary"
          underline="never"
          @click="lookImg(row)"
          >照片</el-link
        >
      </template>
      <template #action="{ row }">
        <el-link
          v-permission="'project:bulkReagent:edit'"
          type="primary"
          underline="never"
          :disabled="row.updateFlag == '0'"
          @click.stop="openEdit(row)"
        >
          编辑
        </el-link>
        <el-divider
          v-permission="[
            'project:bulkReagent:edit',
            'project:bulkReagent:remove'
          ]"
          direction="vertical"
        />
        <el-link
          v-permission="'project:bulkReagent:remove'"
          type="danger"
          underline="never"
          :disabled="row.updateFlag == '0'"
          @click.stop="remove(row)"
        >
          删除
        </el-link>
      </template>
    </ele-pro-table>
    <reagentDialog
      v-model="visibleModal"
      :handle="handle"
      :editData="editData"
      @success="onSearch"
    />
  </div>
</template>
<script setup>
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus';
  import reagentDialog from '../handleModal/reagentDialog.vue';
  import {
    getBulkReagentList,
    bulkReagentRemove
  } from '@/api/project/project-configuration/material-allocation';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();

  // 研究信息列表配置
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      label: '试剂编号',
      prop: 'bulkReagentNumber',
      width: 120
    },
    {
      label: '试剂名称',
      prop: 'reagentName'
    },
    {
      label: '保存条件',
      prop: 'storageCondition'
    },
    {
      label: '照片',
      prop: 'sysFileIds',
      slot: 'sysFileIds',
      width: 120
    },
    {
      label: '备注',
      prop: 'remark'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      showOverflowTooltip: false,
      resizable: false,
      slot: 'action',
      fixed: 'right',
      disabledInSetting: true,
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 表头工具栏风格 */
  const toolDefault = ref(false);

  /** 表格固定高度 */
  const fixedHeight = ref(true);

  /** 表格数据源 */
  const datasource = async ({ pages, where, orders, filters }) => {
    const projectId = userStore.projectId;
    const res = await getBulkReagentList({
      ...where,
      ...orders,
      ...filters,
      projectId
    });
    return {
      records: res.data
    };
  };
  //新增
  /** 新增编辑弹窗是否显示 */
  const visibleModal = ref(false);
  const editData = ref({});
  const handle = ref('add');
  //新增编辑
  const openEdit = (row) => {
    handle.value = row ? 'edit' : 'add';
    visibleModal.value = true;
    editData.value = row ?? null;
  };
  // 删除
  const remove = (row) => {
    ElMessageBox.confirm(`确定要删除该散装试剂吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        bulkReagentRemove(row.reagentId)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            onSearch();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  const tableRef = ref(null);
  const onSearch = () => {
    tableRef.value?.reload?.();
  };

  const lookImg = (row) => {
    const base64Url = btoa(row.sysOssVoList[0].url);
    const url = `https://kkfile.520gcp.com/onlinePreview?url=${base64Url}`;
    window.open(url);
  };
</script>
