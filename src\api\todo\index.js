import request from '@/utils/request';

//查询系统消息列表
export async function getProblemList(params) {
  const res = await request.get('/system/message/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//获取系统消息详细信息
export async function getProblemDetail(messId) {
  const res = await request.get(`/system/message/${messId}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

//获取待办事项列表
export async function getTodoList(params) {
  const res = await request.get('/system/todo/list', { params });
  console.log('获取待办事项列表====>', res);

  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
