<template>
  <div>
    <ele-modal
      form
      :width="600"
      title="新增出入库"
      v-model="visible"
      :move-out="moveOut"
      :resizable="modalResizable"
      :maxable="maxable"
      :inner="inner"
      :reset-on-close="resetOnClose"
      :position="position"
      :append-to-body="true"
      :z-index="2001"
      @closed="cancelDialog"
    >
      <edit-form @close="visible = false" @success="success" />
    </ele-modal>
  </div>
</template>

<script lang="ts" setup>
  import EditForm from '../components/edit-form.vue';
  import { ref } from 'vue';
  const emit = defineEmits(['success']);

  const visible = defineModel({ type: Boolean, default: false });

  const moveOut = ref(false);
  const modalResizable = ref(true);
  const maxable = ref(false);
  const inner = ref(false);
  const resetOnClose = ref(true);
  const position = ref('center');
  const cancelDialog = () => {
    visible.value = false;
  };
  const success = () => {
    emit('success');
  };
</script>
